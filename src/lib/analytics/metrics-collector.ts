import { prisma } from '@/lib/database/prisma';

export interface QueryMetrics {
  id: string;
  userId: string;
  connectionId: string;
  queryHash: string;
  queryType: 'SELECT' | 'INSERT' | 'UPDATE' | 'DELETE' | 'CREATE' | 'ALTER' | 'DROP' | 'UNKNOWN';
  executionTime: number;
  rowsAffected: number;
  rowsReturned: number;
  success: boolean;
  errorMessage?: string;
  complexity: 'LOW' | 'MEDIUM' | 'HIGH';
  timestamp: Date;
  sqlLength: number;
  hasJoins: boolean;
  hasSubqueries: boolean;
  hasAggregations: boolean;
  tablesAccessed: string[];
}

export interface UsageMetrics {
  userId: string;
  date: Date;
  queriesExecuted: number;
  successfulQueries: number;
  failedQueries: number;
  totalExecutionTime: number;
  avgExecutionTime: number;
  connectionsUsed: number;
  collaborationSessions: number;
  chatMessages: number;
}

export interface PerformanceAlert {
  id: string;
  type: 'SLOW_QUERY' | 'HIGH_ERROR_RATE' | 'RESOURCE_USAGE' | 'SECURITY_CONCERN';
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  title: string;
  description: string;
  queryHash?: string;
  userId?: string;
  connectionId?: string;
  threshold: number;
  actualValue: number;
  timestamp: Date;
  resolved: boolean;
}

export class MetricsCollector {
  // Collect query execution metrics
  static async collectQueryMetrics(data: {
    userId: string;
    connectionId: string;
    sql: string;
    result: any;
    executionTime: number;
  }): Promise<void> {
    try {
      const queryAnalysis = this.analyzeSQLQuery(data.sql);
      const queryHash = this.generateQueryHash(data.sql);

      const metrics: Omit<QueryMetrics, 'id'> = {
        userId: data.userId,
        connectionId: data.connectionId,
        queryHash,
        queryType: queryAnalysis.type,
        executionTime: data.executionTime,
        rowsAffected: data.result.data?.affectedRows || 0,
        rowsReturned: data.result.data?.rowCount || 0,
        success: data.result.success,
        errorMessage: data.result.error,
        complexity: queryAnalysis.complexity,
        timestamp: new Date(),
        sqlLength: data.sql.length,
        hasJoins: queryAnalysis.hasJoins,
        hasSubqueries: queryAnalysis.hasSubqueries,
        hasAggregations: queryAnalysis.hasAggregations,
        tablesAccessed: queryAnalysis.tablesAccessed,
      };

      // Store in database (you might want to create a separate metrics table)
      await this.storeQueryMetrics(metrics);

      // Check for performance alerts
      await this.checkPerformanceAlerts(metrics);

    } catch (error) {
      console.error('Failed to collect query metrics:', error);
    }
  }

  // Collect daily usage metrics
  static async collectUsageMetrics(userId: string, date: Date): Promise<UsageMetrics> {
    try {
      const startOfDay = new Date(date);
      startOfDay.setHours(0, 0, 0, 0);
      
      const endOfDay = new Date(date);
      endOfDay.setHours(23, 59, 59, 999);

      // Get query statistics for the day
      const queryStats = await this.getQueryStatsForPeriod(userId, startOfDay, endOfDay);
      
      // Get collaboration statistics
      const collaborationStats = await this.getCollaborationStatsForPeriod(userId, startOfDay, endOfDay);

      const usageMetrics: UsageMetrics = {
        userId,
        date,
        queriesExecuted: queryStats.total,
        successfulQueries: queryStats.successful,
        failedQueries: queryStats.failed,
        totalExecutionTime: queryStats.totalExecutionTime,
        avgExecutionTime: queryStats.avgExecutionTime,
        connectionsUsed: queryStats.uniqueConnections,
        collaborationSessions: collaborationStats.sessions,
        chatMessages: collaborationStats.messages,
      };

      await this.storeUsageMetrics(usageMetrics);
      return usageMetrics;

    } catch (error) {
      console.error('Failed to collect usage metrics:', error);
      throw error;
    }
  }

  // Analyze SQL query for metrics
  private static analyzeSQLQuery(sql: string): {
    type: QueryMetrics['queryType'];
    complexity: QueryMetrics['complexity'];
    hasJoins: boolean;
    hasSubqueries: boolean;
    hasAggregations: boolean;
    tablesAccessed: string[];
  } {
    const lowerSql = sql.toLowerCase().trim();
    
    // Determine query type
    let type: QueryMetrics['queryType'] = 'UNKNOWN';
    if (lowerSql.startsWith('select')) type = 'SELECT';
    else if (lowerSql.startsWith('insert')) type = 'INSERT';
    else if (lowerSql.startsWith('update')) type = 'UPDATE';
    else if (lowerSql.startsWith('delete')) type = 'DELETE';
    else if (lowerSql.startsWith('create')) type = 'CREATE';
    else if (lowerSql.startsWith('alter')) type = 'ALTER';
    else if (lowerSql.startsWith('drop')) type = 'DROP';

    // Analyze complexity indicators
    const hasJoins = /\b(join|inner join|left join|right join|full join)\b/i.test(sql);
    const hasSubqueries = /\b(select\b.*\bselect\b|\bexists\b|\bin\s*\()/i.test(sql);
    const hasAggregations = /\b(count|sum|avg|min|max|group by|having)\b/i.test(sql);
    const hasWindow = /\bover\s*\(/i.test(sql);
    const hasUnion = /\bunion\b/i.test(sql);

    // Calculate complexity
    let complexityScore = 0;
    if (hasJoins) complexityScore += 1;
    if (hasSubqueries) complexityScore += 2;
    if (hasAggregations) complexityScore += 1;
    if (hasWindow) complexityScore += 2;
    if (hasUnion) complexityScore += 1;

    let complexity: QueryMetrics['complexity'] = 'LOW';
    if (complexityScore >= 4) complexity = 'HIGH';
    else if (complexityScore >= 2) complexity = 'MEDIUM';

    // Extract table names (simplified)
    const tableMatches = sql.match(/(?:from|join|update|into)\s+([a-zA-Z_][a-zA-Z0-9_]*)/gi);
    const tablesAccessed = tableMatches
      ? Array.from(new Set(tableMatches.map(match => match.split(/\s+/).pop()?.toLowerCase() || '')))
      : [];

    return {
      type,
      complexity,
      hasJoins,
      hasSubqueries,
      hasAggregations,
      tablesAccessed,
    };
  }

  // Generate consistent hash for SQL queries
  private static generateQueryHash(sql: string): string {
    // Normalize SQL for consistent hashing
    const normalized = sql
      .toLowerCase()
      .replace(/\s+/g, ' ')
      .replace(/'/g, '"')
      .trim();

    // Simple hash function
    let hash = 0;
    for (let i = 0; i < normalized.length; i++) {
      const char = normalized.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    return Math.abs(hash).toString(16);
  }

  // Store query metrics (implement based on your storage strategy)
  private static async storeQueryMetrics(metrics: Omit<QueryMetrics, 'id'>): Promise<void> {
    // For now, we'll store in the existing GeneratedQuery table with additional metadata
    // In a production system, you might want a dedicated metrics table
    try {
      await prisma.generatedQuery.create({
        data: {
          sessionId: 'metrics-session', // Placeholder
          userId: metrics.userId,
          userInput: `Query executed at ${metrics.timestamp.toISOString()}`,
          generatedSQL: '', // We don't store the actual SQL for privacy
          explanation: `Query metrics: ${metrics.queryType}, ${metrics.complexity} complexity`,
          databaseType: 'POSTGRESQL', // Default
          executionTime: metrics.executionTime,
          rowsAffected: metrics.rowsAffected,
          performanceData: JSON.stringify({
            queryHash: metrics.queryHash,
            queryType: metrics.queryType,
            complexity: metrics.complexity,
            rowsReturned: metrics.rowsReturned,
            success: metrics.success,
            sqlLength: metrics.sqlLength,
            hasJoins: metrics.hasJoins,
            hasSubqueries: metrics.hasSubqueries,
            hasAggregations: metrics.hasAggregations,
            tablesAccessed: metrics.tablesAccessed,
            timestamp: metrics.timestamp.toISOString(),
          }),
        },
      });
    } catch (error) {
      console.error('Failed to store query metrics:', error);
    }
  }

  // Store usage metrics
  private static async storeUsageMetrics(metrics: UsageMetrics): Promise<void> {
    // Store in a JSON field or separate table
    // For now, we'll use the user's profile or a separate storage mechanism
    console.log('Usage metrics collected:', metrics);
  }

  // Get query statistics for a period
  private static async getQueryStatsForPeriod(
    userId: string, 
    startDate: Date, 
    endDate: Date
  ): Promise<{
    total: number;
    successful: number;
    failed: number;
    totalExecutionTime: number;
    avgExecutionTime: number;
    uniqueConnections: number;
  }> {
    try {
      const queries = await prisma.generatedQuery.findMany({
        where: {
          userId,
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
        select: {
          executionTime: true,
          performanceData: true,
        },
      });

      const total = queries.length;
      const successful = queries.filter(q => {
        if (!q.performanceData || typeof q.performanceData !== 'string') return false;
        try {
          const data = JSON.parse(q.performanceData);
          return data.success === true;
        } catch {
          return false;
        }
      }).length;
      const failed = total - successful;
      const totalExecutionTime = queries.reduce((sum, q) => sum + (q.executionTime || 0), 0);
      const avgExecutionTime = total > 0 ? totalExecutionTime / total : 0;
      
      // Count unique connections (simplified)
      const uniqueConnections = 1; // Placeholder

      return {
        total,
        successful,
        failed,
        totalExecutionTime,
        avgExecutionTime,
        uniqueConnections,
      };
    } catch (error) {
      console.error('Failed to get query stats:', error);
      return {
        total: 0,
        successful: 0,
        failed: 0,
        totalExecutionTime: 0,
        avgExecutionTime: 0,
        uniqueConnections: 0,
      };
    }
  }

  // Get collaboration statistics
  private static async getCollaborationStatsForPeriod(
    userId: string,
    startDate: Date,
    endDate: Date
  ): Promise<{
    sessions: number;
    messages: number;
  }> {
    // Placeholder - implement based on your collaboration data storage
    return {
      sessions: 0,
      messages: 0,
    };
  }

  // Check for performance alerts
  private static async checkPerformanceAlerts(metrics: Omit<QueryMetrics, 'id'>): Promise<void> {
    const alerts: Omit<PerformanceAlert, 'id'>[] = [];

    // Slow query alert
    if (metrics.executionTime > 5000) { // 5 seconds
      alerts.push({
        type: 'SLOW_QUERY',
        severity: metrics.executionTime > 30000 ? 'CRITICAL' : 'HIGH',
        title: 'Slow Query Detected',
        description: `Query took ${metrics.executionTime}ms to execute`,
        queryHash: metrics.queryHash,
        userId: metrics.userId,
        connectionId: metrics.connectionId,
        threshold: 5000,
        actualValue: metrics.executionTime,
        timestamp: new Date(),
        resolved: false,
      });
    }

    // High complexity query
    if (metrics.complexity === 'HIGH' && metrics.executionTime > 1000) {
      alerts.push({
        type: 'RESOURCE_USAGE',
        severity: 'MEDIUM',
        title: 'Complex Query Performance',
        description: 'High complexity query with significant execution time',
        queryHash: metrics.queryHash,
        userId: metrics.userId,
        connectionId: metrics.connectionId,
        threshold: 1000,
        actualValue: metrics.executionTime,
        timestamp: new Date(),
        resolved: false,
      });
    }

    // Store alerts
    for (const alert of alerts) {
      await this.storeAlert(alert);
    }
  }

  // Store performance alert
  private static async storeAlert(alert: Omit<PerformanceAlert, 'id'>): Promise<void> {
    console.log('Performance alert:', alert);
    // Implement alert storage and notification system
  }

  // Public methods for retrieving analytics data
  static async getQueryPerformanceStats(
    userId: string,
    days: number = 7
  ): Promise<{
    avgExecutionTime: number;
    slowQueries: number;
    totalQueries: number;
    errorRate: number;
  }> {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const stats = await this.getQueryStatsForPeriod(userId, startDate, endDate);
    
    return {
      avgExecutionTime: stats.avgExecutionTime,
      slowQueries: 0, // Implement based on your criteria
      totalQueries: stats.total,
      errorRate: stats.total > 0 ? (stats.failed / stats.total) * 100 : 0,
    };
  }

  static async getTopSlowQueries(
    userId: string,
    limit: number = 10
  ): Promise<Array<{
    queryHash: string;
    avgExecutionTime: number;
    executionCount: number;
    lastExecuted: Date;
  }>> {
    // Implement based on your metrics storage
    return [];
  }

  static async getUsageTrends(
    userId: string,
    days: number = 30
  ): Promise<Array<{
    date: string;
    queries: number;
    executionTime: number;
    errors: number;
  }>> {
    // Implement based on your metrics storage
    return [];
  }
}
