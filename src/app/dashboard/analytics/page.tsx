'use client';

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { AnalyticsDashboard } from '@/components/analytics/dashboard';
import { QueryPerformance } from '@/components/analytics/query-performance';
import { 
  BarChart3, 
  TrendingUp, 
  Zap, 
  AlertTriangle,
  Users,
  Database,
  Clock,
  Activity
} from 'lucide-react';
import { useAnalytics } from '@/hooks/use-analytics';

export default function AnalyticsPage() {
  const [activeTab, setActiveTab] = useState<'overview' | 'performance' | 'usage' | 'alerts'>('overview');
  
  // Mock user ID - in a real app, get from auth context
  const userId = 'user_demo_123';
  
  const analytics = useAnalytics({ 
    userId, 
    autoRefresh: true, 
    refreshInterval: 60000 // Refresh every minute
  });

  const tabs = [
    { 
      id: 'overview', 
      label: 'Overview', 
      icon: BarChart3,
      description: 'General analytics and key metrics'
    },
    { 
      id: 'performance', 
      label: 'Performance', 
      icon: Zap,
      description: 'Query performance analysis and optimization'
    },
    { 
      id: 'usage', 
      label: 'Usage Patterns', 
      icon: TrendingUp,
      description: 'Usage trends and patterns over time'
    },
    { 
      id: 'alerts', 
      label: 'Alerts', 
      icon: AlertTriangle,
      badge: analytics.alertCount > 0 ? analytics.alertCount : undefined,
      description: 'Performance alerts and recommendations'
    },
  ];

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Analytics</h1>
          <p className="text-muted-foreground">
            Monitor query performance, usage patterns, and system health
          </p>
        </div>
        <div className="flex items-center gap-4">
          {analytics.lastUpdated && (
            <div className="text-sm text-muted-foreground">
              Last updated: {analytics.lastUpdated.toLocaleTimeString()}
            </div>
          )}
          <Button 
            onClick={() => analytics.loadAllAnalytics()} 
            disabled={analytics.isLoading}
            variant="outline"
          >
            {analytics.isLoading ? 'Refreshing...' : 'Refresh'}
          </Button>
        </div>
      </div>

      {/* Quick Stats */}
      {analytics.hasData && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Queries</CardTitle>
              <Database className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{analytics.totalQueries}</div>
              <p className="text-xs text-muted-foreground">
                Executed in the last 7 days
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg Response Time</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {analytics.avgExecutionTime < 1000 
                  ? `${Math.round(analytics.avgExecutionTime)}ms`
                  : `${(analytics.avgExecutionTime / 1000).toFixed(2)}s`
                }
              </div>
              <p className="text-xs text-muted-foreground">
                Average execution time
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Error Rate</CardTitle>
              <AlertTriangle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{analytics.errorRate.toFixed(1)}%</div>
              <p className="text-xs text-muted-foreground">
                Failed queries
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Performance Score</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{analytics.performanceScore}</div>
              <Badge 
                variant={
                  analytics.performanceScore >= 80 ? 'default' : 
                  analytics.performanceScore >= 60 ? 'secondary' : 'destructive'
                }
                className="text-xs"
              >
                {analytics.performanceScore >= 80 ? 'Excellent' : 
                 analytics.performanceScore >= 60 ? 'Good' : 'Needs Improvement'}
              </Badge>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Error State */}
      {analytics.error && (
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2 text-red-600">
              <AlertTriangle className="h-5 w-5" />
              <span className="font-medium">Error loading analytics data</span>
            </div>
            <p className="text-sm text-muted-foreground mt-1">{analytics.error}</p>
            <Button 
              onClick={() => analytics.loadAllAnalytics()} 
              className="mt-4"
              variant="outline"
            >
              Retry
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Tabs */}
      <div className="flex space-x-1 bg-muted p-1 rounded-lg">
        {tabs.map((tab) => {
          const Icon = tab.icon;
          return (
            <Button
              key={tab.id}
              variant={activeTab === tab.id ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setActiveTab(tab.id as any)}
              className="flex items-center gap-2"
            >
              <Icon className="h-4 w-4" />
              {tab.label}
              {tab.badge && (
                <Badge variant="secondary" className="text-xs">
                  {tab.badge}
                </Badge>
              )}
            </Button>
          );
        })}
      </div>

      {/* Tab Content */}
      <div className="space-y-6">
        {activeTab === 'overview' && (
          <AnalyticsDashboard userId={userId} />
        )}

        {activeTab === 'performance' && (
          <QueryPerformance userId={userId} />
        )}

        {activeTab === 'usage' && (
          <Card>
            <CardHeader>
              <CardTitle>Usage Patterns</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <TrendingUp className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">Usage Analytics Coming Soon</h3>
                <p className="text-muted-foreground">
                  Detailed usage patterns, user behavior analysis, and feature adoption metrics.
                </p>
              </div>
            </CardContent>
          </Card>
        )}

        {activeTab === 'alerts' && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5" />
                Performance Alerts
                {analytics.alertCount > 0 && (
                  <Badge variant="destructive">{analytics.alertCount}</Badge>
                )}
              </CardTitle>
            </CardHeader>
            <CardContent>
              {analytics.activeAlerts.length === 0 ? (
                <div className="text-center py-8">
                  <AlertTriangle className="h-12 w-12 text-green-500 mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">No Active Alerts</h3>
                  <p className="text-muted-foreground">
                    Your system is performing well. No performance issues detected.
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {analytics.activeAlerts.map((alert) => (
                    <div key={alert.id} className="border rounded-lg p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex items-start gap-3">
                          <AlertTriangle className="h-5 w-5 text-orange-500 mt-0.5" />
                          <div>
                            <div className="flex items-center gap-2">
                              <h4 className="font-medium">{alert.title}</h4>
                              <Badge variant="outline">{alert.severity}</Badge>
                            </div>
                            <p className="text-sm text-muted-foreground mt-1">
                              {alert.description}
                            </p>
                            <p className="text-xs text-muted-foreground mt-2">
                              {new Date(alert.timestamp).toLocaleString()}
                            </p>
                          </div>
                        </div>
                        <Button variant="outline" size="sm">
                          Resolve
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        )}
      </div>

      {/* Loading State */}
      {analytics.isLoading && !analytics.hasData && (
        <Card>
          <CardContent className="p-8">
            <div className="flex items-center justify-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              <span className="ml-2">Loading analytics data...</span>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
