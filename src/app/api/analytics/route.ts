import { NextRequest, NextResponse } from 'next/server';
import { MetricsCollector } from '@/lib/analytics/metrics-collector';
import { z } from 'zod';

// Validation schemas
const analyticsQuerySchema = z.object({
  userId: z.string().cuid(),
  type: z.enum(['performance', 'usage', 'slow-queries', 'trends', 'alerts']),
  days: z.number().int().min(1).max(365).optional().default(7),
  limit: z.number().int().min(1).max(100).optional().default(10),
});

// GET /api/analytics - Get analytics data
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const type = searchParams.get('type');
    const days = parseInt(searchParams.get('days') || '7');
    const limit = parseInt(searchParams.get('limit') || '10');

    if (!userId || !type) {
      return NextResponse.json(
        { error: 'userId and type parameters are required' },
        { status: 400 }
      );
    }

    const validatedData = analyticsQuerySchema.parse({ userId, type, days, limit });

    let data;

    switch (validatedData.type) {
      case 'performance':
        data = await MetricsCollector.getQueryPerformanceStats(
          validatedData.userId,
          validatedData.days
        );
        break;

      case 'slow-queries':
        data = await MetricsCollector.getTopSlowQueries(
          validatedData.userId,
          validatedData.limit
        );
        break;

      case 'trends':
        data = await MetricsCollector.getUsageTrends(
          validatedData.userId,
          validatedData.days
        );
        break;

      case 'usage':
        data = await getUsageOverview(validatedData.userId, validatedData.days);
        break;

      case 'alerts':
        data = await getActiveAlerts(validatedData.userId);
        break;

      default:
        return NextResponse.json(
          { error: 'Invalid analytics type' },
          { status: 400 }
        );
    }

    return NextResponse.json({
      type: validatedData.type,
      data,
      period: {
        days: validatedData.days,
        limit: validatedData.limit,
      },
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      );
    }

    console.error('GET /api/analytics error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/analytics - Record analytics event
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    const recordEventSchema = z.object({
      type: z.enum(['query_execution', 'collaboration_session', 'user_action']),
      userId: z.string().cuid(),
      data: z.object({}).passthrough(),
    });

    const validatedData = recordEventSchema.parse(body);

    switch (validatedData.type) {
      case 'query_execution':
        await handleQueryExecutionEvent(validatedData.userId, validatedData.data);
        break;

      case 'collaboration_session':
        await handleCollaborationEvent(validatedData.userId, validatedData.data);
        break;

      case 'user_action':
        await handleUserActionEvent(validatedData.userId, validatedData.data);
        break;
    }

    return NextResponse.json({
      success: true,
      message: 'Analytics event recorded',
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      );
    }

    console.error('POST /api/analytics error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Helper functions
async function getUsageOverview(userId: string, days: number) {
  try {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    // Get basic usage metrics
    const performanceStats = await MetricsCollector.getQueryPerformanceStats(userId, days);
    
    // Calculate additional metrics
    const overview = {
      totalQueries: performanceStats.totalQueries,
      avgExecutionTime: performanceStats.avgExecutionTime,
      errorRate: performanceStats.errorRate,
      slowQueries: performanceStats.slowQueries,
      
      // Additional calculated metrics
      queriesPerDay: performanceStats.totalQueries / days,
      performanceScore: calculatePerformanceScore(performanceStats),
      
      // Period info
      period: {
        start: startDate.toISOString(),
        end: endDate.toISOString(),
        days,
      },
    };

    return overview;
  } catch (error) {
    console.error('Failed to get usage overview:', error);
    return {
      totalQueries: 0,
      avgExecutionTime: 0,
      errorRate: 0,
      slowQueries: 0,
      queriesPerDay: 0,
      performanceScore: 0,
      period: {
        start: new Date().toISOString(),
        end: new Date().toISOString(),
        days,
      },
    };
  }
}

async function getActiveAlerts(userId: string) {
  // Placeholder for alerts - implement based on your alert storage
  return [
    {
      id: 'alert-1',
      type: 'SLOW_QUERY',
      severity: 'HIGH',
      title: 'Slow Query Detected',
      description: 'Query execution time exceeded 5 seconds',
      timestamp: new Date().toISOString(),
      resolved: false,
    },
  ];
}

async function handleQueryExecutionEvent(userId: string, data: any) {
  try {
    const queryEventSchema = z.object({
      connectionId: z.string(),
      sql: z.string(),
      result: z.object({}).passthrough(),
      executionTime: z.number(),
    });

    const validatedData = queryEventSchema.parse(data);
    
    await MetricsCollector.collectQueryMetrics({
      userId,
      connectionId: validatedData.connectionId,
      sql: validatedData.sql,
      result: validatedData.result,
      executionTime: validatedData.executionTime,
    });

  } catch (error) {
    console.error('Failed to handle query execution event:', error);
  }
}

async function handleCollaborationEvent(userId: string, data: any) {
  try {
    // Record collaboration metrics
    console.log('Collaboration event:', { userId, data });
    
    // Implement collaboration analytics
    // - Session duration
    // - Number of participants
    // - Messages sent
    // - Queries shared
    
  } catch (error) {
    console.error('Failed to handle collaboration event:', error);
  }
}

async function handleUserActionEvent(userId: string, data: any) {
  try {
    // Record user action metrics
    console.log('User action event:', { userId, data });
    
    // Implement user action analytics
    // - Feature usage
    // - Navigation patterns
    // - Time spent in different sections
    
  } catch (error) {
    console.error('Failed to handle user action event:', error);
  }
}

function calculatePerformanceScore(stats: {
  avgExecutionTime: number;
  errorRate: number;
  slowQueries: number;
  totalQueries: number;
}): number {
  // Calculate a performance score from 0-100
  let score = 100;
  
  // Penalize for slow average execution time
  if (stats.avgExecutionTime > 1000) {
    score -= Math.min(30, (stats.avgExecutionTime - 1000) / 100);
  }
  
  // Penalize for high error rate
  score -= stats.errorRate * 2;
  
  // Penalize for slow queries
  if (stats.totalQueries > 0) {
    const slowQueryRatio = stats.slowQueries / stats.totalQueries;
    score -= slowQueryRatio * 20;
  }
  
  return Math.max(0, Math.round(score));
}
