'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useAnalyticsTracking } from '@/hooks/use-analytics';
import { 
  Play, 
  Square, 
  CheckCircle, 
  XCircle, 
  Clock, 
  Database,
  AlertTriangle,
  Info
} from 'lucide-react';

interface QueryExecutorProps {
  sql: string;
  connectionId: string | null;
  onExecutionComplete?: (result: QueryExecutionResult) => void;
}

interface QueryExecutionResult {
  success: boolean;
  data?: {
    rows: any[];
    fields: Array<{
      name: string;
      type: string;
      nullable: boolean;
    }>;
    rowCount: number;
    executionTime: number;
    affectedRows?: number;
  };
  error?: string;
  warnings?: string[];
  metadata: {
    connectionId: string;
    executedAt: string;
    executionTime: number;
    rowsReturned: number;
    queryHash: string;
  };
}

interface QueryValidation {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  queryType: string;
  isReadOnly: boolean;
  estimatedComplexity: 'LOW' | 'MEDIUM' | 'HIGH';
}

export function QueryExecutor({ sql, connectionId, onExecutionComplete }: QueryExecutorProps) {
  const [isExecuting, setIsExecuting] = useState(false);
  const [result, setResult] = useState<QueryExecutionResult | null>(null);
  const [validation, setValidation] = useState<QueryValidation | null>(null);
  const [isValidating, setIsValidating] = useState(false);

  // Analytics tracking - using demo user ID
  const analytics = useAnalyticsTracking('user_demo_123');

  const executeQuery = async () => {
    if (!connectionId || !sql.trim()) {
      return;
    }

    setIsExecuting(true);
    setResult(null);

    try {
      const response = await fetch('/api/queries/execute', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          connectionId,
          sql: sql.trim(),
          maxRows: 1000,
          timeout: 30000,
          saveToHistory: true,
        }),
      });

      const executionResult = await response.json();
      setResult(executionResult);
      onExecutionComplete?.(executionResult);

      // Track query execution for analytics
      if (connectionId) {
        analytics.trackQueryExecution({
          connectionId,
          sql: sql.trim(),
          result: executionResult,
          executionTime: executionResult.metadata?.executionTime || 0,
        });
      }
    } catch (error) {
      const errorResult: QueryExecutionResult = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        metadata: {
          connectionId,
          executedAt: new Date().toISOString(),
          executionTime: 0,
          rowsReturned: 0,
          queryHash: '',
        },
      };
      setResult(errorResult);
      onExecutionComplete?.(errorResult);

      // Track failed query execution for analytics
      if (connectionId) {
        analytics.trackQueryExecution({
          connectionId,
          sql: sql.trim(),
          result: errorResult,
          executionTime: 0,
        });
      }
    } finally {
      setIsExecuting(false);
    }
  };

  const validateQuery = async () => {
    if (!connectionId || !sql.trim()) {
      return;
    }

    setIsValidating(true);
    setValidation(null);

    // Track validation usage
    analytics.trackFeatureUsage('query-validation', 'validate_query');

    try {
      const response = await fetch(
        `/api/queries/execute/validate?connectionId=${connectionId}&sql=${encodeURIComponent(sql.trim())}`
      );

      if (response.ok) {
        const data = await response.json();
        setValidation(data.validation);
      }
    } catch (error) {
      console.error('Failed to validate query:', error);
    } finally {
      setIsValidating(false);
    }
  };

  const getStatusIcon = () => {
    if (isExecuting) {
      return <Clock className="h-4 w-4 animate-spin" />;
    }
    if (result?.success) {
      return <CheckCircle className="h-4 w-4 text-green-500" />;
    }
    if (result && !result.success) {
      return <XCircle className="h-4 w-4 text-red-500" />;
    }
    return <Database className="h-4 w-4" />;
  };

  const getComplexityBadge = (complexity: string) => {
    const variants = {
      LOW: 'default',
      MEDIUM: 'secondary',
      HIGH: 'destructive',
    } as const;

    return (
      <Badge variant={variants[complexity as keyof typeof variants] || 'default'}>
        {complexity} Complexity
      </Badge>
    );
  };

  const canExecute = connectionId && sql.trim() && !isExecuting;

  return (
    <div className="space-y-4">
      {/* Execution Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {getStatusIcon()}
            Query Execution
            {result && (
              <Badge variant={result.success ? 'default' : 'destructive'}>
                {result.success ? 'Success' : 'Failed'}
              </Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Button
              onClick={executeQuery}
              disabled={!canExecute}
              className="flex items-center gap-2"
            >
              {isExecuting ? (
                <>
                  <Square className="h-4 w-4" />
                  Executing...
                </>
              ) : (
                <>
                  <Play className="h-4 w-4" />
                  Execute Query
                </>
              )}
            </Button>

            <Button
              variant="outline"
              onClick={validateQuery}
              disabled={!connectionId || !sql.trim() || isValidating}
              className="flex items-center gap-2"
            >
              {isValidating ? (
                <>
                  <Clock className="h-4 w-4 animate-spin" />
                  Validating...
                </>
              ) : (
                <>
                  <CheckCircle className="h-4 w-4" />
                  Validate
                </>
              )}
            </Button>
          </div>

          {!connectionId && (
            <Alert>
              <Info className="h-4 w-4" />
              <AlertDescription>
                Please select a database connection to execute queries.
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Validation Results */}
      {validation && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4" />
              Query Validation
              {getComplexityBadge(validation.estimatedComplexity)}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex gap-2 flex-wrap">
              <Badge variant="outline">{validation.queryType}</Badge>
              {validation.isReadOnly && <Badge variant="secondary">Read-Only</Badge>}
            </div>

            {validation.errors.length > 0 && (
              <Alert className="border-red-200 bg-red-50">
                <XCircle className="h-4 w-4 text-red-500" />
                <AlertDescription>
                  <div className="font-medium text-red-700 mb-1">Validation Errors:</div>
                  <ul className="list-disc list-inside text-red-600 text-sm">
                    {validation.errors.map((error, index) => (
                      <li key={index}>{error}</li>
                    ))}
                  </ul>
                </AlertDescription>
              </Alert>
            )}

            {validation.warnings.length > 0 && (
              <Alert className="border-yellow-200 bg-yellow-50">
                <AlertTriangle className="h-4 w-4 text-yellow-500" />
                <AlertDescription>
                  <div className="font-medium text-yellow-700 mb-1">Warnings:</div>
                  <ul className="list-disc list-inside text-yellow-600 text-sm">
                    {validation.warnings.map((warning, index) => (
                      <li key={index}>{warning}</li>
                    ))}
                  </ul>
                </AlertDescription>
              </Alert>
            )}

            {validation.isValid && validation.errors.length === 0 && (
              <Alert className="border-green-200 bg-green-50">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <AlertDescription className="text-green-700">
                  Query validation passed. Ready to execute.
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>
      )}

      {/* Execution Results */}
      {result && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {result.success ? (
                <CheckCircle className="h-4 w-4 text-green-500" />
              ) : (
                <XCircle className="h-4 w-4 text-red-500" />
              )}
              Execution Results
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Execution Metadata */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <div className="text-muted-foreground">Execution Time</div>
                <div className="font-medium">{result.metadata.executionTime}ms</div>
              </div>
              <div>
                <div className="text-muted-foreground">Rows Returned</div>
                <div className="font-medium">{result.metadata.rowsReturned}</div>
              </div>
              {result.data?.affectedRows !== undefined && (
                <div>
                  <div className="text-muted-foreground">Rows Affected</div>
                  <div className="font-medium">{result.data.affectedRows}</div>
                </div>
              )}
              <div>
                <div className="text-muted-foreground">Executed At</div>
                <div className="font-medium">
                  {new Date(result.metadata.executedAt).toLocaleTimeString()}
                </div>
              </div>
            </div>

            {/* Error Message */}
            {result.error && (
              <Alert className="border-red-200 bg-red-50">
                <XCircle className="h-4 w-4 text-red-500" />
                <AlertDescription>
                  <div className="font-medium text-red-700 mb-1">Execution Error:</div>
                  <div className="text-red-600 text-sm">{result.error}</div>
                </AlertDescription>
              </Alert>
            )}

            {/* Warnings */}
            {result.warnings && result.warnings.length > 0 && (
              <Alert className="border-yellow-200 bg-yellow-50">
                <AlertTriangle className="h-4 w-4 text-yellow-500" />
                <AlertDescription>
                  <div className="font-medium text-yellow-700 mb-1">Warnings:</div>
                  <ul className="list-disc list-inside text-yellow-600 text-sm">
                    {result.warnings.map((warning, index) => (
                      <li key={index}>{warning}</li>
                    ))}
                  </ul>
                </AlertDescription>
              </Alert>
            )}

            {/* Success Summary */}
            {result.success && result.data && (
              <Alert className="border-green-200 bg-green-50">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <AlertDescription className="text-green-700">
                  Query executed successfully! 
                  {result.data.rowCount > 0 && (
                    <span> Returned {result.data.rowCount} rows.</span>
                  )}
                  {result.data.affectedRows !== undefined && result.data.affectedRows > 0 && (
                    <span> Affected {result.data.affectedRows} rows.</span>
                  )}
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
