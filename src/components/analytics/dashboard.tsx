'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell
} from 'recharts';
import { 
  TrendingUp, 
  TrendingDown, 
  Clock, 
  AlertTriangle, 
  CheckCircle,
  Activity,
  Database,
  Users,
  Zap,
  RefreshCw
} from 'lucide-react';

interface AnalyticsDashboardProps {
  userId: string;
}

interface PerformanceStats {
  avgExecutionTime: number;
  slowQueries: number;
  totalQueries: number;
  errorRate: number;
}

interface UsageOverview {
  totalQueries: number;
  avgExecutionTime: number;
  errorRate: number;
  slowQueries: number;
  queriesPerDay: number;
  performanceScore: number;
  period: {
    start: string;
    end: string;
    days: number;
  };
}

interface TrendData {
  date: string;
  queries: number;
  executionTime: number;
  errors: number;
}

export function AnalyticsDashboard({ userId }: AnalyticsDashboardProps) {
  const [timeRange, setTimeRange] = useState('7');
  const [isLoading, setIsLoading] = useState(true);
  const [usageOverview, setUsageOverview] = useState<UsageOverview | null>(null);
  const [performanceStats, setPerformanceStats] = useState<PerformanceStats | null>(null);
  const [trends, setTrends] = useState<TrendData[]>([]);
  const [slowQueries, setSlowQueries] = useState<any[]>([]);
  const [alerts, setAlerts] = useState<any[]>([]);

  useEffect(() => {
    loadAnalyticsData();
  }, [userId, timeRange]);

  const loadAnalyticsData = async () => {
    setIsLoading(true);
    try {
      const [usageRes, performanceRes, trendsRes, slowQueriesRes, alertsRes] = await Promise.all([
        fetch(`/api/analytics?userId=${userId}&type=usage&days=${timeRange}`),
        fetch(`/api/analytics?userId=${userId}&type=performance&days=${timeRange}`),
        fetch(`/api/analytics?userId=${userId}&type=trends&days=${timeRange}`),
        fetch(`/api/analytics?userId=${userId}&type=slow-queries&limit=5`),
        fetch(`/api/analytics?userId=${userId}&type=alerts`),
      ]);

      const [usage, performance, trendsData, slowQueriesData, alertsData] = await Promise.all([
        usageRes.json(),
        performanceRes.json(),
        trendsRes.json(),
        slowQueriesRes.json(),
        alertsRes.json(),
      ]);

      setUsageOverview(usage.data);
      setPerformanceStats(performance.data);
      setTrends(trendsData.data);
      setSlowQueries(slowQueriesData.data);
      setAlerts(alertsData.data);
    } catch (error) {
      console.error('Failed to load analytics data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getPerformanceScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getPerformanceScoreBadge = (score: number) => {
    if (score >= 80) return 'default';
    if (score >= 60) return 'secondary';
    return 'destructive';
  };

  const formatExecutionTime = (ms: number) => {
    if (ms < 1000) return `${ms}ms`;
    return `${(ms / 1000).toFixed(2)}s`;
  };

  const pieChartData = usageOverview ? [
    { name: 'Successful', value: usageOverview.totalQueries - (usageOverview.totalQueries * usageOverview.errorRate / 100), color: '#10B981' },
    { name: 'Failed', value: usageOverview.totalQueries * usageOverview.errorRate / 100, color: '#EF4444' },
  ] : [];

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center p-8">
          <RefreshCw className="h-8 w-8 animate-spin" />
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Analytics Dashboard</h2>
          <p className="text-muted-foreground">
            Query performance and usage insights
          </p>
        </div>
        <div className="flex items-center gap-4">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1">Last 24h</SelectItem>
              <SelectItem value="7">Last 7 days</SelectItem>
              <SelectItem value="30">Last 30 days</SelectItem>
              <SelectItem value="90">Last 90 days</SelectItem>
            </SelectContent>
          </Select>
          <Button onClick={loadAnalyticsData} variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      {usageOverview && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Queries</CardTitle>
              <Database className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{usageOverview.totalQueries}</div>
              <p className="text-xs text-muted-foreground">
                {usageOverview.queriesPerDay.toFixed(1)} per day
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg Execution Time</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatExecutionTime(usageOverview.avgExecutionTime)}
              </div>
              <p className="text-xs text-muted-foreground">
                Average response time
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Error Rate</CardTitle>
              <AlertTriangle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{usageOverview.errorRate.toFixed(1)}%</div>
              <p className="text-xs text-muted-foreground">
                Failed queries
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Performance Score</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold ${getPerformanceScoreColor(usageOverview.performanceScore)}`}>
                {usageOverview.performanceScore}
              </div>
              <Badge variant={getPerformanceScoreBadge(usageOverview.performanceScore)} className="text-xs">
                {usageOverview.performanceScore >= 80 ? 'Excellent' : 
                 usageOverview.performanceScore >= 60 ? 'Good' : 'Needs Improvement'}
              </Badge>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Query Trends */}
        <Card>
          <CardHeader>
            <CardTitle>Query Trends</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={trends}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip />
                <Line 
                  type="monotone" 
                  dataKey="queries" 
                  stroke="#8884d8" 
                  strokeWidth={2}
                  name="Queries"
                />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Success Rate */}
        <Card>
          <CardHeader>
            <CardTitle>Query Success Rate</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={pieChartData}
                  cx="50%"
                  cy="50%"
                  innerRadius={60}
                  outerRadius={100}
                  paddingAngle={5}
                  dataKey="value"
                >
                  {pieChartData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
            <div className="flex justify-center gap-4 mt-4">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <span className="text-sm">Successful</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                <span className="text-sm">Failed</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Execution Time Trends */}
      <Card>
        <CardHeader>
          <CardTitle>Execution Time Trends</CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={trends}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="executionTime" fill="#82ca9d" name="Avg Execution Time (ms)" />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Alerts and Slow Queries */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Active Alerts */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5" />
              Active Alerts
            </CardTitle>
          </CardHeader>
          <CardContent>
            {alerts.length === 0 ? (
              <div className="text-center py-8">
                <CheckCircle className="h-8 w-8 text-green-500 mx-auto mb-2" />
                <p className="text-muted-foreground">No active alerts</p>
              </div>
            ) : (
              <div className="space-y-3">
                {alerts.map((alert) => (
                  <div key={alert.id} className="flex items-start gap-3 p-3 border rounded-lg">
                    <AlertTriangle className="h-4 w-4 text-orange-500 mt-0.5" />
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{alert.title}</span>
                        <Badge variant="outline" className="text-xs">
                          {alert.severity}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground">{alert.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Slow Queries */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5" />
              Slowest Queries
            </CardTitle>
          </CardHeader>
          <CardContent>
            {slowQueries.length === 0 ? (
              <div className="text-center py-8">
                <Zap className="h-8 w-8 text-green-500 mx-auto mb-2" />
                <p className="text-muted-foreground">No slow queries detected</p>
              </div>
            ) : (
              <div className="space-y-3">
                {slowQueries.map((query, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <div className="font-mono text-sm">Query #{query.queryHash}</div>
                      <div className="text-xs text-muted-foreground">
                        Executed {query.executionCount} times
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-medium">{formatExecutionTime(query.avgExecutionTime)}</div>
                      <div className="text-xs text-muted-foreground">avg time</div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
