'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  <PERSON>atter<PERSON>hart,
  Scatter
} from 'recharts';
import { 
  Clock, 
  TrendingUp, 
  AlertTriangle, 
  Search,
  Filter,
  Download,
  Lightbulb
} from 'lucide-react';

interface QueryPerformanceProps {
  userId: string;
}

interface QueryAnalysis {
  queryHash: string;
  queryType: string;
  complexity: 'LOW' | 'MEDIUM' | 'HIGH';
  avgExecutionTime: number;
  minExecutionTime: number;
  maxExecutionTime: number;
  executionCount: number;
  errorRate: number;
  lastExecuted: string;
  tablesAccessed: string[];
  hasJoins: boolean;
  hasSubqueries: boolean;
  hasAggregations: boolean;
  suggestions: string[];
}

export function QueryPerformance({ userId }: QueryPerformanceProps) {
  const [queries, setQueries] = useState<QueryAnalysis[]>([]);
  const [filteredQueries, setFilteredQueries] = useState<QueryAnalysis[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedComplexity, setSelectedComplexity] = useState<string>('all');
  const [selectedType, setSelectedType] = useState<string>('all');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadQueryPerformanceData();
  }, [userId]);

  useEffect(() => {
    filterQueries();
  }, [queries, searchTerm, selectedComplexity, selectedType]);

  const loadQueryPerformanceData = async () => {
    setIsLoading(true);
    try {
      // Mock data for demonstration
      const mockData: QueryAnalysis[] = [
        {
          queryHash: 'abc123',
          queryType: 'SELECT',
          complexity: 'HIGH',
          avgExecutionTime: 2500,
          minExecutionTime: 1200,
          maxExecutionTime: 4800,
          executionCount: 45,
          errorRate: 2.2,
          lastExecuted: new Date().toISOString(),
          tablesAccessed: ['users', 'orders', 'products'],
          hasJoins: true,
          hasSubqueries: true,
          hasAggregations: true,
          suggestions: [
            'Consider adding an index on users.email',
            'The subquery could be optimized with a JOIN',
            'LIMIT clause recommended for large result sets'
          ]
        },
        {
          queryHash: 'def456',
          queryType: 'SELECT',
          complexity: 'MEDIUM',
          avgExecutionTime: 850,
          minExecutionTime: 600,
          maxExecutionTime: 1200,
          executionCount: 128,
          errorRate: 0.8,
          lastExecuted: new Date().toISOString(),
          tablesAccessed: ['customers', 'orders'],
          hasJoins: true,
          hasSubqueries: false,
          hasAggregations: true,
          suggestions: [
            'Query performance is good',
            'Consider caching results for frequently accessed data'
          ]
        },
        {
          queryHash: 'ghi789',
          queryType: 'INSERT',
          complexity: 'LOW',
          avgExecutionTime: 120,
          minExecutionTime: 80,
          maxExecutionTime: 200,
          executionCount: 89,
          errorRate: 1.1,
          lastExecuted: new Date().toISOString(),
          tablesAccessed: ['audit_log'],
          hasJoins: false,
          hasSubqueries: false,
          hasAggregations: false,
          suggestions: [
            'Excellent performance',
            'No optimization needed'
          ]
        }
      ];

      setQueries(mockData);
    } catch (error) {
      console.error('Failed to load query performance data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const filterQueries = () => {
    let filtered = queries;

    if (searchTerm) {
      filtered = filtered.filter(query =>
        query.queryHash.toLowerCase().includes(searchTerm.toLowerCase()) ||
        query.queryType.toLowerCase().includes(searchTerm.toLowerCase()) ||
        query.tablesAccessed.some(table => 
          table.toLowerCase().includes(searchTerm.toLowerCase())
        )
      );
    }

    if (selectedComplexity !== 'all') {
      filtered = filtered.filter(query => query.complexity === selectedComplexity);
    }

    if (selectedType !== 'all') {
      filtered = filtered.filter(query => query.queryType === selectedType);
    }

    setFilteredQueries(filtered);
  };

  const getComplexityColor = (complexity: string) => {
    switch (complexity) {
      case 'LOW': return 'bg-green-100 text-green-800';
      case 'MEDIUM': return 'bg-yellow-100 text-yellow-800';
      case 'HIGH': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPerformanceStatus = (avgTime: number, errorRate: number) => {
    if (avgTime > 3000 || errorRate > 5) return { status: 'Poor', color: 'destructive' };
    if (avgTime > 1000 || errorRate > 2) return { status: 'Fair', color: 'secondary' };
    return { status: 'Good', color: 'default' };
  };

  const formatTime = (ms: number) => {
    if (ms < 1000) return `${ms}ms`;
    return `${(ms / 1000).toFixed(2)}s`;
  };

  // Prepare chart data
  const chartData = filteredQueries.map(query => ({
    name: query.queryHash.slice(0, 8),
    executionTime: query.avgExecutionTime,
    executionCount: query.executionCount,
    errorRate: query.errorRate,
  }));

  const scatterData = filteredQueries.map(query => ({
    x: query.executionCount,
    y: query.avgExecutionTime,
    name: query.queryHash.slice(0, 8),
    complexity: query.complexity,
  }));

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-5/6"></div>
            <div className="h-4 bg-gray-200 rounded w-4/6"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Query Performance Analysis</h2>
          <p className="text-muted-foreground">
            Detailed performance metrics and optimization suggestions
          </p>
        </div>
        <Button variant="outline" className="flex items-center gap-2">
          <Download className="h-4 w-4" />
          Export Report
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search queries..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <select
              value={selectedComplexity}
              onChange={(e) => setSelectedComplexity(e.target.value)}
              className="px-3 py-2 border rounded-md"
            >
              <option value="all">All Complexities</option>
              <option value="LOW">Low Complexity</option>
              <option value="MEDIUM">Medium Complexity</option>
              <option value="HIGH">High Complexity</option>
            </select>
            <select
              value={selectedType}
              onChange={(e) => setSelectedType(e.target.value)}
              className="px-3 py-2 border rounded-md"
            >
              <option value="all">All Query Types</option>
              <option value="SELECT">SELECT</option>
              <option value="INSERT">INSERT</option>
              <option value="UPDATE">UPDATE</option>
              <option value="DELETE">DELETE</option>
            </select>
          </div>
        </CardContent>
      </Card>

      {/* Performance Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Execution Time Chart */}
        <Card>
          <CardHeader>
            <CardTitle>Average Execution Time</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="executionTime" fill="#8884d8" name="Execution Time (ms)" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Execution Count vs Time Scatter */}
        <Card>
          <CardHeader>
            <CardTitle>Execution Frequency vs Performance</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <ScatterChart data={scatterData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="x" name="Execution Count" />
                <YAxis dataKey="y" name="Avg Time (ms)" />
                <Tooltip cursor={{ strokeDasharray: '3 3' }} />
                <Scatter dataKey="y" fill="#82ca9d" />
              </ScatterChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Query Details Table */}
      <Card>
        <CardHeader>
          <CardTitle>Query Performance Details</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Query Hash</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Complexity</TableHead>
                <TableHead>Avg Time</TableHead>
                <TableHead>Executions</TableHead>
                <TableHead>Error Rate</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredQueries.map((query) => {
                const performance = getPerformanceStatus(query.avgExecutionTime, query.errorRate);
                return (
                  <TableRow key={query.queryHash}>
                    <TableCell className="font-mono text-sm">
                      {query.queryHash.slice(0, 12)}...
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">{query.queryType}</Badge>
                    </TableCell>
                    <TableCell>
                      <Badge className={getComplexityColor(query.complexity)}>
                        {query.complexity}
                      </Badge>
                    </TableCell>
                    <TableCell>{formatTime(query.avgExecutionTime)}</TableCell>
                    <TableCell>{query.executionCount}</TableCell>
                    <TableCell>{query.errorRate.toFixed(1)}%</TableCell>
                    <TableCell>
                      <Badge variant={performance.color as any}>
                        {performance.status}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Button variant="ghost" size="sm">
                        <Lightbulb className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Optimization Suggestions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Lightbulb className="h-5 w-5" />
            Optimization Suggestions
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredQueries
              .filter(query => query.suggestions.length > 0)
              .map((query) => (
                <div key={query.queryHash} className="border rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="font-mono text-sm">Query #{query.queryHash.slice(0, 8)}</span>
                    <Badge className={getComplexityColor(query.complexity)}>
                      {query.complexity}
                    </Badge>
                    <span className="text-sm text-muted-foreground">
                      {formatTime(query.avgExecutionTime)} avg
                    </span>
                  </div>
                  <ul className="space-y-1">
                    {query.suggestions.map((suggestion, index) => (
                      <li key={index} className="text-sm flex items-start gap-2">
                        <span className="text-blue-500 mt-1">•</span>
                        {suggestion}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
