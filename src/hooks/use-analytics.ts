import { useState, useEffect, useCallback } from 'react';

export interface AnalyticsData {
  performance: {
    avgExecutionTime: number;
    slowQueries: number;
    totalQueries: number;
    errorRate: number;
  };
  usage: {
    totalQueries: number;
    avgExecutionTime: number;
    errorRate: number;
    slowQueries: number;
    queriesPerDay: number;
    performanceScore: number;
    period: {
      start: string;
      end: string;
      days: number;
    };
  };
  trends: Array<{
    date: string;
    queries: number;
    executionTime: number;
    errors: number;
  }>;
  slowQueries: Array<{
    queryHash: string;
    avgExecutionTime: number;
    executionCount: number;
    lastExecuted: string;
  }>;
  alerts: Array<{
    id: string;
    type: string;
    severity: string;
    title: string;
    description: string;
    timestamp: string;
    resolved: boolean;
  }>;
}

export interface UseAnalyticsOptions {
  userId: string;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

export function useAnalytics({ userId, autoRefresh = false, refreshInterval = 30000 }: UseAnalyticsOptions) {
  const [data, setData] = useState<Partial<AnalyticsData>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  // Fetch analytics data
  const fetchAnalytics = useCallback(async (type: keyof AnalyticsData, options: { days?: number; limit?: number } = {}) => {
    try {
      const params = new URLSearchParams({
        userId,
        type,
        ...(options.days && { days: options.days.toString() }),
        ...(options.limit && { limit: options.limit.toString() }),
      });

      const response = await fetch(`/api/analytics?${params}`);
      if (!response.ok) {
        throw new Error(`Failed to fetch ${type} analytics`);
      }

      const result = await response.json();
      return result.data;
    } catch (err) {
      console.error(`Error fetching ${type} analytics:`, err);
      throw err;
    }
  }, [userId]);

  // Load all analytics data
  const loadAllAnalytics = useCallback(async (days: number = 7) => {
    setIsLoading(true);
    setError(null);

    try {
      const [performance, usage, trends, slowQueries, alerts] = await Promise.all([
        fetchAnalytics('performance', { days }),
        fetchAnalytics('usage', { days }),
        fetchAnalytics('trends', { days }),
        fetchAnalytics('slowQueries', { limit: 10 }),
        fetchAnalytics('alerts'),
      ]);

      setData({
        performance,
        usage,
        trends,
        slowQueries,
        alerts,
      });

      setLastUpdated(new Date());
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load analytics');
    } finally {
      setIsLoading(false);
    }
  }, [fetchAnalytics]);

  // Record analytics event
  const recordEvent = useCallback(async (
    type: 'query_execution' | 'collaboration_session' | 'user_action',
    eventData: any
  ) => {
    try {
      const response = await fetch('/api/analytics', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type,
          userId,
          data: eventData,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to record analytics event');
      }

      return await response.json();
    } catch (err) {
      console.error('Error recording analytics event:', err);
      throw err;
    }
  }, [userId]);

  // Record query execution
  const recordQueryExecution = useCallback(async (queryData: {
    connectionId: string;
    sql: string;
    result: any;
    executionTime: number;
  }) => {
    return recordEvent('query_execution', queryData);
  }, [recordEvent]);

  // Record collaboration session
  const recordCollaborationSession = useCallback(async (sessionData: {
    sessionId: string;
    action: 'join' | 'leave' | 'message' | 'query_share';
    participants?: number;
    duration?: number;
  }) => {
    return recordEvent('collaboration_session', sessionData);
  }, [recordEvent]);

  // Record user action
  const recordUserAction = useCallback(async (actionData: {
    action: string;
    feature: string;
    metadata?: any;
  }) => {
    return recordEvent('user_action', actionData);
  }, [recordEvent]);

  // Get specific analytics data
  const getPerformanceStats = useCallback((days: number = 7) => {
    return fetchAnalytics('performance', { days });
  }, [fetchAnalytics]);

  const getUsageOverview = useCallback((days: number = 7) => {
    return fetchAnalytics('usage', { days });
  }, [fetchAnalytics]);

  const getTrends = useCallback((days: number = 30) => {
    return fetchAnalytics('trends', { days });
  }, [fetchAnalytics]);

  const getSlowQueries = useCallback((limit: number = 10) => {
    return fetchAnalytics('slowQueries', { limit });
  }, [fetchAnalytics]);

  const getAlerts = useCallback(() => {
    return fetchAnalytics('alerts');
  }, [fetchAnalytics]);

  // Auto-refresh functionality
  useEffect(() => {
    if (autoRefresh && refreshInterval > 0) {
      const interval = setInterval(() => {
        loadAllAnalytics();
      }, refreshInterval);

      return () => clearInterval(interval);
    }
    return undefined;
  }, [autoRefresh, refreshInterval, loadAllAnalytics]);

  // Initial load
  useEffect(() => {
    loadAllAnalytics();
  }, [loadAllAnalytics]);

  return {
    // Data
    data,
    isLoading,
    error,
    lastUpdated,

    // Methods
    loadAllAnalytics,
    recordQueryExecution,
    recordCollaborationSession,
    recordUserAction,

    // Specific data fetchers
    getPerformanceStats,
    getUsageOverview,
    getTrends,
    getSlowQueries,
    getAlerts,

    // Computed values
    hasData: Object.keys(data).length > 0,
    performanceScore: data.usage?.performanceScore || 0,
    totalQueries: data.usage?.totalQueries || 0,
    avgExecutionTime: data.usage?.avgExecutionTime || 0,
    errorRate: data.usage?.errorRate || 0,
    activeAlerts: data.alerts?.filter(alert => !alert.resolved) || [],
    alertCount: data.alerts?.filter(alert => !alert.resolved).length || 0,
  };
}

// Hook for real-time analytics tracking
export function useAnalyticsTracking(userId: string) {
  const { recordQueryExecution, recordCollaborationSession, recordUserAction } = useAnalytics({ userId });

  // Track query execution automatically
  const trackQueryExecution = useCallback((queryData: {
    connectionId: string;
    sql: string;
    result: any;
    executionTime: number;
  }) => {
    // Record in background, don't block UI
    recordQueryExecution(queryData).catch(console.error);
  }, [recordQueryExecution]);

  // Track collaboration events
  const trackCollaboration = useCallback((sessionData: {
    sessionId: string;
    action: 'join' | 'leave' | 'message' | 'query_share';
    participants?: number;
    duration?: number;
  }) => {
    recordCollaborationSession(sessionData).catch(console.error);
  }, [recordCollaborationSession]);

  // Track user actions
  const trackUserAction = useCallback((action: string, feature: string, metadata?: any) => {
    recordUserAction({ action, feature, metadata }).catch(console.error);
  }, [recordUserAction]);

  // Track page views
  const trackPageView = useCallback((page: string) => {
    trackUserAction('page_view', 'navigation', { page });
  }, [trackUserAction]);

  // Track feature usage
  const trackFeatureUsage = useCallback((feature: string, action: string, metadata?: any) => {
    trackUserAction(action, feature, metadata);
  }, [trackUserAction]);

  return {
    trackQueryExecution,
    trackCollaboration,
    trackUserAction,
    trackPageView,
    trackFeatureUsage,
  };
}
