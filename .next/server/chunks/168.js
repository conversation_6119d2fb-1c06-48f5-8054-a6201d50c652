exports.id=168,exports.ids=[168],exports.modules={13878:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=13878,e.exports=t},98480:(e,t,a)=>{"use strict";a.d(t,{ConnectorFactory:()=>c});var n=a(63069);class s{constructor(e){this.config={...e,connectionTimeout:e.connectionTimeout||3e4,queryTimeout:e.queryTimeout||6e4}}sanitizeQuery(e){for(let t of[/;\s*(drop|delete|truncate|alter)\s+/gi,/;\s*exec\s*\(/gi,/;\s*execute\s*\(/gi,/xp_cmdshell/gi,/sp_executesql/gi])if(t.test(e))throw Error("Query contains potentially dangerous SQL patterns");return e.trim()}async withTimeout(e,t,a){return Promise.race([e,new Promise((e,n)=>setTimeout(()=>n(Error(a)),t))])}formatError(e){return e instanceof Error?e:Error(String(e))}}class o extends s{constructor(e){super(e),this.pool=n.createPool({host:e.host,port:e.port,database:e.database,user:e.username,password:e.password,ssl:e.ssl?{rejectUnauthorized:!1}:void 0,connectionLimit:10,charset:"utf8mb4"})}async connect(){try{await this.withTimeout(this.pool.getConnection(),this.config.connectionTimeout,"MySQL connection timeout")}catch(e){throw this.formatError(e)}}async disconnect(){try{await this.pool.end()}catch(e){throw this.formatError(e)}}async testConnection(){let e=null;try{return e=await this.withTimeout(this.pool.getConnection(),this.config.connectionTimeout,"MySQL connection timeout"),await e.query("SELECT 1"),!0}catch(e){return console.error("MySQL connection test failed:",e),!1}finally{e&&e.release()}}async executeQuery(e,t=[]){let a=this.sanitizeQuery(e),n=null;try{let e=Date.now();n=await this.pool.getConnection();let[s,o]=await this.withTimeout(n.execute(a,t),this.config.queryTimeout,"Query execution timeout"),i=Date.now()-e,r=o.map(e=>({name:e.name,type:this.mapMySQLType(e.type||0),nullable:!0}));return{rows:Array.isArray(s)?s:[],fields:r,rowCount:Array.isArray(s)?s.length:0,executionTime:i}}catch(e){throw this.formatError(e)}finally{n&&n.release()}}async getSchema(){let e=`
      SELECT 
        t.TABLE_NAME as table_name,
        t.TABLE_SCHEMA as table_schema,
        c.COLUMN_NAME as column_name,
        c.DATA_TYPE as data_type,
        c.IS_NULLABLE as is_nullable,
        c.COLUMN_DEFAULT as column_default,
        CASE WHEN c.COLUMN_KEY = 'PRI' THEN 1 ELSE 0 END as is_primary_key,
        CASE WHEN c.COLUMN_KEY = 'MUL' THEN 1 ELSE 0 END as is_foreign_key,
        kcu.REFERENCED_TABLE_NAME as referenced_table_name,
        kcu.REFERENCED_COLUMN_NAME as referenced_column_name
      FROM information_schema.TABLES t
      LEFT JOIN information_schema.COLUMNS c ON t.TABLE_NAME = c.TABLE_NAME AND t.TABLE_SCHEMA = c.TABLE_SCHEMA
      LEFT JOIN information_schema.KEY_COLUMN_USAGE kcu ON c.TABLE_NAME = kcu.TABLE_NAME 
        AND c.COLUMN_NAME = kcu.COLUMN_NAME 
        AND c.TABLE_SCHEMA = kcu.TABLE_SCHEMA
        AND kcu.REFERENCED_TABLE_NAME IS NOT NULL
      WHERE t.TABLE_SCHEMA = ? AND t.TABLE_TYPE = 'BASE TABLE'
      ORDER BY t.TABLE_NAME, c.ORDINAL_POSITION;
    `,t=`
      SELECT 
        s.INDEX_NAME as index_name,
        s.TABLE_NAME as table_name,
        s.TABLE_SCHEMA as schema_name,
        GROUP_CONCAT(s.COLUMN_NAME ORDER BY s.SEQ_IN_INDEX) as columns,
        CASE WHEN s.NON_UNIQUE = 0 THEN 1 ELSE 0 END as is_unique,
        CASE WHEN s.INDEX_NAME = 'PRIMARY' THEN 1 ELSE 0 END as is_primary
      FROM information_schema.STATISTICS s
      WHERE s.TABLE_SCHEMA = ?
      GROUP BY s.INDEX_NAME, s.TABLE_NAME, s.TABLE_SCHEMA, s.NON_UNIQUE
      ORDER BY s.TABLE_NAME, s.INDEX_NAME;
    `,a=`
      SELECT 
        TABLE_NAME as view_name,
        TABLE_SCHEMA as schema_name,
        VIEW_DEFINITION as view_definition
      FROM information_schema.VIEWS
      WHERE TABLE_SCHEMA = ?
      ORDER BY TABLE_NAME;
    `;try{let[n,s,o]=await Promise.all([this.executeQuery(e,[this.config.database]),this.executeQuery(t,[this.config.database]),this.executeQuery(a,[this.config.database])]),i=new Map;for(let e of n.rows){let t=`${e.table_schema}.${e.table_name}`;i.has(t)||i.set(t,{name:e.table_name,schema:e.table_schema,columns:[],indexes:[]}),e.column_name&&i.get(t).columns.push({name:e.column_name,type:e.data_type,nullable:"YES"===e.is_nullable,defaultValue:e.column_default,isPrimaryKey:1===e.is_primary_key,isForeignKey:1===e.is_foreign_key,referencedTable:e.referenced_table_name,referencedColumn:e.referenced_column_name})}for(let e of s.rows){let t=`${e.schema_name}.${e.table_name}`;i.has(t)&&i.get(t).indexes.push({name:e.index_name,columns:e.columns?e.columns.split(","):[],isUnique:1===e.is_unique,isPrimary:1===e.is_primary})}return{tables:Array.from(i.values()),views:o.rows.map(e=>({name:e.view_name,schema:e.schema_name,definition:e.view_definition}))}}catch(e){throw this.formatError(e)}}async validateQuery(e){try{let t=this.sanitizeQuery(e),a=`EXPLAIN ${t}`;return await this.executeQuery(a),{isValid:!0}}catch(e){return{isValid:!1,error:e instanceof Error?e.message:String(e)}}}mapMySQLType(e){return({0:"decimal",1:"tinyint",2:"smallint",3:"int",4:"float",5:"double",7:"timestamp",8:"bigint",9:"mediumint",10:"date",11:"time",12:"datetime",13:"year",15:"varchar",16:"bit",245:"json",246:"decimal",247:"enum",248:"set",249:"tinyblob",250:"mediumblob",251:"longblob",252:"blob",253:"varchar",254:"char",255:"geometry"})[e]||"unknown"}}var i=a(35900);class r extends s{constructor(e){super(e);let t={host:e.host,port:e.port,database:e.database,user:e.username,password:e.password,ssl:!!e.ssl&&{rejectUnauthorized:!1},max:10,min:1,idleTimeoutMillis:3e4,connectionTimeoutMillis:e.connectionTimeout,query_timeout:e.queryTimeout};this.pool=new i.Pool(t),this.pool.on("error",e=>{console.error("PostgreSQL pool error:",e)})}async connect(){try{await this.withTimeout(this.pool.connect(),this.config.connectionTimeout,"PostgreSQL connection timeout")}catch(e){throw this.formatError(e)}}async disconnect(){try{await this.pool.end()}catch(e){throw this.formatError(e)}}async testConnection(){let e=null;try{return e=await this.withTimeout(this.pool.connect(),this.config.connectionTimeout,"PostgreSQL connection timeout"),await e.query("SELECT 1"),!0}catch(e){return console.error("PostgreSQL connection test failed:",e),!1}finally{e&&e.release()}}async executeQuery(e,t=[]){let a=this.sanitizeQuery(e),n=null;try{let e=Date.now();n=await this.pool.connect();let s=await this.withTimeout(n.query(a,t),this.config.queryTimeout,"Query execution timeout"),o=Date.now()-e,i=s.fields.map(e=>({name:e.name,type:this.mapPostgreSQLType(e.dataTypeID),nullable:!0}));return{rows:s.rows,fields:i,rowCount:s.rowCount||0,executionTime:o}}catch(e){throw this.formatError(e)}finally{n&&n.release()}}async getSchema(){let e=`
      SELECT 
        t.table_name,
        t.table_schema,
        c.column_name,
        c.data_type,
        c.is_nullable,
        c.column_default,
        CASE WHEN pk.column_name IS NOT NULL THEN true ELSE false END as is_primary_key,
        CASE WHEN fk.column_name IS NOT NULL THEN true ELSE false END as is_foreign_key,
        fk.referenced_table_name,
        fk.referenced_column_name
      FROM information_schema.tables t
      LEFT JOIN information_schema.columns c ON t.table_name = c.table_name AND t.table_schema = c.table_schema
      LEFT JOIN (
        SELECT ku.table_name, ku.column_name, ku.table_schema
        FROM information_schema.table_constraints tc
        JOIN information_schema.key_column_usage ku ON tc.constraint_name = ku.constraint_name
        WHERE tc.constraint_type = 'PRIMARY KEY'
      ) pk ON c.table_name = pk.table_name AND c.column_name = pk.column_name AND c.table_schema = pk.table_schema
      LEFT JOIN (
        SELECT 
          ku.table_name, 
          ku.column_name, 
          ku.table_schema,
          ccu.table_name as referenced_table_name,
          ccu.column_name as referenced_column_name
        FROM information_schema.table_constraints tc
        JOIN information_schema.key_column_usage ku ON tc.constraint_name = ku.constraint_name
        JOIN information_schema.constraint_column_usage ccu ON tc.constraint_name = ccu.constraint_name
        WHERE tc.constraint_type = 'FOREIGN KEY'
      ) fk ON c.table_name = fk.table_name AND c.column_name = fk.column_name AND c.table_schema = fk.table_schema
      WHERE t.table_schema NOT IN ('information_schema', 'pg_catalog', 'pg_toast')
      ORDER BY t.table_name, c.ordinal_position;
    `,t=`
      SELECT 
        i.relname as index_name,
        t.relname as table_name,
        n.nspname as schema_name,
        array_agg(a.attname ORDER BY a.attnum) as columns,
        ix.indisunique as is_unique,
        ix.indisprimary as is_primary
      FROM pg_class i
      JOIN pg_index ix ON i.oid = ix.indexrelid
      JOIN pg_class t ON ix.indrelid = t.oid
      JOIN pg_namespace n ON t.relnamespace = n.oid
      JOIN pg_attribute a ON t.oid = a.attrelid AND a.attnum = ANY(ix.indkey)
      WHERE n.nspname NOT IN ('information_schema', 'pg_catalog', 'pg_toast')
      GROUP BY i.relname, t.relname, n.nspname, ix.indisunique, ix.indisprimary
      ORDER BY t.relname, i.relname;
    `,a=`
      SELECT 
        table_name as view_name,
        table_schema as schema_name,
        view_definition
      FROM information_schema.views
      WHERE table_schema NOT IN ('information_schema', 'pg_catalog')
      ORDER BY table_name;
    `;try{let[n,s,o]=await Promise.all([this.executeQuery(e),this.executeQuery(t),this.executeQuery(a)]),i=new Map;for(let e of n.rows){let t=`${e.table_schema}.${e.table_name}`;i.has(t)||i.set(t,{name:e.table_name,schema:e.table_schema,columns:[],indexes:[]}),e.column_name&&i.get(t).columns.push({name:e.column_name,type:e.data_type,nullable:"YES"===e.is_nullable,defaultValue:e.column_default,isPrimaryKey:e.is_primary_key,isForeignKey:e.is_foreign_key,referencedTable:e.referenced_table_name,referencedColumn:e.referenced_column_name})}for(let e of s.rows){let t=`${e.schema_name}.${e.table_name}`;i.has(t)&&i.get(t).indexes.push({name:e.index_name,columns:e.columns,isUnique:e.is_unique,isPrimary:e.is_primary})}return{tables:Array.from(i.values()),views:o.rows.map(e=>({name:e.view_name,schema:e.schema_name,definition:e.view_definition}))}}catch(e){throw this.formatError(e)}}async validateQuery(e){try{let t=this.sanitizeQuery(e),a=`EXPLAIN ${t}`;return await this.executeQuery(a),{isValid:!0}}catch(e){return{isValid:!1,error:e instanceof Error?e.message:String(e)}}}mapPostgreSQLType(e){return({16:"boolean",20:"bigint",21:"smallint",23:"integer",25:"text",700:"real",701:"double precision",1043:"varchar",1082:"date",1114:"timestamp",1184:"timestamptz"})[e]||"unknown"}}class c{static{this.connectors=new Map}static createConnector(e,t,a){let n;if(a&&this.connectors.has(a))return this.connectors.get(a);switch(e){case"MYSQL":n=new o(t);break;case"POSTGRESQL":n=new r(t);break;default:throw Error(`Unsupported database type: ${e}`)}return a&&this.connectors.set(a,n),n}static async removeConnector(e){let t=this.connectors.get(e);if(t){try{await t.disconnect()}catch(t){console.error(`Error disconnecting connector ${e}:`,t)}this.connectors.delete(e)}}static async removeAllConnectors(){let e=Array.from(this.connectors.entries()).map(async([e,t])=>{try{await t.disconnect()}catch(t){console.error(`Error disconnecting connector ${e}:`,t)}});await Promise.allSettled(e),this.connectors.clear()}static getActiveConnections(){return Array.from(this.connectors.keys())}static getConnectionCount(){return this.connectors.size}static hasConnection(e){return this.connectors.has(e)}}process.on("SIGTERM",async()=>{console.log("Received SIGTERM, closing database connections..."),await c.removeAllConnectors(),process.exit(0)}),process.on("SIGINT",async()=>{console.log("Received SIGINT, closing database connections..."),await c.removeAllConnectors(),process.exit(0)})}};