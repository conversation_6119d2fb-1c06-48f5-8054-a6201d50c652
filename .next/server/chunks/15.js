exports.id=15,exports.ids=[15],exports.modules={4e4:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,2583,23)),Promise.resolve().then(s.t.bind(s,26840,23)),Promise.resolve().then(s.t.bind(s,38771,23)),Promise.resolve().then(s.t.bind(s,13225,23)),Promise.resolve().then(s.t.bind(s,9295,23)),Promise.resolve().then(s.t.bind(s,43982,23))},92912:(e,t,s)=>{Promise.resolve().then(s.bind(s,56820)),Promise.resolve().then(s.bind(s,87658))},56820:(e,t,s)=>{"use strict";s.r(t),s.d(t,{Providers:()=>l});var a=s(95344),r=s(19115),o=s(26274),i=s(47674),n=s(3729),d=s(51540);function c({children:e}){let{data:t,status:s}=(0,i.useSession)(),{setCurrentUser:r,loadUserConnections:o,loadUserSessions:c,currentUser:l,connections:u}=(0,d.l)();return(0,n.useEffect)(()=>{let e=async()=>{if("authenticated"===s&&t?.user&&!l)try{r({id:t.user.id,name:t.user.name||"",email:t.user.email||""}),await o(t.user.id),await c(t.user.id);let e=d.l.getState();e.connections.length>0&&e.connections[0]&&d.l.getState().setCurrentDatabase(e.connections[0])}catch(e){console.error("Failed to initialize authenticated user:",e)}else if("unauthenticated"===s&&!l)try{let e=await fetch("/api/users?email=<EMAIL>");if(e.ok){let t=await e.json();r({id:t.id,name:t.name,email:t.email}),await o(t.id),await c(t.id);let s=d.l.getState();s.connections.length>0&&s.connections[0]&&d.l.getState().setCurrentDatabase(s.connections[0])}else console.error("Demo user not found in database")}catch(e){console.error("Failed to initialize demo user:",e)}};"loading"!==s&&e()},[t,s,l,r,o,c]),a.jsx(a.Fragment,{children:e})}function l({children:e}){let[t]=(0,n.useState)(()=>new r.S({defaultOptions:{queries:{staleTime:6e4,retry:(e,t)=>!(t?.status>=400&&t?.status<500)&&e<3},mutations:{retry:!1}}}));return a.jsx(i.SessionProvider,{children:a.jsx(o.aH,{client:t,children:a.jsx(c,{children:e})})})}},87658:(e,t,s)=>{"use strict";s.r(t),s.d(t,{Toaster:()=>D});var a=s(95344),r=s(3729);let o=0,i=new Map,n=e=>{if(i.has(e))return;let t=setTimeout(()=>{i.delete(e),u({type:"REMOVE_TOAST",toastId:e})},1e6);i.set(e,t)},d=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:s}=t;return s?n(s):e.toasts.forEach(e=>{n(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===s||void 0===s?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},c=[],l={toasts:[]};function u(e){l=d(l,e),c.forEach(e=>{e(l)})}function m({...e}){let t=(o=(o+1)%Number.MAX_SAFE_INTEGER).toString(),s=()=>u({type:"DISMISS_TOAST",toastId:t});return u({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:e=>{e||s()}}}),{id:t,dismiss:s,update:e=>u({type:"UPDATE_TOAST",toast:{...e,id:t}})}}var p=s(33390),f=s(49247),y=s(14513),g=s(11453);let h=p.zt,v=r.forwardRef(({className:e,...t},s)=>a.jsx(p.l_,{ref:s,className:(0,g.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...t}));v.displayName=p.l_.displayName;let w=(0,f.j)("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),x=r.forwardRef(({className:e,variant:t,...s},r)=>a.jsx(p.fC,{ref:r,className:(0,g.cn)(w({variant:t}),e),...s}));x.displayName=p.fC.displayName,r.forwardRef(({className:e,...t},s)=>a.jsx(p.aU,{ref:s,className:(0,g.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",e),...t})).displayName=p.aU.displayName;let S=r.forwardRef(({className:e,...t},s)=>a.jsx(p.x8,{ref:s,className:(0,g.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":"",...t,children:a.jsx(y.Z,{className:"h-4 w-4"})}));S.displayName=p.x8.displayName;let b=r.forwardRef(({className:e,...t},s)=>a.jsx(p.Dx,{ref:s,className:(0,g.cn)("text-sm font-semibold",e),...t}));b.displayName=p.Dx.displayName;let T=r.forwardRef(({className:e,...t},s)=>a.jsx(p.dk,{ref:s,className:(0,g.cn)("text-sm opacity-90",e),...t}));function D(){let{toasts:e}=function(){let[e,t]=r.useState(l);return r.useEffect(()=>(c.push(t),()=>{let e=c.indexOf(t);e>-1&&c.splice(e,1)}),[e]),{...e,toast:m,dismiss:e=>u({type:"DISMISS_TOAST",toastId:e})}}();return(0,a.jsxs)(h,{children:[e.map(function({id:e,title:t,description:s,action:r,...o}){return(0,a.jsxs)(x,{...o,children:[(0,a.jsxs)("div",{className:"grid gap-1",children:[t&&a.jsx(b,{children:t}),s&&a.jsx(T,{children:s})]}),r,a.jsx(S,{})]},e)}),a.jsx(v,{})]})}T.displayName=p.dk.displayName},11453:(e,t,s)=>{"use strict";s.d(t,{SY:()=>i,cn:()=>o});var a=s(56815),r=s(79377);function o(...e){return(0,r.m6)((0,a.W)(e))}function i(e){let t=new Date,s=new Date(e),a=Math.floor((t.getTime()-s.getTime())/1e3);if(a<60)return"just now";let r=Math.floor(a/60);if(r<60)return`${r} minute${1===r?"":"s"} ago`;let o=Math.floor(r/60);if(o<24)return`${o} hour${1===o?"":"s"} ago`;let i=Math.floor(o/24);return i<7?`${i} day${1===i?"":"s"} ago`:new Intl.DateTimeFormat("en-US",{month:"long",day:"numeric",year:"numeric"}).format(new Date(e))}},51540:(e,t,s)=>{"use strict";s.d(t,{l:()=>o});var a=s(43158),r=s(67023);let o=(0,a.Ue)()((0,r.mW)((0,r.tJ)((e,t)=>({currentSession:null,currentQuery:"",currentDatabase:null,messages:[{id:"1",role:"assistant",content:"Hello! I'm your AI SQL assistant. I can help you write, debug, and optimize SQL queries. What would you like to work on today?",timestamp:new Date,type:"text"}],isAILoading:!1,queryHistory:[],connections:[],activeTab:"chat",sidebarOpen:!0,currentUser:null,setCurrentQuery:t=>e({currentQuery:t}),setCurrentDatabase:t=>e({currentDatabase:t}),addMessage:t=>e(e=>({messages:[...e.messages,t]})),setAILoading:t=>e({isAILoading:t}),addToHistory:t=>e(e=>({queryHistory:[t,...e.queryHistory.slice(0,49)]})),setActiveTab:t=>e({activeTab:t}),setSidebarOpen:t=>e({sidebarOpen:t}),addConnection:t=>e(e=>({connections:[...e.connections,t]})),removeConnection:t=>e(e=>({connections:e.connections.filter(e=>e.id!==t)})),clearMessages:()=>e({messages:[{id:"1",role:"assistant",content:"Hello! I'm your AI SQL assistant. I can help you write, debug, and optimize SQL queries. What would you like to work on today?",timestamp:new Date,type:"text"}]}),generateQuery:async e=>{let{addMessage:s,setAILoading:a,setCurrentQuery:r,setActiveTab:o,currentDatabase:n}=t();s({id:Date.now().toString(),role:"user",content:e,timestamp:new Date,type:"text"}),a(!0);try{await new Promise(e=>setTimeout(e,1500));let a=function(e,t){let s=e.toLowerCase();return s.includes("customer")&&s.includes("revenue")?`SELECT 
  c.customer_id,
  c.name,
  c.email,
  COUNT(o.order_id) as total_orders,
  SUM(o.total_amount) as total_revenue
FROM customers c
LEFT JOIN orders o ON c.customer_id = o.customer_id
WHERE o.order_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY c.customer_id, c.name, c.email
ORDER BY total_revenue DESC
LIMIT 10;`:s.includes("product")&&s.includes("sales")?`SELECT 
  p.product_id,
  p.name as product_name,
  COUNT(oi.item_id) as items_sold,
  SUM(oi.quantity * oi.unit_price) as total_sales
FROM products p
JOIN order_items oi ON p.product_id = oi.product_id
JOIN orders o ON oi.order_id = o.order_id
WHERE o.order_date >= DATE_SUB(NOW(), INTERVAL 7 DAY)
GROUP BY p.product_id, p.name
ORDER BY total_sales DESC;`:s.includes("order")&&s.includes("status")?`SELECT 
  status,
  COUNT(*) as order_count,
  AVG(total_amount) as avg_order_value
FROM orders
WHERE order_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY status
ORDER BY order_count DESC;`:`SELECT *
FROM your_table
WHERE condition = 'value'
ORDER BY created_at DESC
LIMIT 10;`}(e,n?.databaseType),o={id:(Date.now()+1).toString(),role:"assistant",content:"I've generated a SQL query based on your request. Here's what I came up with:",timestamp:new Date,type:"query",metadata:{sql:a,explanation:i(e),userInput:e,suggestions:["Consider adding appropriate indexes for better performance","Add error handling for edge cases","Test with sample data before running on production"]}};s(o),r(a);let d={id:Date.now().toString(),sessionId:"current",userInput:e,generatedSQL:a,explanation:i(e),createdAt:new Date};t().addToHistory(d)}catch(e){s({id:(Date.now()+1).toString(),role:"assistant",content:"I apologize, but I encountered an error while generating your query. Please try again or rephrase your request.",timestamp:new Date,type:"text"})}finally{a(!1)}},optimizeQuery:async e=>(await new Promise(e=>setTimeout(e,1e3)),`-- Optimized version
${e}
-- Added index hints and optimizations`),explainQuery:async e=>(await new Promise(e=>setTimeout(e,800)),`This query performs the following operations:
1. Selects data from the specified tables
2. Applies filtering conditions
3. Groups and aggregates results
4. Orders the output for better readability`),setCurrentUser:t=>e({currentUser:t}),loadUserSessions:async e=>{try{let t=await fetch(`/api/queries?type=sessions&userId=${e}`);if(t.ok){let e=await t.json();console.log("Loaded sessions:",e)}}catch(e){console.error("Failed to load user sessions:",e)}},loadUserConnections:async t=>{try{let s=await fetch(`/api/connections?userId=${t}`);if(s.ok){let t=await s.json();e({connections:t})}}catch(e){console.error("Failed to load user connections:",e)}},createNewSession:async(t,s)=>{try{let a=await fetch("/api/queries?type=session",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({userId:t,databaseConnectionId:s,name:`Session ${new Date().toLocaleString()}`})});if(a.ok){let t=await a.json();e({currentSession:t}),console.log("Created new session:",t)}}catch(e){console.error("Failed to create new session:",e)}},saveQueryToDatabase:async(e,s,a)=>{let{currentSession:r,currentUser:o,currentDatabase:i}=t();if(!r||!o||!i){console.warn("Cannot save query: missing session, user, or database");return}try{let t=await fetch("/api/queries?type=query",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({sessionId:r.id,userId:o.id,userInput:e,generatedSQL:s,explanation:a,databaseType:i.databaseType})});if(t.ok){let e=await t.json();console.log("Saved query to database:",e)}}catch(e){console.error("Failed to save query to database:",e)}}}),{name:"querycraft-store",partialize:e=>({queryHistory:e.queryHistory,connections:e.connections,sidebarOpen:e.sidebarOpen,currentDatabase:e.currentDatabase})}),{name:"querycraft-store"}));function i(e){let t=e.toLowerCase();return t.includes("customer")&&t.includes("revenue")?"This query retrieves the top customers by revenue in the last 30 days, including their contact information and order statistics.":t.includes("product")&&t.includes("sales")?"This query analyzes product sales performance over the last 7 days, showing which products are selling best.":t.includes("order")&&t.includes("status")?"This query provides an overview of order statuses and their distribution over the last 30 days.":"This query retrieves data based on your specified criteria with appropriate filtering and sorting."}},98890:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>v,metadata:()=>h});var a=s(25036),r=s(45968),o=s.n(r),i=s(26100),n=s.n(i);s(5023);var d=s(86843);let c=(0,d.createProxy)(String.raw`/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/providers.tsx`),{__esModule:l,$$typeof:u}=c;c.default;let m=(0,d.createProxy)(String.raw`/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/providers.tsx#Providers`),p=(0,d.createProxy)(String.raw`/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/ui/toaster.tsx`),{__esModule:f,$$typeof:y}=p;p.default;let g=(0,d.createProxy)(String.raw`/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/ui/toaster.tsx#Toaster`),h={title:{default:"QueryCraft Studio",template:"%s | QueryCraft Studio"},description:"AI-powered SQL development platform with multi-agent assistance for MySQL and PostgreSQL databases.",keywords:["SQL","Database","AI","MySQL","PostgreSQL","Query Optimization","SQL Development","Database Tools"],authors:[{name:"QueryCraft Studio Team"}],creator:"QueryCraft Studio",openGraph:{type:"website",locale:"en_US",url:"https://querycraft.studio",title:"QueryCraft Studio",description:"AI-powered SQL development platform with multi-agent assistance",siteName:"QueryCraft Studio",images:[{url:"/og-image.png",width:1200,height:630,alt:"QueryCraft Studio"}]},twitter:{card:"summary_large_image",title:"QueryCraft Studio",description:"AI-powered SQL development platform with multi-agent assistance",images:["/og-image.png"],creator:"@querycraft"},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}},manifest:"/manifest.json",icons:{icon:"/favicon.ico",shortcut:"/favicon-16x16.png",apple:"/apple-touch-icon.png"}};function v({children:e}){return(0,a.jsxs)("html",{lang:"en",suppressHydrationWarning:!0,children:[(0,a.jsxs)("head",{children:[a.jsx("meta",{charSet:"utf-8"}),a.jsx("meta",{name:"viewport",content:"width=device-width, initial-scale=1"}),a.jsx("meta",{name:"theme-color",content:"#3b82f6"}),a.jsx("link",{rel:"preconnect",href:"https://fonts.googleapis.com"}),a.jsx("link",{rel:"preconnect",href:"https://fonts.gstatic.com",crossOrigin:""})]}),a.jsx("body",{className:`${o().variable} ${n().variable} font-sans antialiased`,suppressHydrationWarning:!0,children:(0,a.jsxs)(m,{children:[a.jsx("div",{className:"relative flex min-h-screen flex-col",children:a.jsx("div",{className:"flex-1",children:e})}),a.jsx(g,{})]})})]})}},5023:()=>{}};