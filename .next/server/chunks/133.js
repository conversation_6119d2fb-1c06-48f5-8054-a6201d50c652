"use strict";exports.id=133,exports.ids=[133],exports.modules={67133:(e,t,a)=>{a.d(t,{QueryService:()=>s});var r=a(68505);class s{static async createSession(e){try{return await r._B.querySession.create({data:{...e,sessionData:e.sessionData||{}}})}catch(e){(0,r.Lb)(e)}}static async getSessionById(e){try{return await r._B.querySession.findUnique({where:{id:e},include:{user:{select:{id:!0,name:!0,email:!0}},databaseConnection:{select:{id:!0,name:!0,databaseType:!0}},generatedQueries:{orderBy:{createdAt:"desc"},take:10},chatMessages:{orderBy:{createdAt:"asc"}}}})}catch(e){(0,r.Lb)(e)}}static async getUserSessions(e,t=20){try{return await r._B.querySession.findMany({where:{userId:e,isActive:!0},include:{databaseConnection:{select:{id:!0,name:!0,databaseType:!0}},_count:{select:{generatedQueries:!0,chatMessages:!0}}},orderBy:{updatedAt:"desc"},take:t})}catch(e){(0,r.Lb)(e)}}static async createGeneratedQuery(e){try{return await r._B.generatedQuery.create({data:e})}catch(e){(0,r.Lb)(e)}}static async updateQueryExecution(e,t){try{return await r._B.generatedQuery.update({where:{id:e},data:t})}catch(e){(0,r.Lb)(e)}}static async addQueryFeedback(e,t){try{return await r._B.generatedQuery.update({where:{id:e},data:{userFeedback:t}})}catch(e){(0,r.Lb)(e)}}static async getUserQueryHistory(e,t=50,a){try{return await r._B.generatedQuery.findMany({where:{userId:e,...a&&{databaseType:a}},include:{session:{select:{id:!0,name:!0,databaseConnection:{select:{id:!0,name:!0}}}}},orderBy:{createdAt:"desc"},take:t})}catch(e){(0,r.Lb)(e)}}static async addChatMessage(e){try{return await r._B.chatMessage.create({data:e})}catch(e){(0,r.Lb)(e)}}static async getSessionMessages(e){try{return await r._B.chatMessage.findMany({where:{sessionId:e},orderBy:{createdAt:"asc"}})}catch(e){(0,r.Lb)(e)}}static async updateSession(e,t){try{return await r._B.querySession.update({where:{id:e},data:t})}catch(e){(0,r.Lb)(e)}}static async deleteSession(e){try{await r._B.querySession.delete({where:{id:e}})}catch(e){(0,r.Lb)(e)}}static async getQueryAnalytics(e,t=30){try{let a=new Date(Date.now()-864e5*t),[s,c,n,i,d]=await Promise.all([r._B.generatedQuery.count({where:{userId:e,createdAt:{gte:a}}}),r._B.generatedQuery.count({where:{userId:e,status:"EXECUTED",createdAt:{gte:a}}}),r._B.generatedQuery.aggregate({where:{userId:e,executionTime:{not:null},createdAt:{gte:a}},_avg:{executionTime:!0}}),r._B.generatedQuery.groupBy({by:["databaseType"],where:{userId:e,createdAt:{gte:a}},_count:{id:!0},orderBy:{_count:{id:"desc"}}}),r._B.$queryRaw`
          SELECT 
            DATE(created_at) as date,
            COUNT(*) as count
          FROM generated_queries 
          WHERE user_id = ${e} 
            AND created_at >= ${a}
          GROUP BY DATE(created_at)
          ORDER BY date DESC
        `]);return{totalQueries:s,successfulQueries:c,successRate:s>0?c/s*100:0,averageExecutionTime:n._avg.executionTime||0,topDatabases:i,dailyActivity:d}}catch(e){(0,r.Lb)(e)}}}}};