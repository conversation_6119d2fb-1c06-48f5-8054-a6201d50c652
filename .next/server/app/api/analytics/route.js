"use strict";(()=>{var e={};e.id=567,e.ids=[567],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27086:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>T,originalPathname:()=>Q,patchFetch:()=>E,requestAsyncStorage:()=>S,routeModule:()=>x,serverHooks:()=>I,staticGenerationAsyncStorage:()=>b,staticGenerationBailout:()=>q});var s={};r.r(s),r.d(s,{GET:()=>y,POST:()=>m});var a=r(95419),o=r(69108),i=r(99678),n=r(78070),c=r(68505);class u{static async collectQueryMetrics(e){try{let t=this.analyzeSQLQuery(e.sql),r=this.generateQueryHash(e.sql),s={userId:e.userId,connectionId:e.connectionId,queryHash:r,queryType:t.type,executionTime:e.executionTime,rowsAffected:e.result.data?.affectedRows||0,rowsReturned:e.result.data?.rowCount||0,success:e.result.success,errorMessage:e.result.error,complexity:t.complexity,timestamp:new Date,sqlLength:e.sql.length,hasJoins:t.hasJoins,hasSubqueries:t.hasSubqueries,hasAggregations:t.hasAggregations,tablesAccessed:t.tablesAccessed};await this.storeQueryMetrics(s),await this.checkPerformanceAlerts(s)}catch(e){console.error("Failed to collect query metrics:",e)}}static async collectUsageMetrics(e,t){try{let r=new Date(t);r.setHours(0,0,0,0);let s=new Date(t);s.setHours(23,59,59,999);let a=await this.getQueryStatsForPeriod(e,r,s),o=await this.getCollaborationStatsForPeriod(e,r,s),i={userId:e,date:t,queriesExecuted:a.total,successfulQueries:a.successful,failedQueries:a.failed,totalExecutionTime:a.totalExecutionTime,avgExecutionTime:a.avgExecutionTime,connectionsUsed:a.uniqueConnections,collaborationSessions:o.sessions,chatMessages:o.messages};return await this.storeUsageMetrics(i),i}catch(e){throw console.error("Failed to collect usage metrics:",e),e}}static analyzeSQLQuery(e){let t=e.toLowerCase().trim(),r="UNKNOWN";t.startsWith("select")?r="SELECT":t.startsWith("insert")?r="INSERT":t.startsWith("update")?r="UPDATE":t.startsWith("delete")?r="DELETE":t.startsWith("create")?r="CREATE":t.startsWith("alter")?r="ALTER":t.startsWith("drop")&&(r="DROP");let s=/\b(join|inner join|left join|right join|full join)\b/i.test(e),a=/\b(select\b.*\bselect\b|\bexists\b|\bin\s*\()/i.test(e),o=/\b(count|sum|avg|min|max|group by|having)\b/i.test(e),i=/\bover\s*\(/i.test(e),n=/\bunion\b/i.test(e),c=0;s&&(c+=1),a&&(c+=2),o&&(c+=1),i&&(c+=2),n&&(c+=1);let u="LOW";c>=4?u="HIGH":c>=2&&(u="MEDIUM");let l=e.match(/(?:from|join|update|into)\s+([a-zA-Z_][a-zA-Z0-9_]*)/gi);return{type:r,complexity:u,hasJoins:s,hasSubqueries:a,hasAggregations:o,tablesAccessed:l?Array.from(new Set(l.map(e=>e.split(/\s+/).pop()?.toLowerCase()||""))):[]}}static generateQueryHash(e){let t=e.toLowerCase().replace(/\s+/g," ").replace(/'/g,'"').trim(),r=0;for(let e=0;e<t.length;e++)r=(r<<5)-r+t.charCodeAt(e),r&=r;return Math.abs(r).toString(16)}static async storeQueryMetrics(e){try{await c._B.generatedQuery.create({data:{sessionId:"metrics-session",userId:e.userId,userInput:`Query executed at ${e.timestamp.toISOString()}`,generatedSQL:"",explanation:`Query metrics: ${e.queryType}, ${e.complexity} complexity`,databaseType:"POSTGRESQL",executionTime:e.executionTime,rowsAffected:e.rowsAffected,performanceData:JSON.stringify({queryHash:e.queryHash,queryType:e.queryType,complexity:e.complexity,rowsReturned:e.rowsReturned,success:e.success,sqlLength:e.sqlLength,hasJoins:e.hasJoins,hasSubqueries:e.hasSubqueries,hasAggregations:e.hasAggregations,tablesAccessed:e.tablesAccessed,timestamp:e.timestamp.toISOString()})}})}catch(e){console.error("Failed to store query metrics:",e)}}static async storeUsageMetrics(e){console.log("Usage metrics collected:",e)}static async getQueryStatsForPeriod(e,t,r){try{let s=await c._B.generatedQuery.findMany({where:{userId:e,createdAt:{gte:t,lte:r}},select:{executionTime:!0,performanceData:!0}}),a=s.length,o=s.filter(e=>{if(!e.performanceData||"string"!=typeof e.performanceData)return!1;try{let t=JSON.parse(e.performanceData);return!0===t.success}catch{return!1}}).length,i=s.reduce((e,t)=>e+(t.executionTime||0),0);return{total:a,successful:o,failed:a-o,totalExecutionTime:i,avgExecutionTime:a>0?i/a:0,uniqueConnections:1}}catch(e){return console.error("Failed to get query stats:",e),{total:0,successful:0,failed:0,totalExecutionTime:0,avgExecutionTime:0,uniqueConnections:0}}}static async getCollaborationStatsForPeriod(e,t,r){return{sessions:0,messages:0}}static async checkPerformanceAlerts(e){let t=[];for(let r of(e.executionTime>5e3&&t.push({type:"SLOW_QUERY",severity:e.executionTime>3e4?"CRITICAL":"HIGH",title:"Slow Query Detected",description:`Query took ${e.executionTime}ms to execute`,queryHash:e.queryHash,userId:e.userId,connectionId:e.connectionId,threshold:5e3,actualValue:e.executionTime,timestamp:new Date,resolved:!1}),"HIGH"===e.complexity&&e.executionTime>1e3&&t.push({type:"RESOURCE_USAGE",severity:"MEDIUM",title:"Complex Query Performance",description:"High complexity query with significant execution time",queryHash:e.queryHash,userId:e.userId,connectionId:e.connectionId,threshold:1e3,actualValue:e.executionTime,timestamp:new Date,resolved:!1}),t))await this.storeAlert(r)}static async storeAlert(e){console.log("Performance alert:",e)}static async getQueryPerformanceStats(e,t=7){let r=new Date,s=new Date;s.setDate(s.getDate()-t);let a=await this.getQueryStatsForPeriod(e,s,r);return{avgExecutionTime:a.avgExecutionTime,slowQueries:0,totalQueries:a.total,errorRate:a.total>0?a.failed/a.total*100:0}}static async getTopSlowQueries(e,t=10){return[]}static async getUsageTrends(e,t=30){return[]}}var l=r(88302);let d=l.z.object({userId:l.z.string().cuid(),type:l.z.enum(["performance","usage","slow-queries","trends","alerts"]),days:l.z.number().int().min(1).max(365).optional().default(7),limit:l.z.number().int().min(1).max(100).optional().default(10)});async function y(e){try{let t;let{searchParams:r}=new URL(e.url),s=r.get("userId"),a=r.get("type"),o=parseInt(r.get("days")||"7"),i=parseInt(r.get("limit")||"10");if(!s||!a)return n.Z.json({error:"userId and type parameters are required"},{status:400});let c=d.parse({userId:s,type:a,days:o,limit:i});switch(c.type){case"performance":t=await u.getQueryPerformanceStats(c.userId,c.days);break;case"slow-queries":t=await u.getTopSlowQueries(c.userId,c.limit);break;case"trends":t=await u.getUsageTrends(c.userId,c.days);break;case"usage":t=await g(c.userId,c.days);break;case"alerts":t=await p(c.userId);break;default:return n.Z.json({error:"Invalid analytics type"},{status:400})}return n.Z.json({type:c.type,data:t,period:{days:c.days,limit:c.limit},timestamp:new Date().toISOString()})}catch(e){if(e instanceof l.z.ZodError)return n.Z.json({error:"Validation failed",details:e.errors},{status:400});return console.error("GET /api/analytics error:",e),n.Z.json({error:"Internal server error"},{status:500})}}async function m(e){try{let t=await e.json(),r=l.z.object({type:l.z.enum(["query_execution","collaboration_session","user_action"]),userId:l.z.string().cuid(),data:l.z.object({}).passthrough()}).parse(t);switch(r.type){case"query_execution":await h(r.userId,r.data);break;case"collaboration_session":await f(r.userId,r.data);break;case"user_action":await w(r.userId,r.data)}return n.Z.json({success:!0,message:"Analytics event recorded",timestamp:new Date().toISOString()})}catch(e){if(e instanceof l.z.ZodError)return n.Z.json({error:"Validation failed",details:e.errors},{status:400});return console.error("POST /api/analytics error:",e),n.Z.json({error:"Internal server error"},{status:500})}}async function g(e,t){try{let r;let s=new Date,a=new Date;a.setDate(a.getDate()-t);let o=await u.getQueryPerformanceStats(e,t);return{totalQueries:o.totalQueries,avgExecutionTime:o.avgExecutionTime,errorRate:o.errorRate,slowQueries:o.slowQueries,queriesPerDay:o.totalQueries/t,performanceScore:(r=100,o.avgExecutionTime>1e3&&(r-=Math.min(30,(o.avgExecutionTime-1e3)/100)),r-=2*o.errorRate,o.totalQueries>0&&(r-=o.slowQueries/o.totalQueries*20),Math.max(0,Math.round(r))),period:{start:a.toISOString(),end:s.toISOString(),days:t}}}catch(e){return console.error("Failed to get usage overview:",e),{totalQueries:0,avgExecutionTime:0,errorRate:0,slowQueries:0,queriesPerDay:0,performanceScore:0,period:{start:new Date().toISOString(),end:new Date().toISOString(),days:t}}}}async function p(e){return[{id:"alert-1",type:"SLOW_QUERY",severity:"HIGH",title:"Slow Query Detected",description:"Query execution time exceeded 5 seconds",timestamp:new Date().toISOString(),resolved:!1}]}async function h(e,t){try{let r=l.z.object({connectionId:l.z.string(),sql:l.z.string(),result:l.z.object({}).passthrough(),executionTime:l.z.number()}).parse(t);await u.collectQueryMetrics({userId:e,connectionId:r.connectionId,sql:r.sql,result:r.result,executionTime:r.executionTime})}catch(e){console.error("Failed to handle query execution event:",e)}}async function f(e,t){try{console.log("Collaboration event:",{userId:e,data:t})}catch(e){console.error("Failed to handle collaboration event:",e)}}async function w(e,t){try{console.log("User action event:",{userId:e,data:t})}catch(e){console.error("Failed to handle user action event:",e)}}let x=new a.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/analytics/route",pathname:"/api/analytics",filename:"route",bundlePath:"app/api/analytics/route"},resolvedPagePath:"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/app/api/analytics/route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:S,staticGenerationAsyncStorage:b,serverHooks:I,headerHooks:T,staticGenerationBailout:q}=x,Q="/api/analytics/route";function E(){return(0,i.patchFetch)({serverHooks:I,staticGenerationAsyncStorage:b})}},68505:(e,t,r)=>{r.d(t,{Lb:()=>o,_B:()=>a});let s=require("@prisma/client"),a=globalThis.prisma??new s.PrismaClient({log:["query","error","warn"],errorFormat:"pretty"});function o(e){if(console.error("Database error:",e),"P2002"===e.code)throw Error("A record with this information already exists");if("P2025"===e.code)throw Error("Record not found");if("P2003"===e.code)throw Error("Foreign key constraint failed");throw Error("Database operation failed")}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[638,543],()=>r(27086));module.exports=s})();