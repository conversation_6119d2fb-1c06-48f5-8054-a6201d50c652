(()=>{var e={};e.id=591,e.ids=[591],e.modules={30517:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},14300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},41808:e=>{"use strict";e.exports=require("net")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},63477:e=>{"use strict";e.exports=require("querystring")},12781:e=>{"use strict";e.exports=require("stream")},39512:e=>{"use strict";e.exports=require("timers")},24404:e=>{"use strict";e.exports=require("tls")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},39697:()=>{},62302:()=>{},30107:()=>{},47524:()=>{},36093:(e,t,s)=>{"use strict";let i;s.r(t),s.d(t,{headerHooks:()=>y,originalPathname:()=>w,patchFetch:()=>k,requestAsyncStorage:()=>x,routeModule:()=>v,serverHooks:()=>b,staticGenerationAsyncStorage:()=>g,staticGenerationBailout:()=>_});var a={};s.r(a),s.d(a,{GET:()=>h,POST:()=>f,dynamic:()=>m});var n=s(95419),o=s(69108),r=s(99678);let{Server:c,Namespace:p,Socket:l}=s(26371);class u{constructor(e){this.sessions=new Map,this.userColors=["#FF6B6B","#4ECDC4","#45B7D1","#96CEB4","#FFEAA7","#DDA0DD","#98D8C8","#F7DC6F","#BB8FCE","#85C1E9"],this.io=new c(e,{cors:{origin:process.env.NEXTAUTH_URL||"http://localhost:3000",methods:["GET","POST"]},path:"/api/socket"}),this.setupEventHandlers(),this.startCleanupInterval()}setupEventHandlers(){this.io.on("connection",e=>{console.log(`Client connected: ${e.id}`),e.on("authenticate",async t=>{try{let s=await this.authenticateUser(t);s?(e.data.user=s,e.emit("authenticated",{success:!0,user:s})):(e.emit("authenticated",{success:!1,error:"Invalid token"}),e.disconnect())}catch(t){e.emit("authenticated",{success:!1,error:"Authentication failed"}),e.disconnect()}}),e.on("join_session",t=>{this.handleJoinSession(e,t)}),e.on("leave_session",t=>{this.handleLeaveSession(e,t)}),e.on("sql_change",t=>{this.handleSqlChange(e,t)}),e.on("cursor_update",t=>{this.handleCursorUpdate(e,t)}),e.on("chat_message",t=>{this.handleChatMessage(e,t)}),e.on("query_execution_start",t=>{this.handleQueryExecutionStart(e,t)}),e.on("query_execution_complete",t=>{this.handleQueryExecutionComplete(e,t)}),e.on("disconnect",()=>{this.handleDisconnect(e)})})}async authenticateUser(e){try{return{id:`user_${Date.now()}`,name:"Demo User",email:"<EMAIL>",color:this.getRandomColor(),socketId:"",joinedAt:new Date,isActive:!0}}catch(e){return null}}handleJoinSession(e,t){let{sessionId:s,connectionId:i,sessionName:a}=t,n=e.data.user;if(!n){e.emit("error",{message:"Not authenticated"});return}let o=this.sessions.get(s);o||(o={id:s,name:a||`Session ${s.slice(0,8)}`,connectionId:i,ownerId:n.id,participants:new Map,sqlContent:"",cursors:new Map,createdAt:new Date,lastActivity:new Date},this.sessions.set(s,o)),n.socketId=e.id,n.color=this.getRandomColor(),o.participants.set(n.id,n),o.lastActivity=new Date,e.join(s),e.data.sessionId=s,e.emit("session_joined",{session:this.serializeSession(o),user:n,participants:Array.from(o.participants.values())}),e.to(s).emit("user_joined",{user:n,participants:Array.from(o.participants.values())}),o.sqlContent&&e.emit("sql_sync",{content:o.sqlContent}),console.log(`User ${n.name} joined session ${s}`)}handleLeaveSession(e,t){let s=this.sessions.get(t),i=e.data.user;s&&i&&(s.participants.delete(i.id),s.cursors.delete(i.id),e.leave(t),e.data.sessionId=null,e.to(t).emit("user_left",{userId:i.id,participants:Array.from(s.participants.values())}),0===s.participants.size&&(this.sessions.delete(t),console.log(`Session ${t} cleaned up (no participants)`)),console.log(`User ${i.name} left session ${t}`))}handleSqlChange(e,t){let s=this.sessions.get(t.sessionId),i=e.data.user;s&&i&&(s.sqlContent=t.content,s.lastActivity=new Date,e.to(t.sessionId).emit("sql_change",{content:t.content,operation:t.operation,userId:i.id,userName:i.name}))}handleCursorUpdate(e,t){let s=this.sessions.get(t.sessionId),i=e.data.user;s&&i&&(t.position.userId=i.id,s.cursors.set(i.id,t.position),e.to(t.sessionId).emit("cursor_update",{position:t.position,user:{id:i.id,name:i.name,color:i.color}}))}handleChatMessage(e,t){let s=this.sessions.get(t.sessionId),i=e.data.user;if(!s||!i)return;let a={id:`msg_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,userId:i.id,userName:i.name,message:t.message,timestamp:new Date,type:"message"};this.io.to(t.sessionId).emit("chat_message",a)}handleQueryExecutionStart(e,t){let s=e.data.user;if(!s)return;let i={id:`msg_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,userId:"system",userName:"System",message:`${s.name} started executing a query`,timestamp:new Date,type:"system"};this.io.to(t.sessionId).emit("query_execution_start",{userId:s.id,userName:s.name,sql:t.sql}),this.io.to(t.sessionId).emit("chat_message",i)}handleQueryExecutionComplete(e,t){let s=e.data.user;if(!s)return;let i=t.result.success?`Query completed successfully (${t.result.metadata?.rowsReturned||0} rows, ${t.result.metadata?.executionTime||0}ms)`:`Query failed: ${t.result.error}`,a={id:`msg_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,userId:"system",userName:"System",message:`${s.name}: ${i}`,timestamp:new Date,type:"query_execution"};this.io.to(t.sessionId).emit("query_execution_complete",{userId:s.id,userName:s.name,result:t.result}),this.io.to(t.sessionId).emit("chat_message",a)}handleDisconnect(e){let t=e.data.user,s=e.data.sessionId;s&&t&&this.handleLeaveSession(e,s),console.log(`Client disconnected: ${e.id}`)}getRandomColor(){return this.userColors[Math.floor(Math.random()*this.userColors.length)]||"#FF6B6B"}serializeSession(e){return{id:e.id,name:e.name,connectionId:e.connectionId,ownerId:e.ownerId,participantCount:e.participants.size,createdAt:e.createdAt,lastActivity:e.lastActivity}}startCleanupInterval(){setInterval(()=>{let e=new Date;Array.from(this.sessions.entries()).forEach(([t,s])=>{e.getTime()-s.lastActivity.getTime()>18e5&&(this.sessions.delete(t),console.log(`Cleaned up inactive session: ${t}`))})},3e5)}getActiveSessions(){return Array.from(this.sessions.values())}getSessionById(e){return this.sessions.get(e)}getSessionParticipants(e){let t=this.sessions.get(e);return t?Array.from(t.participants.values()):[]}}let d=null,m="force-dynamic";async function h(e){if(!i){console.log("Initializing Socket.IO server...");let e=global.httpServer;if(!e)return new Response("HTTP server not available",{status:500});i=(d||(d=new u(e)),d).io,console.log("Socket.IO server initialized")}return new Response("Socket.IO server is running",{status:200})}async function f(e){return new Response("WebSocket connections should use the Socket.IO client",{status:400})}let v=new n.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/socket/route",pathname:"/api/socket",filename:"route",bundlePath:"app/api/socket/route"},resolvedPagePath:"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/app/api/socket/route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:x,staticGenerationAsyncStorage:g,serverHooks:b,headerHooks:y,staticGenerationBailout:_}=v,w="/api/socket/route";function k(){return(0,r.patchFetch)({serverHooks:b,staticGenerationAsyncStorage:g})}},10523:(e,t,s)=>{"use strict";/*!
 * accepts
 * Copyright(c) 2014 Jonathan Ong
 * Copyright(c) 2015 Douglas Christopher Wilson
 * MIT Licensed
 */var i=s(73685),a=s(78813);function n(e){if(!(this instanceof n))return new n(e);this.headers=e.headers,this.negotiator=new i(e)}function o(e){return -1===e.indexOf("/")?a.lookup(e):e}function r(e){return"string"==typeof e}e.exports=n,n.prototype.type=n.prototype.types=function(e){var t=e;if(t&&!Array.isArray(t)){t=Array(arguments.length);for(var s=0;s<t.length;s++)t[s]=arguments[s]}if(!t||0===t.length)return this.negotiator.mediaTypes();if(!this.headers.accept)return t[0];var i=t.map(o),a=this.negotiator.mediaTypes(i.filter(r))[0];return!!a&&t[i.indexOf(a)]},n.prototype.encoding=n.prototype.encodings=function(e){var t=e;if(t&&!Array.isArray(t)){t=Array(arguments.length);for(var s=0;s<t.length;s++)t[s]=arguments[s]}return t&&0!==t.length?this.negotiator.encodings(t)[0]||!1:this.negotiator.encodings()},n.prototype.charset=n.prototype.charsets=function(e){var t=e;if(t&&!Array.isArray(t)){t=Array(arguments.length);for(var s=0;s<t.length;s++)t[s]=arguments[s]}return t&&0!==t.length?this.negotiator.charsets(t)[0]||!1:this.negotiator.charsets()},n.prototype.lang=n.prototype.langs=n.prototype.language=n.prototype.languages=function(e){var t=e;if(t&&!Array.isArray(t)){t=Array(arguments.length);for(var s=0;s<t.length;s++)t[s]=arguments[s]}return t&&0!==t.length?this.negotiator.languages(t)[0]||!1:this.negotiator.languages()}},49324:(e,t,s)=>{/*!
 * base64id v0.1.0
 */var i=s(6113),a=function(){};a.prototype.getRandomBytes=function(e){var t=this;if((e=e||12)>4096)return i.randomBytes(e);var s=parseInt(4096/e),a=parseInt(.85*s);if(!a||(null==this.bytesBufferIndex&&(this.bytesBufferIndex=-1),this.bytesBufferIndex==s&&(this.bytesBuffer=null,this.bytesBufferIndex=-1),(-1==this.bytesBufferIndex||this.bytesBufferIndex>a)&&(this.isGeneratingBytes||(this.isGeneratingBytes=!0,i.randomBytes(4096,function(e,s){t.bytesBuffer=s,t.bytesBufferIndex=0,t.isGeneratingBytes=!1})),-1==this.bytesBufferIndex)))return i.randomBytes(e);var n=this.bytesBuffer.slice(e*this.bytesBufferIndex,e*(this.bytesBufferIndex+1));return this.bytesBufferIndex++,n},a.prototype.generateId=function(){var e=Buffer.alloc(15);return e.writeInt32BE?(this.sequenceNumber=this.sequenceNumber+1|0,e.writeInt32BE(this.sequenceNumber,11),i.randomBytes?this.getRandomBytes(12).copy(e):[0,4,8].forEach(function(t){e.writeInt32BE(***********Math.random()|0,t)}),e.toString("base64").replace(/\//g,"_").replace(/\+/g,"-")):Math.abs(Math.random()*Math.random()*Date.now()|0).toString()+Math.abs(Math.random()*Math.random()*Date.now()|0).toString()},e.exports=new a},83191:(e,t)=>{"use strict";/*!
 * cookie
 * Copyright(c) 2012-2014 Roman Shtylman
 * Copyright(c) 2015 Douglas Christopher Wilson
 * MIT Licensed
 */t.parse=function(e,t){if("string"!=typeof e)throw TypeError("argument str must be a string");var s={},a=e.length;if(a<2)return s;var n=t&&t.decode||l,o=0,r=0,u=0;do{if(-1===(r=e.indexOf("=",o)))break;if(-1===(u=e.indexOf(";",o)))u=a;else if(r>u){o=e.lastIndexOf(";",r-1)+1;continue}var d=c(e,o,r),m=p(e,r,d),h=e.slice(d,m);if(!i.call(s,h)){var f=c(e,r+1,u),v=p(e,u,f);34===e.charCodeAt(f)&&34===e.charCodeAt(v-1)&&(f++,v--);var x=e.slice(f,v);s[h]=function(e,t){try{return t(e)}catch(t){return e}}(x,n)}o=u+1}while(o<a);return s},t.serialize=function(e,t,i){var c=i&&i.encode||encodeURIComponent;if("function"!=typeof c)throw TypeError("option encode is invalid");if(!a.test(e))throw TypeError("argument name is invalid");var p=c(t);if(!n.test(p))throw TypeError("argument val is invalid");var l=e+"="+p;if(!i)return l;if(null!=i.maxAge){var u=Math.floor(i.maxAge);if(!isFinite(u))throw TypeError("option maxAge is invalid");l+="; Max-Age="+u}if(i.domain){if(!o.test(i.domain))throw TypeError("option domain is invalid");l+="; Domain="+i.domain}if(i.path){if(!r.test(i.path))throw TypeError("option path is invalid");l+="; Path="+i.path}if(i.expires){var d=i.expires;if("[object Date]"!==s.call(d)||isNaN(d.valueOf()))throw TypeError("option expires is invalid");l+="; Expires="+d.toUTCString()}if(i.httpOnly&&(l+="; HttpOnly"),i.secure&&(l+="; Secure"),i.partitioned&&(l+="; Partitioned"),i.priority)switch("string"==typeof i.priority?i.priority.toLowerCase():i.priority){case"low":l+="; Priority=Low";break;case"medium":l+="; Priority=Medium";break;case"high":l+="; Priority=High";break;default:throw TypeError("option priority is invalid")}if(i.sameSite)switch("string"==typeof i.sameSite?i.sameSite.toLowerCase():i.sameSite){case!0:case"strict":l+="; SameSite=Strict";break;case"lax":l+="; SameSite=Lax";break;case"none":l+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return l};var s=Object.prototype.toString,i=Object.prototype.hasOwnProperty,a=/^[!#$%&'*+\-.^_`|~0-9A-Za-z]+$/,n=/^("?)[\u0021\u0023-\u002B\u002D-\u003A\u003C-\u005B\u005D-\u007E]*\1$/,o=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,r=/^[\u0020-\u003A\u003D-\u007E]*$/;function c(e,t,s){do{var i=e.charCodeAt(t);if(32!==i&&9!==i)return t}while(++t<s);return s}function p(e,t,s){for(;t>s;){var i=e.charCodeAt(--t);if(32!==i&&9!==i)return t+1}return s}function l(e){return -1!==e.indexOf("%")?decodeURIComponent(e):e}},79879:(e,t,s)=>{!function(){"use strict";var t=s(9335),i=s(15198),a={origin:"*",methods:"GET,HEAD,PUT,PATCH,POST,DELETE",preflightContinue:!1,optionsSuccessStatus:204};function n(e){return"string"==typeof e||e instanceof String}function o(e,t){var s=t.headers.origin,i=[];return e.origin&&"*"!==e.origin?(n(e.origin)?i.push([{key:"Access-Control-Allow-Origin",value:e.origin}]):i.push([{key:"Access-Control-Allow-Origin",value:!!function e(t,s){if(Array.isArray(s)){for(var i=0;i<s.length;++i)if(e(t,s[i]))return!0;return!1}return n(s)?t===s:s instanceof RegExp?s.test(t):!!s}(s,e.origin)&&s}]),i.push([{key:"Vary",value:"Origin"}])):i.push([{key:"Access-Control-Allow-Origin",value:"*"}]),i}function r(e){return!0===e.credentials?{key:"Access-Control-Allow-Credentials",value:"true"}:null}function c(e){var t=e.exposedHeaders;return t&&(t.join&&(t=t.join(",")),t&&t.length)?{key:"Access-Control-Expose-Headers",value:t}:null}function p(e,t){for(var s=0,a=e.length;s<a;s++){var n=e[s];n&&(Array.isArray(n)?p(n,t):"Vary"===n.key&&n.value?i(t,n.value):n.value&&t.setHeader(n.key,n.value))}}e.exports=function(e){var s=null;return s="function"==typeof e?e:function(t,s){s(null,e)},function(e,i,n){s(e,function(s,l){if(s)n(s);else{var u=t({},a,l),d=null;u.origin&&"function"==typeof u.origin?d=u.origin:u.origin&&(d=function(e,t){t(null,u.origin)}),d?d(e.headers.origin,function(t,s){var a,l,d,m,h,f,v,x,g;t||!s?n(t):(u.origin=s,a=u,l=e,d=i,m=n,g=[],"OPTIONS"===(l.method&&l.method.toUpperCase&&l.method.toUpperCase())?(g.push(o(a,l)),g.push(r(a,l)),g.push(((h=a.methods).join&&(h=a.methods.join(",")),{key:"Access-Control-Allow-Methods",value:h})),g.push((f=a.allowedHeaders||a.headers,v=[],f?f.join&&(f=f.join(",")):(f=l.headers["access-control-request-headers"],v.push([{key:"Vary",value:"Access-Control-Request-Headers"}])),f&&f.length&&v.push([{key:"Access-Control-Allow-Headers",value:f}]),v)),g.push((x=("number"==typeof a.maxAge||a.maxAge)&&a.maxAge.toString())&&x.length?{key:"Access-Control-Max-Age",value:x}:null),g.push(c(a,l)),p(g,d),a.preflightContinue?m():(d.statusCode=a.optionsSuccessStatus,d.setHeader("Content-Length","0"),d.end())):(g.push(o(a,l)),g.push(r(a,l)),g.push(c(a,l)),p(g,d),m()))}):n()}})}}}()},47751:(e,t,s)=>{t.formatArgs=function(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;let s="color: "+this.color;t.splice(1,0,s,"color: inherit");let i=0,a=0;t[0].replace(/%[a-zA-Z%]/g,e=>{"%%"!==e&&(i++,"%c"===e&&(a=i))}),t.splice(a,0,s)},t.save=function(e){try{e?t.storage.setItem("debug",e):t.storage.removeItem("debug")}catch(e){}},t.load=function(){let e;try{e=t.storage.getItem("debug")}catch(e){}return!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG),e},t.useColors=function(){let e;return!("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))&&("undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof navigator&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/))},t.storage=function(){try{return localStorage}catch(e){}}(),t.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.log=console.debug||console.log||(()=>{}),e.exports=s(14464)(t);let{formatters:i}=e.exports;i.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},14464:(e,t,s)=>{e.exports=function(e){function t(e){let s,a,n;let o=null;function r(...e){if(!r.enabled)return;let i=Number(new Date),a=i-(s||i);r.diff=a,r.prev=s,r.curr=i,s=i,e[0]=t.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let n=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,(s,i)=>{if("%%"===s)return"%";n++;let a=t.formatters[i];if("function"==typeof a){let t=e[n];s=a.call(r,t),e.splice(n,1),n--}return s}),t.formatArgs.call(r,e),(r.log||t.log).apply(r,e)}return r.namespace=e,r.useColors=t.useColors(),r.color=t.selectColor(e),r.extend=i,r.destroy=t.destroy,Object.defineProperty(r,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==o?o:(a!==t.namespaces&&(a=t.namespaces,n=t.enabled(e)),n),set:e=>{o=e}}),"function"==typeof t.init&&t.init(r),r}function i(e,s){let i=t(this.namespace+(void 0===s?":":s)+e);return i.log=this.log,i}function a(e){return e.toString().substring(2,e.toString().length-2).replace(/\.\*\?$/,"*")}return t.debug=t,t.default=t,t.coerce=function(e){return e instanceof Error?e.stack||e.message:e},t.disable=function(){let e=[...t.names.map(a),...t.skips.map(a).map(e=>"-"+e)].join(",");return t.enable(""),e},t.enable=function(e){let s;t.save(e),t.namespaces=e,t.names=[],t.skips=[];let i=("string"==typeof e?e:"").split(/[\s,]+/),a=i.length;for(s=0;s<a;s++)i[s]&&("-"===(e=i[s].replace(/\*/g,".*?"))[0]?t.skips.push(RegExp("^"+e.slice(1)+"$")):t.names.push(RegExp("^"+e+"$")))},t.enabled=function(e){let s,i;if("*"===e[e.length-1])return!0;for(s=0,i=t.skips.length;s<i;s++)if(t.skips[s].test(e))return!1;for(s=0,i=t.names.length;s<i;s++)if(t.names[s].test(e))return!0;return!1},t.humanize=s(87553),t.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach(s=>{t[s]=e[s]}),t.names=[],t.skips=[],t.formatters={},t.selectColor=function(e){let s=0;for(let t=0;t<e.length;t++)s=(s<<5)-s+e.charCodeAt(t)|0;return t.colors[Math.abs(s)%t.colors.length]},t.enable(t.load()),t}},34989:(e,t,s)=>{"undefined"==typeof process||"renderer"===process.type||process.__nwjs?e.exports=s(47751):e.exports=s(55262)},55262:(e,t,s)=>{let i=s(76224),a=s(73837);t.init=function(e){e.inspectOpts={};let s=Object.keys(t.inspectOpts);for(let i=0;i<s.length;i++)e.inspectOpts[s[i]]=t.inspectOpts[s[i]]},t.log=function(...e){return process.stderr.write(a.formatWithOptions(t.inspectOpts,...e)+"\n")},t.formatArgs=function(s){let{namespace:i,useColors:a}=this;if(a){let t=this.color,a="\x1b[3"+(t<8?t:"8;5;"+t),n=`  ${a};1m${i} \u001B[0m`;s[0]=n+s[0].split("\n").join("\n"+n),s.push(a+"m+"+e.exports.humanize(this.diff)+"\x1b[0m")}else s[0]=(t.inspectOpts.hideDate?"":new Date().toISOString()+" ")+i+" "+s[0]},t.save=function(e){e?process.env.DEBUG=e:delete process.env.DEBUG},t.load=function(){return process.env.DEBUG},t.useColors=function(){return"colors"in t.inspectOpts?!!t.inspectOpts.colors:i.isatty(process.stderr.fd)},t.destroy=a.deprecate(()=>{},"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."),t.colors=[6,2,3,4,5,1];try{let e=s(46052);e&&(e.stderr||e).level>=2&&(t.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221])}catch(e){}t.inspectOpts=Object.keys(process.env).filter(e=>/^debug_/i.test(e)).reduce((e,t)=>{let s=t.substring(6).toLowerCase().replace(/_([a-z])/g,(e,t)=>t.toUpperCase()),i=process.env[t];return i=!!/^(yes|on|true|enabled)$/i.test(i)||!/^(no|off|false|disabled)$/i.test(i)&&("null"===i?null:Number(i)),e[s]=i,e},{}),e.exports=s(14464)(t);let{formatters:n}=e.exports;n.o=function(e){return this.inspectOpts.colors=this.useColors,a.inspect(e,this.inspectOpts).split("\n").map(e=>e.trim()).join(" ")},n.O=function(e){return this.inspectOpts.colors=this.useColors,a.inspect(e,this.inspectOpts)}},1670:(e,t,s)=>{"use strict";let i=s(71968);i.createWebSocketStream=s(6264),i.Server=s(28734),i.Receiver=s(37363),i.Sender=s(58709),i.WebSocket=i,i.WebSocketServer=i.Server,e.exports=i},7855:(e,t,s)=>{"use strict";let{EMPTY_BUFFER:i}=s(69853),a=Buffer[Symbol.species];function n(e,t,s,i,a){for(let n=0;n<a;n++)s[i+n]=e[n]^t[3&n]}function o(e,t){for(let s=0;s<e.length;s++)e[s]^=t[3&s]}if(e.exports={concat:function(e,t){if(0===e.length)return i;if(1===e.length)return e[0];let s=Buffer.allocUnsafe(t),n=0;for(let t=0;t<e.length;t++){let i=e[t];s.set(i,n),n+=i.length}return n<t?new a(s.buffer,s.byteOffset,n):s},mask:n,toArrayBuffer:function(e){return e.length===e.buffer.byteLength?e.buffer:e.buffer.slice(e.byteOffset,e.byteOffset+e.length)},toBuffer:function e(t){let s;return(e.readOnly=!0,Buffer.isBuffer(t))?t:(t instanceof ArrayBuffer?s=new a(t):ArrayBuffer.isView(t)?s=new a(t.buffer,t.byteOffset,t.byteLength):(s=Buffer.from(t),e.readOnly=!1),s)},unmask:o},!process.env.WS_NO_BUFFER_UTIL)try{let t=s(39697);e.exports.mask=function(e,s,i,a,o){o<48?n(e,s,i,a,o):t.mask(e,s,i,a,o)},e.exports.unmask=function(e,s){e.length<32?o(e,s):t.unmask(e,s)}}catch(e){}},69853:e=>{"use strict";e.exports={BINARY_TYPES:["nodebuffer","arraybuffer","fragments"],EMPTY_BUFFER:Buffer.alloc(0),GUID:"258EAFA5-E914-47DA-95CA-C5AB0DC85B11",kForOnEventAttribute:Symbol("kIsForOnEventAttribute"),kListener:Symbol("kListener"),kStatusCode:Symbol("status-code"),kWebSocket:Symbol("websocket"),NOOP:()=>{}}},76628:(e,t,s)=>{"use strict";let{kForOnEventAttribute:i,kListener:a}=s(69853),n=Symbol("kCode"),o=Symbol("kData"),r=Symbol("kError"),c=Symbol("kMessage"),p=Symbol("kReason"),l=Symbol("kTarget"),u=Symbol("kType"),d=Symbol("kWasClean");class m{constructor(e){this[l]=null,this[u]=e}get target(){return this[l]}get type(){return this[u]}}Object.defineProperty(m.prototype,"target",{enumerable:!0}),Object.defineProperty(m.prototype,"type",{enumerable:!0});class h extends m{constructor(e,t={}){super(e),this[n]=void 0===t.code?0:t.code,this[p]=void 0===t.reason?"":t.reason,this[d]=void 0!==t.wasClean&&t.wasClean}get code(){return this[n]}get reason(){return this[p]}get wasClean(){return this[d]}}Object.defineProperty(h.prototype,"code",{enumerable:!0}),Object.defineProperty(h.prototype,"reason",{enumerable:!0}),Object.defineProperty(h.prototype,"wasClean",{enumerable:!0});class f extends m{constructor(e,t={}){super(e),this[r]=void 0===t.error?null:t.error,this[c]=void 0===t.message?"":t.message}get error(){return this[r]}get message(){return this[c]}}Object.defineProperty(f.prototype,"error",{enumerable:!0}),Object.defineProperty(f.prototype,"message",{enumerable:!0});class v extends m{constructor(e,t={}){super(e),this[o]=void 0===t.data?null:t.data}get data(){return this[o]}}function x(e,t,s){"object"==typeof e&&e.handleEvent?e.handleEvent.call(e,s):e.call(t,s)}Object.defineProperty(v.prototype,"data",{enumerable:!0}),e.exports={CloseEvent:h,ErrorEvent:f,Event:m,EventTarget:{addEventListener(e,t,s={}){let n;for(let n of this.listeners(e))if(!s[i]&&n[a]===t&&!n[i])return;if("message"===e)n=function(e,s){let i=new v("message",{data:s?e:e.toString()});i[l]=this,x(t,this,i)};else if("close"===e)n=function(e,s){let i=new h("close",{code:e,reason:s.toString(),wasClean:this._closeFrameReceived&&this._closeFrameSent});i[l]=this,x(t,this,i)};else if("error"===e)n=function(e){let s=new f("error",{error:e,message:e.message});s[l]=this,x(t,this,s)};else{if("open"!==e)return;n=function(){let e=new m("open");e[l]=this,x(t,this,e)}}n[i]=!!s[i],n[a]=t,s.once?this.once(e,n):this.on(e,n)},removeEventListener(e,t){for(let s of this.listeners(e))if(s[a]===t&&!s[i]){this.removeListener(e,s);break}}},MessageEvent:v}},48102:(e,t,s)=>{"use strict";let{tokenChars:i}=s(73030);function a(e,t,s){void 0===e[t]?e[t]=[s]:e[t].push(s)}e.exports={format:function(e){return Object.keys(e).map(t=>{let s=e[t];return Array.isArray(s)||(s=[s]),s.map(e=>[t].concat(Object.keys(e).map(t=>{let s=e[t];return Array.isArray(s)||(s=[s]),s.map(e=>!0===e?t:`${t}=${e}`).join("; ")})).join("; ")).join(", ")}).join(", ")},parse:function(e){let t,s;let n=Object.create(null),o=Object.create(null),r=!1,c=!1,p=!1,l=-1,u=-1,d=-1,m=0;for(;m<e.length;m++)if(u=e.charCodeAt(m),void 0===t){if(-1===d&&1===i[u])-1===l&&(l=m);else if(0!==m&&(32===u||9===u))-1===d&&-1!==l&&(d=m);else if(59===u||44===u){if(-1===l)throw SyntaxError(`Unexpected character at index ${m}`);-1===d&&(d=m);let s=e.slice(l,d);44===u?(a(n,s,o),o=Object.create(null)):t=s,l=d=-1}else throw SyntaxError(`Unexpected character at index ${m}`)}else if(void 0===s){if(-1===d&&1===i[u])-1===l&&(l=m);else if(32===u||9===u)-1===d&&-1!==l&&(d=m);else if(59===u||44===u){if(-1===l)throw SyntaxError(`Unexpected character at index ${m}`);-1===d&&(d=m),a(o,e.slice(l,d),!0),44===u&&(a(n,t,o),o=Object.create(null),t=void 0),l=d=-1}else if(61===u&&-1!==l&&-1===d)s=e.slice(l,m),l=d=-1;else throw SyntaxError(`Unexpected character at index ${m}`)}else if(c){if(1!==i[u])throw SyntaxError(`Unexpected character at index ${m}`);-1===l?l=m:r||(r=!0),c=!1}else if(p){if(1===i[u])-1===l&&(l=m);else if(34===u&&-1!==l)p=!1,d=m;else if(92===u)c=!0;else throw SyntaxError(`Unexpected character at index ${m}`)}else if(34===u&&61===e.charCodeAt(m-1))p=!0;else if(-1===d&&1===i[u])-1===l&&(l=m);else if(-1!==l&&(32===u||9===u))-1===d&&(d=m);else if(59===u||44===u){if(-1===l)throw SyntaxError(`Unexpected character at index ${m}`);-1===d&&(d=m);let i=e.slice(l,d);r&&(i=i.replace(/\\/g,""),r=!1),a(o,s,i),44===u&&(a(n,t,o),o=Object.create(null),t=void 0),s=void 0,l=d=-1}else throw SyntaxError(`Unexpected character at index ${m}`);if(-1===l||p||32===u||9===u)throw SyntaxError("Unexpected end of input");-1===d&&(d=m);let h=e.slice(l,d);return void 0===t?a(n,h,o):(void 0===s?a(o,h,!0):r?a(o,s,h.replace(/\\/g,"")):a(o,s,h),a(n,t,o)),n}}},3790:e=>{"use strict";let t=Symbol("kDone"),s=Symbol("kRun");class i{constructor(e){this[t]=()=>{this.pending--,this[s]()},this.concurrency=e||1/0,this.jobs=[],this.pending=0}add(e){this.jobs.push(e),this[s]()}[s](){if(this.pending!==this.concurrency&&this.jobs.length){let e=this.jobs.shift();this.pending++,e(this[t])}}}e.exports=i},13101:(e,t,s)=>{"use strict";let i;let a=s(59796),n=s(7855),o=s(3790),{kStatusCode:r}=s(69853),c=Buffer[Symbol.species],p=Buffer.from([0,0,255,255]),l=Symbol("permessage-deflate"),u=Symbol("total-length"),d=Symbol("callback"),m=Symbol("buffers"),h=Symbol("error");class f{constructor(e,t,s){this._maxPayload=0|s,this._options=e||{},this._threshold=void 0!==this._options.threshold?this._options.threshold:1024,this._isServer=!!t,this._deflate=null,this._inflate=null,this.params=null,i||(i=new o(void 0!==this._options.concurrencyLimit?this._options.concurrencyLimit:10))}static get extensionName(){return"permessage-deflate"}offer(){let e={};return this._options.serverNoContextTakeover&&(e.server_no_context_takeover=!0),this._options.clientNoContextTakeover&&(e.client_no_context_takeover=!0),this._options.serverMaxWindowBits&&(e.server_max_window_bits=this._options.serverMaxWindowBits),this._options.clientMaxWindowBits?e.client_max_window_bits=this._options.clientMaxWindowBits:null==this._options.clientMaxWindowBits&&(e.client_max_window_bits=!0),e}accept(e){return e=this.normalizeParams(e),this.params=this._isServer?this.acceptAsServer(e):this.acceptAsClient(e),this.params}cleanup(){if(this._inflate&&(this._inflate.close(),this._inflate=null),this._deflate){let e=this._deflate[d];this._deflate.close(),this._deflate=null,e&&e(Error("The deflate stream was closed while data was being processed"))}}acceptAsServer(e){let t=this._options,s=e.find(e=>(!1!==t.serverNoContextTakeover||!e.server_no_context_takeover)&&(!e.server_max_window_bits||!1!==t.serverMaxWindowBits&&("number"!=typeof t.serverMaxWindowBits||!(t.serverMaxWindowBits>e.server_max_window_bits)))&&("number"!=typeof t.clientMaxWindowBits||!!e.client_max_window_bits));if(!s)throw Error("None of the extension offers can be accepted");return t.serverNoContextTakeover&&(s.server_no_context_takeover=!0),t.clientNoContextTakeover&&(s.client_no_context_takeover=!0),"number"==typeof t.serverMaxWindowBits&&(s.server_max_window_bits=t.serverMaxWindowBits),"number"==typeof t.clientMaxWindowBits?s.client_max_window_bits=t.clientMaxWindowBits:(!0===s.client_max_window_bits||!1===t.clientMaxWindowBits)&&delete s.client_max_window_bits,s}acceptAsClient(e){let t=e[0];if(!1===this._options.clientNoContextTakeover&&t.client_no_context_takeover)throw Error('Unexpected parameter "client_no_context_takeover"');if(t.client_max_window_bits){if(!1===this._options.clientMaxWindowBits||"number"==typeof this._options.clientMaxWindowBits&&t.client_max_window_bits>this._options.clientMaxWindowBits)throw Error('Unexpected or invalid parameter "client_max_window_bits"')}else"number"==typeof this._options.clientMaxWindowBits&&(t.client_max_window_bits=this._options.clientMaxWindowBits);return t}normalizeParams(e){return e.forEach(e=>{Object.keys(e).forEach(t=>{let s=e[t];if(s.length>1)throw Error(`Parameter "${t}" must have only a single value`);if(s=s[0],"client_max_window_bits"===t){if(!0!==s){let e=+s;if(!Number.isInteger(e)||e<8||e>15)throw TypeError(`Invalid value for parameter "${t}": ${s}`);s=e}else if(!this._isServer)throw TypeError(`Invalid value for parameter "${t}": ${s}`)}else if("server_max_window_bits"===t){let e=+s;if(!Number.isInteger(e)||e<8||e>15)throw TypeError(`Invalid value for parameter "${t}": ${s}`);s=e}else if("client_no_context_takeover"===t||"server_no_context_takeover"===t){if(!0!==s)throw TypeError(`Invalid value for parameter "${t}": ${s}`)}else throw Error(`Unknown parameter "${t}"`);e[t]=s})}),e}decompress(e,t,s){i.add(i=>{this._decompress(e,t,(e,t)=>{i(),s(e,t)})})}compress(e,t,s){i.add(i=>{this._compress(e,t,(e,t)=>{i(),s(e,t)})})}_decompress(e,t,s){let i=this._isServer?"client":"server";if(!this._inflate){let e=`${i}_max_window_bits`,t="number"!=typeof this.params[e]?a.Z_DEFAULT_WINDOWBITS:this.params[e];this._inflate=a.createInflateRaw({...this._options.zlibInflateOptions,windowBits:t}),this._inflate[l]=this,this._inflate[u]=0,this._inflate[m]=[],this._inflate.on("error",g),this._inflate.on("data",x)}this._inflate[d]=s,this._inflate.write(e),t&&this._inflate.write(p),this._inflate.flush(()=>{let e=this._inflate[h];if(e){this._inflate.close(),this._inflate=null,s(e);return}let a=n.concat(this._inflate[m],this._inflate[u]);this._inflate._readableState.endEmitted?(this._inflate.close(),this._inflate=null):(this._inflate[u]=0,this._inflate[m]=[],t&&this.params[`${i}_no_context_takeover`]&&this._inflate.reset()),s(null,a)})}_compress(e,t,s){let i=this._isServer?"server":"client";if(!this._deflate){let e=`${i}_max_window_bits`,t="number"!=typeof this.params[e]?a.Z_DEFAULT_WINDOWBITS:this.params[e];this._deflate=a.createDeflateRaw({...this._options.zlibDeflateOptions,windowBits:t}),this._deflate[u]=0,this._deflate[m]=[],this._deflate.on("data",v)}this._deflate[d]=s,this._deflate.write(e),this._deflate.flush(a.Z_SYNC_FLUSH,()=>{if(!this._deflate)return;let e=n.concat(this._deflate[m],this._deflate[u]);t&&(e=new c(e.buffer,e.byteOffset,e.length-4)),this._deflate[d]=null,this._deflate[u]=0,this._deflate[m]=[],t&&this.params[`${i}_no_context_takeover`]&&this._deflate.reset(),s(null,e)})}}function v(e){this[m].push(e),this[u]+=e.length}function x(e){if(this[u]+=e.length,this[l]._maxPayload<1||this[u]<=this[l]._maxPayload){this[m].push(e);return}this[h]=RangeError("Max payload size exceeded"),this[h].code="WS_ERR_UNSUPPORTED_MESSAGE_LENGTH",this[h][r]=1009,this.removeListener("data",x),this.reset()}function g(e){this[l]._inflate=null,e[r]=1007,this[d](e)}e.exports=f},37363:(e,t,s)=>{"use strict";let{Writable:i}=s(12781),a=s(13101),{BINARY_TYPES:n,EMPTY_BUFFER:o,kStatusCode:r,kWebSocket:c}=s(69853),{concat:p,toArrayBuffer:l,unmask:u}=s(7855),{isValidStatusCode:d,isValidUTF8:m}=s(73030),h=Buffer[Symbol.species];class f extends i{constructor(e={}){super(),this._allowSynchronousEvents=void 0===e.allowSynchronousEvents||e.allowSynchronousEvents,this._binaryType=e.binaryType||n[0],this._extensions=e.extensions||{},this._isServer=!!e.isServer,this._maxPayload=0|e.maxPayload,this._skipUTF8Validation=!!e.skipUTF8Validation,this[c]=void 0,this._bufferedBytes=0,this._buffers=[],this._compressed=!1,this._payloadLength=0,this._mask=void 0,this._fragmented=0,this._masked=!1,this._fin=!1,this._opcode=0,this._totalPayloadLength=0,this._messageLength=0,this._fragments=[],this._errored=!1,this._loop=!1,this._state=0}_write(e,t,s){if(8===this._opcode&&0==this._state)return s();this._bufferedBytes+=e.length,this._buffers.push(e),this.startLoop(s)}consume(e){if(this._bufferedBytes-=e,e===this._buffers[0].length)return this._buffers.shift();if(e<this._buffers[0].length){let t=this._buffers[0];return this._buffers[0]=new h(t.buffer,t.byteOffset+e,t.length-e),new h(t.buffer,t.byteOffset,e)}let t=Buffer.allocUnsafe(e);do{let s=this._buffers[0],i=t.length-e;e>=s.length?t.set(this._buffers.shift(),i):(t.set(new Uint8Array(s.buffer,s.byteOffset,e),i),this._buffers[0]=new h(s.buffer,s.byteOffset+e,s.length-e)),e-=s.length}while(e>0);return t}startLoop(e){this._loop=!0;do switch(this._state){case 0:this.getInfo(e);break;case 1:this.getPayloadLength16(e);break;case 2:this.getPayloadLength64(e);break;case 3:this.getMask();break;case 4:this.getData(e);break;case 5:case 6:this._loop=!1;return}while(this._loop);this._errored||e()}getInfo(e){if(this._bufferedBytes<2){this._loop=!1;return}let t=this.consume(2);if((48&t[0])!=0){e(this.createError(RangeError,"RSV2 and RSV3 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_2_3"));return}let s=(64&t[0])==64;if(s&&!this._extensions[a.extensionName]){e(this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1"));return}if(this._fin=(128&t[0])==128,this._opcode=15&t[0],this._payloadLength=127&t[1],0===this._opcode){if(s){e(this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1"));return}if(!this._fragmented){e(this.createError(RangeError,"invalid opcode 0",!0,1002,"WS_ERR_INVALID_OPCODE"));return}this._opcode=this._fragmented}else if(1===this._opcode||2===this._opcode){if(this._fragmented){e(this.createError(RangeError,`invalid opcode ${this._opcode}`,!0,1002,"WS_ERR_INVALID_OPCODE"));return}this._compressed=s}else if(this._opcode>7&&this._opcode<11){if(!this._fin){e(this.createError(RangeError,"FIN must be set",!0,1002,"WS_ERR_EXPECTED_FIN"));return}if(s){e(this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1"));return}if(this._payloadLength>125||8===this._opcode&&1===this._payloadLength){e(this.createError(RangeError,`invalid payload length ${this._payloadLength}`,!0,1002,"WS_ERR_INVALID_CONTROL_PAYLOAD_LENGTH"));return}}else{e(this.createError(RangeError,`invalid opcode ${this._opcode}`,!0,1002,"WS_ERR_INVALID_OPCODE"));return}if(this._fin||this._fragmented||(this._fragmented=this._opcode),this._masked=(128&t[1])==128,this._isServer){if(!this._masked){e(this.createError(RangeError,"MASK must be set",!0,1002,"WS_ERR_EXPECTED_MASK"));return}}else if(this._masked){e(this.createError(RangeError,"MASK must be clear",!0,1002,"WS_ERR_UNEXPECTED_MASK"));return}126===this._payloadLength?this._state=1:127===this._payloadLength?this._state=2:this.haveLength(e)}getPayloadLength16(e){if(this._bufferedBytes<2){this._loop=!1;return}this._payloadLength=this.consume(2).readUInt16BE(0),this.haveLength(e)}getPayloadLength64(e){if(this._bufferedBytes<8){this._loop=!1;return}let t=this.consume(8),s=t.readUInt32BE(0);if(s>2097151){e(this.createError(RangeError,"Unsupported WebSocket frame: payload length > 2^53 - 1",!1,1009,"WS_ERR_UNSUPPORTED_DATA_PAYLOAD_LENGTH"));return}this._payloadLength=***********s+t.readUInt32BE(4),this.haveLength(e)}haveLength(e){if(this._payloadLength&&this._opcode<8&&(this._totalPayloadLength+=this._payloadLength,this._totalPayloadLength>this._maxPayload&&this._maxPayload>0)){e(this.createError(RangeError,"Max payload size exceeded",!1,1009,"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH"));return}this._masked?this._state=3:this._state=4}getMask(){if(this._bufferedBytes<4){this._loop=!1;return}this._mask=this.consume(4),this._state=4}getData(e){let t=o;if(this._payloadLength){if(this._bufferedBytes<this._payloadLength){this._loop=!1;return}t=this.consume(this._payloadLength),this._masked&&(this._mask[0]|this._mask[1]|this._mask[2]|this._mask[3])!=0&&u(t,this._mask)}if(this._opcode>7){this.controlMessage(t,e);return}if(this._compressed){this._state=5,this.decompress(t,e);return}t.length&&(this._messageLength=this._totalPayloadLength,this._fragments.push(t)),this.dataMessage(e)}decompress(e,t){this._extensions[a.extensionName].decompress(e,this._fin,(e,s)=>{if(e)return t(e);if(s.length){if(this._messageLength+=s.length,this._messageLength>this._maxPayload&&this._maxPayload>0){t(this.createError(RangeError,"Max payload size exceeded",!1,1009,"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH"));return}this._fragments.push(s)}this.dataMessage(t),0===this._state&&this.startLoop(t)})}dataMessage(e){if(!this._fin){this._state=0;return}let t=this._messageLength,s=this._fragments;if(this._totalPayloadLength=0,this._messageLength=0,this._fragmented=0,this._fragments=[],2===this._opcode){let i;i="nodebuffer"===this._binaryType?p(s,t):"arraybuffer"===this._binaryType?l(p(s,t)):s,this._allowSynchronousEvents?(this.emit("message",i,!0),this._state=0):(this._state=6,setImmediate(()=>{this.emit("message",i,!0),this._state=0,this.startLoop(e)}))}else{let i=p(s,t);if(!this._skipUTF8Validation&&!m(i)){e(this.createError(Error,"invalid UTF-8 sequence",!0,1007,"WS_ERR_INVALID_UTF8"));return}5===this._state||this._allowSynchronousEvents?(this.emit("message",i,!1),this._state=0):(this._state=6,setImmediate(()=>{this.emit("message",i,!1),this._state=0,this.startLoop(e)}))}}controlMessage(e,t){if(8===this._opcode){if(0===e.length)this._loop=!1,this.emit("conclude",1005,o),this.end();else{let s=e.readUInt16BE(0);if(!d(s)){t(this.createError(RangeError,`invalid status code ${s}`,!0,1002,"WS_ERR_INVALID_CLOSE_CODE"));return}let i=new h(e.buffer,e.byteOffset+2,e.length-2);if(!this._skipUTF8Validation&&!m(i)){t(this.createError(Error,"invalid UTF-8 sequence",!0,1007,"WS_ERR_INVALID_UTF8"));return}this._loop=!1,this.emit("conclude",s,i),this.end()}this._state=0;return}this._allowSynchronousEvents?(this.emit(9===this._opcode?"ping":"pong",e),this._state=0):(this._state=6,setImmediate(()=>{this.emit(9===this._opcode?"ping":"pong",e),this._state=0,this.startLoop(t)}))}createError(e,t,s,i,a){this._loop=!1,this._errored=!0;let n=new e(s?`Invalid WebSocket frame: ${t}`:t);return Error.captureStackTrace(n,this.createError),n.code=a,n[r]=i,n}}e.exports=f},58709:(e,t,s)=>{"use strict";let i;let{Duplex:a}=s(12781),{randomFillSync:n}=s(6113),o=s(13101),{EMPTY_BUFFER:r}=s(69853),{isValidStatusCode:c}=s(73030),{mask:p,toBuffer:l}=s(7855),u=Symbol("kByteLength"),d=Buffer.alloc(4),m=8192;class h{constructor(e,t,s){this._extensions=t||{},s&&(this._generateMask=s,this._maskBuffer=Buffer.alloc(4)),this._socket=e,this._firstFragment=!0,this._compress=!1,this._bufferedBytes=0,this._deflating=!1,this._queue=[]}static frame(e,t){let s,a;let o=!1,r=2,c=!1;t.mask&&(s=t.maskBuffer||d,t.generateMask?t.generateMask(s):(8192===m&&(void 0===i&&(i=Buffer.alloc(8192)),n(i,0,8192),m=0),s[0]=i[m++],s[1]=i[m++],s[2]=i[m++],s[3]=i[m++]),c=(s[0]|s[1]|s[2]|s[3])==0,r=6),"string"==typeof e?a=(!t.mask||c)&&void 0!==t[u]?t[u]:(e=Buffer.from(e)).length:(a=e.length,o=t.mask&&t.readOnly&&!c);let l=a;a>=65536?(r+=8,l=127):a>125&&(r+=2,l=126);let h=Buffer.allocUnsafe(o?a+r:r);return(h[0]=t.fin?128|t.opcode:t.opcode,t.rsv1&&(h[0]|=64),h[1]=l,126===l?h.writeUInt16BE(a,2):127===l&&(h[2]=h[3]=0,h.writeUIntBE(a,4,6)),t.mask)?(h[1]|=128,h[r-4]=s[0],h[r-3]=s[1],h[r-2]=s[2],h[r-1]=s[3],c)?[h,e]:o?(p(e,s,h,r,a),[h]):(p(e,s,e,0,a),[h,e]):[h,e]}close(e,t,s,i){let a;if(void 0===e)a=r;else if("number"==typeof e&&c(e)){if(void 0!==t&&t.length){let s=Buffer.byteLength(t);if(s>123)throw RangeError("The message must not be greater than 123 bytes");(a=Buffer.allocUnsafe(2+s)).writeUInt16BE(e,0),"string"==typeof t?a.write(t,2):a.set(t,2)}else(a=Buffer.allocUnsafe(2)).writeUInt16BE(e,0)}else throw TypeError("First argument must be a valid error code number");let n={[u]:a.length,fin:!0,generateMask:this._generateMask,mask:s,maskBuffer:this._maskBuffer,opcode:8,readOnly:!1,rsv1:!1};this._deflating?this.enqueue([this.dispatch,a,!1,n,i]):this.sendFrame(h.frame(a,n),i)}ping(e,t,s){let i,a;if("string"==typeof e?(i=Buffer.byteLength(e),a=!1):(i=(e=l(e)).length,a=l.readOnly),i>125)throw RangeError("The data size must not be greater than 125 bytes");let n={[u]:i,fin:!0,generateMask:this._generateMask,mask:t,maskBuffer:this._maskBuffer,opcode:9,readOnly:a,rsv1:!1};this._deflating?this.enqueue([this.dispatch,e,!1,n,s]):this.sendFrame(h.frame(e,n),s)}pong(e,t,s){let i,a;if("string"==typeof e?(i=Buffer.byteLength(e),a=!1):(i=(e=l(e)).length,a=l.readOnly),i>125)throw RangeError("The data size must not be greater than 125 bytes");let n={[u]:i,fin:!0,generateMask:this._generateMask,mask:t,maskBuffer:this._maskBuffer,opcode:10,readOnly:a,rsv1:!1};this._deflating?this.enqueue([this.dispatch,e,!1,n,s]):this.sendFrame(h.frame(e,n),s)}send(e,t,s){let i,a;let n=this._extensions[o.extensionName],r=t.binary?2:1,c=t.compress;if("string"==typeof e?(i=Buffer.byteLength(e),a=!1):(i=(e=l(e)).length,a=l.readOnly),this._firstFragment?(this._firstFragment=!1,c&&n&&n.params[n._isServer?"server_no_context_takeover":"client_no_context_takeover"]&&(c=i>=n._threshold),this._compress=c):(c=!1,r=0),t.fin&&(this._firstFragment=!0),n){let n={[u]:i,fin:t.fin,generateMask:this._generateMask,mask:t.mask,maskBuffer:this._maskBuffer,opcode:r,readOnly:a,rsv1:c};this._deflating?this.enqueue([this.dispatch,e,this._compress,n,s]):this.dispatch(e,this._compress,n,s)}else this.sendFrame(h.frame(e,{[u]:i,fin:t.fin,generateMask:this._generateMask,mask:t.mask,maskBuffer:this._maskBuffer,opcode:r,readOnly:a,rsv1:!1}),s)}dispatch(e,t,s,i){if(!t){this.sendFrame(h.frame(e,s),i);return}let a=this._extensions[o.extensionName];this._bufferedBytes+=s[u],this._deflating=!0,a.compress(e,s.fin,(e,t)=>{if(this._socket.destroyed){let e=Error("The socket was closed while data was being compressed");"function"==typeof i&&i(e);for(let t=0;t<this._queue.length;t++){let s=this._queue[t],i=s[s.length-1];"function"==typeof i&&i(e)}return}this._bufferedBytes-=s[u],this._deflating=!1,s.readOnly=!1,this.sendFrame(h.frame(t,s),i),this.dequeue()})}dequeue(){for(;!this._deflating&&this._queue.length;){let e=this._queue.shift();this._bufferedBytes-=e[3][u],Reflect.apply(e[0],this,e.slice(1))}}enqueue(e){this._bufferedBytes+=e[3][u],this._queue.push(e)}sendFrame(e,t){2===e.length?(this._socket.cork(),this._socket.write(e[0]),this._socket.write(e[1],t),this._socket.uncork()):this._socket.write(e[0],t)}}e.exports=h},6264:(e,t,s)=>{"use strict";let{Duplex:i}=s(12781);function a(e){e.emit("close")}function n(){!this.destroyed&&this._writableState.finished&&this.destroy()}function o(e){this.removeListener("error",o),this.destroy(),0===this.listenerCount("error")&&this.emit("error",e)}e.exports=function(e,t){let s=!0,r=new i({...t,autoDestroy:!1,emitClose:!1,objectMode:!1,writableObjectMode:!1});return e.on("message",function(t,s){let i=!s&&r._readableState.objectMode?t.toString():t;r.push(i)||e.pause()}),e.once("error",function(e){r.destroyed||(s=!1,r.destroy(e))}),e.once("close",function(){r.destroyed||r.push(null)}),r._destroy=function(t,i){if(e.readyState===e.CLOSED){i(t),process.nextTick(a,r);return}let n=!1;e.once("error",function(e){n=!0,i(e)}),e.once("close",function(){n||i(t),process.nextTick(a,r)}),s&&e.terminate()},r._final=function(t){if(e.readyState===e.CONNECTING){e.once("open",function(){r._final(t)});return}null!==e._socket&&(e._socket._writableState.finished?(t(),r._readableState.endEmitted&&r.destroy()):(e._socket.once("finish",function(){t()}),e.close()))},r._read=function(){e.isPaused&&e.resume()},r._write=function(t,s,i){if(e.readyState===e.CONNECTING){e.once("open",function(){r._write(t,s,i)});return}e.send(t,i)},r.on("end",n),r.on("error",o),r}},42342:(e,t,s)=>{"use strict";let{tokenChars:i}=s(73030);e.exports={parse:function(e){let t=new Set,s=-1,a=-1,n=0;for(;n<e.length;n++){let o=e.charCodeAt(n);if(-1===a&&1===i[o])-1===s&&(s=n);else if(0!==n&&(32===o||9===o))-1===a&&-1!==s&&(a=n);else if(44===o){if(-1===s)throw SyntaxError(`Unexpected character at index ${n}`);-1===a&&(a=n);let i=e.slice(s,a);if(t.has(i))throw SyntaxError(`The "${i}" subprotocol is duplicated`);t.add(i),s=a=-1}else throw SyntaxError(`Unexpected character at index ${n}`)}if(-1===s||-1!==a)throw SyntaxError("Unexpected end of input");let o=e.slice(s,n);if(t.has(o))throw SyntaxError(`The "${o}" subprotocol is duplicated`);return t.add(o),t}}},73030:(e,t,s)=>{"use strict";let{isUtf8:i}=s(14300);function a(e){let t=e.length,s=0;for(;s<t;)if((128&e[s])==0)s++;else if((224&e[s])==192){if(s+1===t||(192&e[s+1])!=128||(254&e[s])==192)return!1;s+=2}else if((240&e[s])==224){if(s+2>=t||(192&e[s+1])!=128||(192&e[s+2])!=128||224===e[s]&&(224&e[s+1])==128||237===e[s]&&(224&e[s+1])==160)return!1;s+=3}else{if((248&e[s])!=240||s+3>=t||(192&e[s+1])!=128||(192&e[s+2])!=128||(192&e[s+3])!=128||240===e[s]&&(240&e[s+1])==128||244===e[s]&&e[s+1]>143||e[s]>244)return!1;s+=4}return!0}if(e.exports={isValidStatusCode:function(e){return e>=1e3&&e<=1014&&1004!==e&&1005!==e&&1006!==e||e>=3e3&&e<=4999},isValidUTF8:a,tokenChars:[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,1,1,1,1,1,0,0,1,1,0,1,1,0,1,1,1,1,1,1,1,1,1,1,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,0]},i)e.exports.isValidUTF8=function(e){return e.length<24?a(e):i(e)};else if(!process.env.WS_NO_UTF_8_VALIDATE)try{let t=s(62302);e.exports.isValidUTF8=function(e){return e.length<32?a(e):t(e)}}catch(e){}},28734:(e,t,s)=>{"use strict";let i=s(82361),a=s(13685),{Duplex:n}=s(12781),{createHash:o}=s(6113),r=s(48102),c=s(13101),p=s(42342),l=s(71968),{GUID:u,kWebSocket:d}=s(69853),m=/^[+/0-9A-Za-z]{22}==$/;class h extends i{constructor(e,t){if(super(),null==(e={allowSynchronousEvents:!0,autoPong:!0,maxPayload:*********,skipUTF8Validation:!1,perMessageDeflate:!1,handleProtocols:null,clientTracking:!0,verifyClient:null,noServer:!1,backlog:null,server:null,host:null,path:null,port:null,WebSocket:l,...e}).port&&!e.server&&!e.noServer||null!=e.port&&(e.server||e.noServer)||e.server&&e.noServer)throw TypeError('One and only one of the "port", "server", or "noServer" options must be specified');if(null!=e.port?(this._server=a.createServer((e,t)=>{let s=a.STATUS_CODES[426];t.writeHead(426,{"Content-Length":s.length,"Content-Type":"text/plain"}),t.end(s)}),this._server.listen(e.port,e.host,e.backlog,t)):e.server&&(this._server=e.server),this._server){let e=this.emit.bind(this,"connection");this._removeListeners=function(e,t){for(let s of Object.keys(t))e.on(s,t[s]);return function(){for(let s of Object.keys(t))e.removeListener(s,t[s])}}(this._server,{listening:this.emit.bind(this,"listening"),error:this.emit.bind(this,"error"),upgrade:(t,s,i)=>{this.handleUpgrade(t,s,i,e)}})}!0===e.perMessageDeflate&&(e.perMessageDeflate={}),e.clientTracking&&(this.clients=new Set,this._shouldEmitClose=!1),this.options=e,this._state=0}address(){if(this.options.noServer)throw Error('The server is operating in "noServer" mode');return this._server?this._server.address():null}close(e){if(2===this._state){e&&this.once("close",()=>{e(Error("The server is not running"))}),process.nextTick(f,this);return}if(e&&this.once("close",e),1!==this._state){if(this._state=1,this.options.noServer||this.options.server)this._server&&(this._removeListeners(),this._removeListeners=this._server=null),this.clients&&this.clients.size?this._shouldEmitClose=!0:process.nextTick(f,this);else{let e=this._server;this._removeListeners(),this._removeListeners=this._server=null,e.close(()=>{f(this)})}}}shouldHandle(e){if(this.options.path){let t=e.url.indexOf("?");if((-1!==t?e.url.slice(0,t):e.url)!==this.options.path)return!1}return!0}handleUpgrade(e,t,s,i){t.on("error",v);let a=e.headers["sec-websocket-key"],n=e.headers.upgrade,o=+e.headers["sec-websocket-version"];if("GET"!==e.method){g(this,e,t,405,"Invalid HTTP method");return}if(void 0===n||"websocket"!==n.toLowerCase()){g(this,e,t,400,"Invalid Upgrade header");return}if(void 0===a||!m.test(a)){g(this,e,t,400,"Missing or invalid Sec-WebSocket-Key header");return}if(8!==o&&13!==o){g(this,e,t,400,"Missing or invalid Sec-WebSocket-Version header");return}if(!this.shouldHandle(e)){x(t,400);return}let l=e.headers["sec-websocket-protocol"],u=new Set;if(void 0!==l)try{u=p.parse(l)}catch(s){g(this,e,t,400,"Invalid Sec-WebSocket-Protocol header");return}let d=e.headers["sec-websocket-extensions"],h={};if(this.options.perMessageDeflate&&void 0!==d){let s=new c(this.options.perMessageDeflate,!0,this.options.maxPayload);try{let e=r.parse(d);e[c.extensionName]&&(s.accept(e[c.extensionName]),h[c.extensionName]=s)}catch(s){g(this,e,t,400,"Invalid or unacceptable Sec-WebSocket-Extensions header");return}}if(this.options.verifyClient){let n={origin:e.headers[`${8===o?"sec-websocket-origin":"origin"}`],secure:!!(e.socket.authorized||e.socket.encrypted),req:e};if(2===this.options.verifyClient.length){this.options.verifyClient(n,(n,o,r,c)=>{if(!n)return x(t,o||401,r,c);this.completeUpgrade(h,a,u,e,t,s,i)});return}if(!this.options.verifyClient(n))return x(t,401)}this.completeUpgrade(h,a,u,e,t,s,i)}completeUpgrade(e,t,s,i,a,n,p){if(!a.readable||!a.writable)return a.destroy();if(a[d])throw Error("server.handleUpgrade() was called more than once with the same socket, possibly due to a misconfiguration");if(this._state>0)return x(a,503);let l=o("sha1").update(t+u).digest("base64"),m=["HTTP/1.1 101 Switching Protocols","Upgrade: websocket","Connection: Upgrade",`Sec-WebSocket-Accept: ${l}`],h=new this.options.WebSocket(null,void 0,this.options);if(s.size){let e=this.options.handleProtocols?this.options.handleProtocols(s,i):s.values().next().value;e&&(m.push(`Sec-WebSocket-Protocol: ${e}`),h._protocol=e)}if(e[c.extensionName]){let t=e[c.extensionName].params,s=r.format({[c.extensionName]:[t]});m.push(`Sec-WebSocket-Extensions: ${s}`),h._extensions=e}this.emit("headers",m,i),a.write(m.concat("\r\n").join("\r\n")),a.removeListener("error",v),h.setSocket(a,n,{allowSynchronousEvents:this.options.allowSynchronousEvents,maxPayload:this.options.maxPayload,skipUTF8Validation:this.options.skipUTF8Validation}),this.clients&&(this.clients.add(h),h.on("close",()=>{this.clients.delete(h),this._shouldEmitClose&&!this.clients.size&&process.nextTick(f,this)})),p(h,i)}}function f(e){e._state=2,e.emit("close")}function v(){this.destroy()}function x(e,t,s,i){s=s||a.STATUS_CODES[t],i={Connection:"close","Content-Type":"text/html","Content-Length":Buffer.byteLength(s),...i},e.once("finish",e.destroy),e.end(`HTTP/1.1 ${t} ${a.STATUS_CODES[t]}\r
`+Object.keys(i).map(e=>`${e}: ${i[e]}`).join("\r\n")+"\r\n\r\n"+s)}function g(e,t,s,i,a){if(e.listenerCount("wsClientError")){let i=Error(a);Error.captureStackTrace(i,g),e.emit("wsClientError",i,s,t)}else x(s,i,a)}e.exports=h},71968:(e,t,s)=>{"use strict";let i=s(82361),a=s(95687),n=s(13685),o=s(41808),r=s(24404),{randomBytes:c,createHash:p}=s(6113),{Duplex:l,Readable:u}=s(12781),{URL:d}=s(57310),m=s(13101),h=s(37363),f=s(58709),{BINARY_TYPES:v,EMPTY_BUFFER:x,GUID:g,kForOnEventAttribute:b,kListener:y,kStatusCode:_,kWebSocket:w,NOOP:k}=s(69853),{EventTarget:{addEventListener:S,removeEventListener:C}}=s(76628),{format:E,parse:O}=s(48102),{toBuffer:T}=s(7855),A=Symbol("kAborted"),j=[8,13],R=["CONNECTING","OPEN","CLOSING","CLOSED"],N=/^[!#$%&'*+\-.0-9A-Z^_`|a-z~]+$/;class P extends i{constructor(e,t,s){super(),this._binaryType=v[0],this._closeCode=1006,this._closeFrameReceived=!1,this._closeFrameSent=!1,this._closeMessage=x,this._closeTimer=null,this._extensions={},this._paused=!1,this._protocol="",this._readyState=P.CONNECTING,this._receiver=null,this._sender=null,this._socket=null,null!==e?(this._bufferedAmount=0,this._isServer=!1,this._redirects=0,void 0===t?t=[]:Array.isArray(t)||("object"==typeof t&&null!==t?(s=t,t=[]):t=[t]),function e(t,s,i,o){let r,l,u,h;let f={allowSynchronousEvents:!0,autoPong:!0,protocolVersion:j[1],maxPayload:*********,skipUTF8Validation:!1,perMessageDeflate:!0,followRedirects:!1,maxRedirects:10,...o,socketPath:void 0,hostname:void 0,protocol:void 0,timeout:void 0,method:"GET",host:void 0,path:void 0,port:void 0};if(t._autoPong=f.autoPong,!j.includes(f.protocolVersion))throw RangeError(`Unsupported protocol version: ${f.protocolVersion} (supported versions: ${j.join(", ")})`);if(s instanceof d)r=s;else try{r=new d(s)}catch(e){throw SyntaxError(`Invalid URL: ${s}`)}"http:"===r.protocol?r.protocol="ws:":"https:"===r.protocol&&(r.protocol="wss:"),t._url=r.href;let v="wss:"===r.protocol,x="ws+unix:"===r.protocol;if("ws:"===r.protocol||v||x?x&&!r.pathname?l="The URL's pathname is empty":r.hash&&(l="The URL contains a fragment identifier"):l='The URL\'s protocol must be one of "ws:", "wss:", "http:", "https", or "ws+unix:"',l){let e=SyntaxError(l);if(0===t._redirects)throw e;F(t,e);return}let b=v?443:80,y=c(16).toString("base64"),_=v?a.request:n.request,w=new Set;if(f.createConnection=f.createConnection||(v?B:I),f.defaultPort=f.defaultPort||b,f.port=r.port||b,f.host=r.hostname.startsWith("[")?r.hostname.slice(1,-1):r.hostname,f.headers={...f.headers,"Sec-WebSocket-Version":f.protocolVersion,"Sec-WebSocket-Key":y,Connection:"Upgrade",Upgrade:"websocket"},f.path=r.pathname+r.search,f.timeout=f.handshakeTimeout,f.perMessageDeflate&&(u=new m(!0!==f.perMessageDeflate?f.perMessageDeflate:{},!1,f.maxPayload),f.headers["Sec-WebSocket-Extensions"]=E({[m.extensionName]:u.offer()})),i.length){for(let e of i){if("string"!=typeof e||!N.test(e)||w.has(e))throw SyntaxError("An invalid or duplicated subprotocol was specified");w.add(e)}f.headers["Sec-WebSocket-Protocol"]=i.join(",")}if(f.origin&&(f.protocolVersion<13?f.headers["Sec-WebSocket-Origin"]=f.origin:f.headers.Origin=f.origin),(r.username||r.password)&&(f.auth=`${r.username}:${r.password}`),x){let e=f.path.split(":");f.socketPath=e[0],f.path=e[1]}if(f.followRedirects){if(0===t._redirects){t._originalIpc=x,t._originalSecure=v,t._originalHostOrSocketPath=x?f.socketPath:r.host;let e=o&&o.headers;if(o={...o,headers:{}},e)for(let[t,s]of Object.entries(e))o.headers[t.toLowerCase()]=s}else if(0===t.listenerCount("redirect")){let e=x?!!t._originalIpc&&f.socketPath===t._originalHostOrSocketPath:!t._originalIpc&&r.host===t._originalHostOrSocketPath;e&&(!t._originalSecure||v)||(delete f.headers.authorization,delete f.headers.cookie,e||delete f.headers.host,f.auth=void 0)}f.auth&&!o.headers.authorization&&(o.headers.authorization="Basic "+Buffer.from(f.auth).toString("base64")),h=t._req=_(f),t._redirects&&t.emit("redirect",t.url,h)}else h=t._req=_(f);f.timeout&&h.on("timeout",()=>{L(t,h,"Opening handshake has timed out")}),h.on("error",e=>{null===h||h[A]||(h=t._req=null,F(t,e))}),h.on("response",a=>{let n=a.headers.location,r=a.statusCode;if(n&&f.followRedirects&&r>=300&&r<400){let a;if(++t._redirects>f.maxRedirects){L(t,h,"Maximum redirects exceeded");return}h.abort();try{a=new d(n,s)}catch(e){F(t,SyntaxError(`Invalid URL: ${n}`));return}e(t,a,i,o)}else t.emit("unexpected-response",h,a)||L(t,h,`Unexpected server response: ${a.statusCode}`)}),h.on("upgrade",(e,s,i)=>{let a;if(t.emit("upgrade",e),t.readyState!==P.CONNECTING)return;h=t._req=null;let n=e.headers.upgrade;if(void 0===n||"websocket"!==n.toLowerCase()){L(t,s,"Invalid Upgrade header");return}let o=p("sha1").update(y+g).digest("base64");if(e.headers["sec-websocket-accept"]!==o){L(t,s,"Invalid Sec-WebSocket-Accept header");return}let r=e.headers["sec-websocket-protocol"];if(void 0!==r?w.size?w.has(r)||(a="Server sent an invalid subprotocol"):a="Server sent a subprotocol but none was requested":w.size&&(a="Server sent no subprotocol"),a){L(t,s,a);return}r&&(t._protocol=r);let c=e.headers["sec-websocket-extensions"];if(void 0!==c){let e;if(!u){L(t,s,"Server sent a Sec-WebSocket-Extensions header but no extension was requested");return}try{e=O(c)}catch(e){L(t,s,"Invalid Sec-WebSocket-Extensions header");return}let i=Object.keys(e);if(1!==i.length||i[0]!==m.extensionName){L(t,s,"Server indicated an extension that was not requested");return}try{u.accept(e[m.extensionName])}catch(e){L(t,s,"Invalid Sec-WebSocket-Extensions header");return}t._extensions[m.extensionName]=u}t.setSocket(s,i,{allowSynchronousEvents:f.allowSynchronousEvents,generateMask:f.generateMask,maxPayload:f.maxPayload,skipUTF8Validation:f.skipUTF8Validation})}),f.finishRequest?f.finishRequest(h,t):h.end()}(this,e,t,s)):(this._autoPong=s.autoPong,this._isServer=!0)}get binaryType(){return this._binaryType}set binaryType(e){v.includes(e)&&(this._binaryType=e,this._receiver&&(this._receiver._binaryType=e))}get bufferedAmount(){return this._socket?this._socket._writableState.length+this._sender._bufferedBytes:this._bufferedAmount}get extensions(){return Object.keys(this._extensions).join()}get isPaused(){return this._paused}get onclose(){return null}get onerror(){return null}get onopen(){return null}get onmessage(){return null}get protocol(){return this._protocol}get readyState(){return this._readyState}get url(){return this._url}setSocket(e,t,s){let i=new h({allowSynchronousEvents:s.allowSynchronousEvents,binaryType:this.binaryType,extensions:this._extensions,isServer:this._isServer,maxPayload:s.maxPayload,skipUTF8Validation:s.skipUTF8Validation});this._sender=new f(e,this._extensions,s.generateMask),this._receiver=i,this._socket=e,i[w]=this,e[w]=this,i.on("conclude",D),i.on("drain",U),i.on("error",M),i.on("message",W),i.on("ping",$),i.on("pong",V),e.setTimeout&&e.setTimeout(0),e.setNoDelay&&e.setNoDelay(),t.length>0&&e.unshift(t),e.on("close",G),e.on("data",K),e.on("end",Y),e.on("error",J),this._readyState=P.OPEN,this.emit("open")}emitClose(){if(!this._socket){this._readyState=P.CLOSED,this.emit("close",this._closeCode,this._closeMessage);return}this._extensions[m.extensionName]&&this._extensions[m.extensionName].cleanup(),this._receiver.removeAllListeners(),this._readyState=P.CLOSED,this.emit("close",this._closeCode,this._closeMessage)}close(e,t){if(this.readyState!==P.CLOSED){if(this.readyState===P.CONNECTING){L(this,this._req,"WebSocket was closed before the connection was established");return}if(this.readyState===P.CLOSING){this._closeFrameSent&&(this._closeFrameReceived||this._receiver._writableState.errorEmitted)&&this._socket.end();return}this._readyState=P.CLOSING,this._sender.close(e,t,!this._isServer,e=>{!e&&(this._closeFrameSent=!0,(this._closeFrameReceived||this._receiver._writableState.errorEmitted)&&this._socket.end())}),this._closeTimer=setTimeout(this._socket.destroy.bind(this._socket),3e4)}}pause(){this.readyState!==P.CONNECTING&&this.readyState!==P.CLOSED&&(this._paused=!0,this._socket.pause())}ping(e,t,s){if(this.readyState===P.CONNECTING)throw Error("WebSocket is not open: readyState 0 (CONNECTING)");if("function"==typeof e?(s=e,e=t=void 0):"function"==typeof t&&(s=t,t=void 0),"number"==typeof e&&(e=e.toString()),this.readyState!==P.OPEN){q(this,e,s);return}void 0===t&&(t=!this._isServer),this._sender.ping(e||x,t,s)}pong(e,t,s){if(this.readyState===P.CONNECTING)throw Error("WebSocket is not open: readyState 0 (CONNECTING)");if("function"==typeof e?(s=e,e=t=void 0):"function"==typeof t&&(s=t,t=void 0),"number"==typeof e&&(e=e.toString()),this.readyState!==P.OPEN){q(this,e,s);return}void 0===t&&(t=!this._isServer),this._sender.pong(e||x,t,s)}resume(){this.readyState!==P.CONNECTING&&this.readyState!==P.CLOSED&&(this._paused=!1,this._receiver._writableState.needDrain||this._socket.resume())}send(e,t,s){if(this.readyState===P.CONNECTING)throw Error("WebSocket is not open: readyState 0 (CONNECTING)");if("function"==typeof t&&(s=t,t={}),"number"==typeof e&&(e=e.toString()),this.readyState!==P.OPEN){q(this,e,s);return}let i={binary:"string"!=typeof e,mask:!this._isServer,compress:!0,fin:!0,...t};this._extensions[m.extensionName]||(i.compress=!1),this._sender.send(e||x,i,s)}terminate(){if(this.readyState!==P.CLOSED){if(this.readyState===P.CONNECTING){L(this,this._req,"WebSocket was closed before the connection was established");return}this._socket&&(this._readyState=P.CLOSING,this._socket.destroy())}}}function F(e,t){e._readyState=P.CLOSING,e.emit("error",t),e.emitClose()}function I(e){return e.path=e.socketPath,o.connect(e)}function B(e){return e.path=void 0,e.servername||""===e.servername||(e.servername=o.isIP(e.host)?"":e.host),r.connect(e)}function L(e,t,s){e._readyState=P.CLOSING;let i=Error(s);Error.captureStackTrace(i,L),t.setHeader?(t[A]=!0,t.abort(),t.socket&&!t.socket.destroyed&&t.socket.destroy(),process.nextTick(F,e,i)):(t.destroy(i),t.once("error",e.emit.bind(e,"error")),t.once("close",e.emitClose.bind(e)))}function q(e,t,s){if(t){let s=T(t).length;e._socket?e._sender._bufferedBytes+=s:e._bufferedAmount+=s}if(s){let t=Error(`WebSocket is not open: readyState ${e.readyState} (${R[e.readyState]})`);process.nextTick(s,t)}}function D(e,t){let s=this[w];s._closeFrameReceived=!0,s._closeMessage=t,s._closeCode=e,void 0!==s._socket[w]&&(s._socket.removeListener("data",K),process.nextTick(H,s._socket),1005===e?s.close():s.close(e,t))}function U(){let e=this[w];e.isPaused||e._socket.resume()}function M(e){let t=this[w];void 0!==t._socket[w]&&(t._socket.removeListener("data",K),process.nextTick(H,t._socket),t.close(e[_])),t.emit("error",e)}function z(){this[w].emitClose()}function W(e,t){this[w].emit("message",e,t)}function $(e){let t=this[w];t._autoPong&&t.pong(e,!this._isServer,k),t.emit("ping",e)}function V(e){this[w].emit("pong",e)}function H(e){e.resume()}function G(){let e;let t=this[w];this.removeListener("close",G),this.removeListener("data",K),this.removeListener("end",Y),t._readyState=P.CLOSING,this._readableState.endEmitted||t._closeFrameReceived||t._receiver._writableState.errorEmitted||null===(e=t._socket.read())||t._receiver.write(e),t._receiver.end(),this[w]=void 0,clearTimeout(t._closeTimer),t._receiver._writableState.finished||t._receiver._writableState.errorEmitted?t.emitClose():(t._receiver.on("error",z),t._receiver.on("finish",z))}function K(e){this[w]._receiver.write(e)||this.pause()}function Y(){let e=this[w];e._readyState=P.CLOSING,e._receiver.end(),this.end()}function J(){let e=this[w];this.removeListener("error",J),this.on("error",k),e&&(e._readyState=P.CLOSING,this.destroy())}Object.defineProperty(P,"CONNECTING",{enumerable:!0,value:R.indexOf("CONNECTING")}),Object.defineProperty(P.prototype,"CONNECTING",{enumerable:!0,value:R.indexOf("CONNECTING")}),Object.defineProperty(P,"OPEN",{enumerable:!0,value:R.indexOf("OPEN")}),Object.defineProperty(P.prototype,"OPEN",{enumerable:!0,value:R.indexOf("OPEN")}),Object.defineProperty(P,"CLOSING",{enumerable:!0,value:R.indexOf("CLOSING")}),Object.defineProperty(P.prototype,"CLOSING",{enumerable:!0,value:R.indexOf("CLOSING")}),Object.defineProperty(P,"CLOSED",{enumerable:!0,value:R.indexOf("CLOSED")}),Object.defineProperty(P.prototype,"CLOSED",{enumerable:!0,value:R.indexOf("CLOSED")}),["binaryType","bufferedAmount","extensions","isPaused","protocol","readyState","url"].forEach(e=>{Object.defineProperty(P.prototype,e,{enumerable:!0})}),["open","error","close","message"].forEach(e=>{Object.defineProperty(P.prototype,`on${e}`,{enumerable:!0,get(){for(let t of this.listeners(e))if(t[b])return t[y];return null},set(t){for(let t of this.listeners(e))if(t[b]){this.removeListener(e,t);break}"function"==typeof t&&this.addEventListener(e,t,{[b]:!0})}})}),P.prototype.addEventListener=S,P.prototype.removeEventListener=C,e.exports=P},10753:e=>{"use strict";e.exports=(e,t=process.argv)=>{let s=e.startsWith("-")?"":1===e.length?"-":"--",i=t.indexOf(s+e),a=t.indexOf("--");return -1!==i&&(-1===a||i<a)}},90279:(e,t,s)=>{/*!
 * mime-db
 * Copyright(c) 2014 Jonathan Ong
 * Copyright(c) 2015-2022 Douglas Christopher Wilson
 * MIT Licensed
 */e.exports=s(2753)},78813:(e,t,s)=>{"use strict";/*!
 * mime-types
 * Copyright(c) 2014 Jonathan Ong
 * Copyright(c) 2015 Douglas Christopher Wilson
 * MIT Licensed
 */var i=s(90279),a=s(71017).extname,n=/^\s*([^;\s]*)(?:;|\s|$)/,o=/^text\//i;function r(e){if(!e||"string"!=typeof e)return!1;var t=n.exec(e),s=t&&i[t[1].toLowerCase()];return s&&s.charset?s.charset:!!(t&&o.test(t[1]))&&"UTF-8"}t.charset=r,t.charsets={lookup:r},t.contentType=function(e){if(!e||"string"!=typeof e)return!1;var s=-1===e.indexOf("/")?t.lookup(e):e;if(!s)return!1;if(-1===s.indexOf("charset")){var i=t.charset(s);i&&(s+="; charset="+i.toLowerCase())}return s},t.extension=function(e){if(!e||"string"!=typeof e)return!1;var s=n.exec(e),i=s&&t.extensions[s[1].toLowerCase()];return!!i&&!!i.length&&i[0]},t.extensions=Object.create(null),t.lookup=function(e){if(!e||"string"!=typeof e)return!1;var s=a("x."+e).toLowerCase().substr(1);return!!s&&(t.types[s]||!1)},t.types=Object.create(null),function(e,t){var s=["nginx","apache",void 0,"iana"];Object.keys(i).forEach(function(a){var n=i[a],o=n.extensions;if(o&&o.length){e[a]=o;for(var r=0;r<o.length;r++){var c=o[r];if(t[c]){var p=s.indexOf(i[t[c]].source),l=s.indexOf(n.source);if("application/octet-stream"!==t[c]&&(p>l||p===l&&"application/"===t[c].substr(0,12)))continue}t[c]=a}}})}(t.extensions,t.types)},87553:e=>{function t(e,t,s,i){return Math.round(e/s)+" "+i+(t>=1.5*s?"s":"")}e.exports=function(e,s){s=s||{};var i,a,n=typeof e;if("string"===n&&e.length>0)return function(e){if(!((e=String(e)).length>100)){var t=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(t){var s=parseFloat(t[1]);switch((t[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return 315576e5*s;case"weeks":case"week":case"w":return 6048e5*s;case"days":case"day":case"d":return 864e5*s;case"hours":case"hour":case"hrs":case"hr":case"h":return 36e5*s;case"minutes":case"minute":case"mins":case"min":case"m":return 6e4*s;case"seconds":case"second":case"secs":case"sec":case"s":return 1e3*s;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return s;default:return}}}}(e);if("number"===n&&isFinite(e))return s.long?(i=Math.abs(e))>=864e5?t(e,i,864e5,"day"):i>=36e5?t(e,i,36e5,"hour"):i>=6e4?t(e,i,6e4,"minute"):i>=1e3?t(e,i,1e3,"second"):e+" ms":(a=Math.abs(e))>=864e5?Math.round(e/864e5)+"d":a>=36e5?Math.round(e/36e5)+"h":a>=6e4?Math.round(e/6e4)+"m":a>=1e3?Math.round(e/1e3)+"s":e+"ms";throw Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},73685:(e,t,s)=>{"use strict";/*!
 * negotiator
 * Copyright(c) 2012 Federico Romero
 * Copyright(c) 2012-2014 Isaac Z. Schlueter
 * Copyright(c) 2015 Douglas Christopher Wilson
 * MIT Licensed
 */var i=s(36116),a=s(47346),n=s(26888),o=s(92384);function r(e){if(!(this instanceof r))return new r(e);this.request=e}e.exports=r,e.exports.Negotiator=r,r.prototype.charset=function(e){var t=this.charsets(e);return t&&t[0]},r.prototype.charsets=function(e){return i(this.request.headers["accept-charset"],e)},r.prototype.encoding=function(e){var t=this.encodings(e);return t&&t[0]},r.prototype.encodings=function(e){return a(this.request.headers["accept-encoding"],e)},r.prototype.language=function(e){var t=this.languages(e);return t&&t[0]},r.prototype.languages=function(e){return n(this.request.headers["accept-language"],e)},r.prototype.mediaType=function(e){var t=this.mediaTypes(e);return t&&t[0]},r.prototype.mediaTypes=function(e){return o(this.request.headers.accept,e)},r.prototype.preferredCharset=r.prototype.charset,r.prototype.preferredCharsets=r.prototype.charsets,r.prototype.preferredEncoding=r.prototype.encoding,r.prototype.preferredEncodings=r.prototype.encodings,r.prototype.preferredLanguage=r.prototype.language,r.prototype.preferredLanguages=r.prototype.languages,r.prototype.preferredMediaType=r.prototype.mediaType,r.prototype.preferredMediaTypes=r.prototype.mediaTypes},36116:e=>{"use strict";e.exports=s,e.exports.preferredCharsets=s;var t=/^\s*([^\s;]+)\s*(?:;(.*))?$/;function s(e,s){var o=function(e){for(var s=e.split(","),i=0,a=0;i<s.length;i++){var n=function(e,s){var i=t.exec(e);if(!i)return null;var a=i[1],n=1;if(i[2])for(var o=i[2].split(";"),r=0;r<o.length;r++){var c=o[r].trim().split("=");if("q"===c[0]){n=parseFloat(c[1]);break}}return{charset:a,q:n,i:s}}(s[i].trim(),i);n&&(s[a++]=n)}return s.length=a,s}(void 0===e?"*":e||"");if(!s)return o.filter(n).sort(i).map(a);var r=s.map(function(e,t){return function(e,t,s){for(var i={o:-1,q:0,s:0},a=0;a<t.length;a++){var n=function(e,t,s){var i=0;if(t.charset.toLowerCase()===e.toLowerCase())i|=1;else if("*"!==t.charset)return null;return{i:s,o:t.i,q:t.q,s:i}}(e,t[a],s);n&&0>(i.s-n.s||i.q-n.q||i.o-n.o)&&(i=n)}return i}(e,o,t)});return r.filter(n).sort(i).map(function(e){return s[r.indexOf(e)]})}function i(e,t){return t.q-e.q||t.s-e.s||e.o-t.o||e.i-t.i||0}function a(e){return e.charset}function n(e){return e.q>0}},47346:e=>{"use strict";e.exports=i,e.exports.preferredEncodings=i;var t=/^\s*([^\s;]+)\s*(?:;(.*))?$/;function s(e,t,s){var i=0;if(t.encoding.toLowerCase()===e.toLowerCase())i|=1;else if("*"!==t.encoding)return null;return{i:s,o:t.i,q:t.q,s:i}}function i(e,i){var r=function(e){for(var i=e.split(","),a=!1,n=1,o=0,r=0;o<i.length;o++){var c=function(e,s){var i=t.exec(e);if(!i)return null;var a=i[1],n=1;if(i[2])for(var o=i[2].split(";"),r=0;r<o.length;r++){var c=o[r].trim().split("=");if("q"===c[0]){n=parseFloat(c[1]);break}}return{encoding:a,q:n,i:s}}(i[o].trim(),o);c&&(i[r++]=c,a=a||s("identity",c),n=Math.min(n,c.q||1))}return a||(i[r++]={encoding:"identity",q:n,i:o}),i.length=r,i}(e||"");if(!i)return r.filter(o).sort(a).map(n);var c=i.map(function(e,t){return function(e,t,i){for(var a={o:-1,q:0,s:0},n=0;n<t.length;n++){var o=s(e,t[n],i);o&&0>(a.s-o.s||a.q-o.q||a.o-o.o)&&(a=o)}return a}(e,r,t)});return c.filter(o).sort(a).map(function(e){return i[c.indexOf(e)]})}function a(e,t){return t.q-e.q||t.s-e.s||e.o-t.o||e.i-t.i||0}function n(e){return e.encoding}function o(e){return e.q>0}},26888:e=>{"use strict";e.exports=i,e.exports.preferredLanguages=i;var t=/^\s*([^\s\-;]+)(?:-([^\s;]+))?\s*(?:;(.*))?$/;function s(e,s){var i=t.exec(e);if(!i)return null;var a=i[1],n=i[2],o=a;n&&(o+="-"+n);var r=1;if(i[3])for(var c=i[3].split(";"),p=0;p<c.length;p++){var l=c[p].split("=");"q"===l[0]&&(r=parseFloat(l[1]))}return{prefix:a,suffix:n,q:r,i:s,full:o}}function i(e,t){var i=function(e){for(var t=e.split(","),i=0,a=0;i<t.length;i++){var n=s(t[i].trim(),i);n&&(t[a++]=n)}return t.length=a,t}(void 0===e?"*":e||"");if(!t)return i.filter(o).sort(a).map(n);var r=t.map(function(e,t){return function(e,t,i){for(var a={o:-1,q:0,s:0},n=0;n<t.length;n++){var o=function(e,t,i){var a=s(e);if(!a)return null;var n=0;if(t.full.toLowerCase()===a.full.toLowerCase())n|=4;else if(t.prefix.toLowerCase()===a.full.toLowerCase())n|=2;else if(t.full.toLowerCase()===a.prefix.toLowerCase())n|=1;else if("*"!==t.full)return null;return{i:i,o:t.i,q:t.q,s:n}}(e,t[n],i);o&&0>(a.s-o.s||a.q-o.q||a.o-o.o)&&(a=o)}return a}(e,i,t)});return r.filter(o).sort(a).map(function(e){return t[r.indexOf(e)]})}function a(e,t){return t.q-e.q||t.s-e.s||e.o-t.o||e.i-t.i||0}function n(e){return e.full}function o(e){return e.q>0}},92384:e=>{"use strict";e.exports=i,e.exports.preferredMediaTypes=i;var t=/^\s*([^\s\/;]+)\/([^;\s]+)\s*(?:;(.*))?$/;function s(e,s){var i=t.exec(e);if(!i)return null;var a=Object.create(null),n=1,o=i[2],p=i[1];if(i[3])for(var l=(function(e){for(var t=e.split(";"),s=1,i=0;s<t.length;s++)r(t[i])%2==0?t[++i]=t[s]:t[i]+=";"+t[s];t.length=i+1;for(var s=0;s<t.length;s++)t[s]=t[s].trim();return t})(i[3]).map(c),u=0;u<l.length;u++){var d=l[u],m=d[0].toLowerCase(),h=d[1],f=h&&'"'===h[0]&&'"'===h[h.length-1]?h.substr(1,h.length-2):h;if("q"===m){n=parseFloat(f);break}a[m]=f}return{type:p,subtype:o,params:a,q:n,i:s}}function i(e,t){var i=function(e){for(var t=function(e){for(var t=e.split(","),s=1,i=0;s<t.length;s++)r(t[i])%2==0?t[++i]=t[s]:t[i]+=","+t[s];return t.length=i+1,t}(e),i=0,a=0;i<t.length;i++){var n=s(t[i].trim(),i);n&&(t[a++]=n)}return t.length=a,t}(void 0===e?"*/*":e||"");if(!t)return i.filter(o).sort(a).map(n);var c=t.map(function(e,t){return function(e,t,i){for(var a={o:-1,q:0,s:0},n=0;n<t.length;n++){var o=function(e,t,i){var a=s(e),n=0;if(!a)return null;if(t.type.toLowerCase()==a.type.toLowerCase())n|=4;else if("*"!=t.type)return null;if(t.subtype.toLowerCase()==a.subtype.toLowerCase())n|=2;else if("*"!=t.subtype)return null;var o=Object.keys(t.params);if(o.length>0){if(!o.every(function(e){return"*"==t.params[e]||(t.params[e]||"").toLowerCase()==(a.params[e]||"").toLowerCase()}))return null;n|=1}return{i:i,o:t.i,q:t.q,s:n}}(e,t[n],i);o&&0>(a.s-o.s||a.q-o.q||a.o-o.o)&&(a=o)}return a}(e,i,t)});return c.filter(o).sort(a).map(function(e){return t[c.indexOf(e)]})}function a(e,t){return t.q-e.q||t.s-e.s||e.o-t.o||e.i-t.i||0}function n(e){return e.type+"/"+e.subtype}function o(e){return e.q>0}function r(e){for(var t=0,s=0;-1!==(s=e.indexOf('"',s));)t++,s++;return t}function c(e){var t,s,i=e.indexOf("=");return -1===i?t=e:(t=e.substr(0,i),s=e.substr(i+1)),[t,s]}},95419:(e,t,s)=>{"use strict";e.exports=s(30517)},9335:e=>{"use strict";/*
object-assign
(c) Sindre Sorhus
@license MIT
*/var t=Object.getOwnPropertySymbols,s=Object.prototype.hasOwnProperty,i=Object.prototype.propertyIsEnumerable;e.exports=!function(){try{if(!Object.assign)return!1;var e=new String("abc");if(e[5]="de","5"===Object.getOwnPropertyNames(e)[0])return!1;for(var t={},s=0;s<10;s++)t["_"+String.fromCharCode(s)]=s;var i=Object.getOwnPropertyNames(t).map(function(e){return t[e]});if("**********"!==i.join(""))return!1;var a={};if("abcdefghijklmnopqrst".split("").forEach(function(e){a[e]=e}),"abcdefghijklmnopqrst"!==Object.keys(Object.assign({},a)).join(""))return!1;return!0}catch(e){return!1}}()?function(e,a){for(var n,o,r=function(e){if(null==e)throw TypeError("Object.assign cannot be called with null or undefined");return Object(e)}(e),c=1;c<arguments.length;c++){for(var p in n=Object(arguments[c]))s.call(n,p)&&(r[p]=n[p]);if(t){o=t(n);for(var l=0;l<o.length;l++)i.call(n,o[l])&&(r[o[l]]=n[o[l]])}}return r}:Object.assign},99310:function(e,t,s){"use strict";var i,a,n=this&&this.__rest||function(e,t){var s={};for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&0>t.indexOf(i)&&(s[i]=e[i]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,i=Object.getOwnPropertySymbols(e);a<i.length;a++)0>t.indexOf(i[a])&&Object.prototype.propertyIsEnumerable.call(e,i[a])&&(s[i[a]]=e[i[a]]);return s};Object.defineProperty(t,"__esModule",{value:!0}),t.ClusterAdapterWithHeartbeat=t.ClusterAdapter=t.MessageType=void 0;let o=s(70807),r=s(28161),c=s(6113),p=(0,r.debug)("socket.io-adapter");function l(){return(0,c.randomBytes)(8).toString("hex")}function u(e){return{rooms:[...e.rooms],except:[...e.except],flags:e.flags}}function d(e){return{rooms:new Set(e.rooms),except:new Set(e.except),flags:e.flags}}(i=a=t.MessageType||(t.MessageType={}))[i.INITIAL_HEARTBEAT=1]="INITIAL_HEARTBEAT",i[i.HEARTBEAT=2]="HEARTBEAT",i[i.BROADCAST=3]="BROADCAST",i[i.SOCKETS_JOIN=4]="SOCKETS_JOIN",i[i.SOCKETS_LEAVE=5]="SOCKETS_LEAVE",i[i.DISCONNECT_SOCKETS=6]="DISCONNECT_SOCKETS",i[i.FETCH_SOCKETS=7]="FETCH_SOCKETS",i[i.FETCH_SOCKETS_RESPONSE=8]="FETCH_SOCKETS_RESPONSE",i[i.SERVER_SIDE_EMIT=9]="SERVER_SIDE_EMIT",i[i.SERVER_SIDE_EMIT_RESPONSE=10]="SERVER_SIDE_EMIT_RESPONSE",i[i.BROADCAST_CLIENT_COUNT=11]="BROADCAST_CLIENT_COUNT",i[i.BROADCAST_ACK=12]="BROADCAST_ACK",i[i.ADAPTER_CLOSE=13]="ADAPTER_CLOSE";class m extends o.Adapter{constructor(e){super(e),this.requests=new Map,this.ackRequests=new Map,this.uid=l()}onMessage(e,t){if(e.uid===this.uid)return p("[%s] ignore message from self",this.uid);switch(p("[%s] new event of type %d from %s",this.uid,e.type,e.uid),e.type){case a.BROADCAST:if(void 0!==e.data.requestId)super.broadcastWithAck(e.data.packet,d(e.data.opts),t=>{p("[%s] waiting for %d client acknowledgements",this.uid,t),this.publishResponse(e.uid,{type:a.BROADCAST_CLIENT_COUNT,data:{requestId:e.data.requestId,clientCount:t}})},t=>{p("[%s] received acknowledgement with value %j",this.uid,t),this.publishResponse(e.uid,{type:a.BROADCAST_ACK,data:{requestId:e.data.requestId,packet:t}})});else{let s=e.data.packet,i=d(e.data.opts);this.addOffsetIfNecessary(s,i,t),super.broadcast(s,i)}break;case a.SOCKETS_JOIN:super.addSockets(d(e.data.opts),e.data.rooms);break;case a.SOCKETS_LEAVE:super.delSockets(d(e.data.opts),e.data.rooms);break;case a.DISCONNECT_SOCKETS:super.disconnectSockets(d(e.data.opts),e.data.close);break;case a.FETCH_SOCKETS:p("[%s] calling fetchSockets with opts %j",this.uid,e.data.opts),super.fetchSockets(d(e.data.opts)).then(t=>{this.publishResponse(e.uid,{type:a.FETCH_SOCKETS_RESPONSE,data:{requestId:e.data.requestId,sockets:t.map(e=>{let t=e.handshake,{sessionStore:s}=t,i=n(t,["sessionStore"]);return{id:e.id,handshake:i,rooms:[...e.rooms],data:e.data}})}})});break;case a.SERVER_SIDE_EMIT:{let t=e.data.packet;if(!(void 0!==e.data.requestId)){this.nsp._onServerSideEmit(t);return}let s=!1;this.nsp._onServerSideEmit([...t,t=>{s||(s=!0,p("[%s] calling acknowledgement with %j",this.uid,t),this.publishResponse(e.uid,{type:a.SERVER_SIDE_EMIT_RESPONSE,data:{requestId:e.data.requestId,packet:t}}))}]);break}case a.BROADCAST_CLIENT_COUNT:case a.BROADCAST_ACK:case a.FETCH_SOCKETS_RESPONSE:case a.SERVER_SIDE_EMIT_RESPONSE:this.onResponse(e);break;default:p("[%s] unknown message type: %s",this.uid,e.type)}}onResponse(e){var t,s;let i=e.data.requestId;switch(p("[%s] received response %s to request %s",this.uid,e.type,i),e.type){case a.BROADCAST_CLIENT_COUNT:null===(t=this.ackRequests.get(i))||void 0===t||t.clientCountCallback(e.data.clientCount);break;case a.BROADCAST_ACK:null===(s=this.ackRequests.get(i))||void 0===s||s.ack(e.data.packet);break;case a.FETCH_SOCKETS_RESPONSE:{let t=this.requests.get(i);if(!t)return;t.current++,e.data.sockets.forEach(e=>t.responses.push(e)),t.current===t.expected&&(clearTimeout(t.timeout),t.resolve(t.responses),this.requests.delete(i));break}case a.SERVER_SIDE_EMIT_RESPONSE:{let t=this.requests.get(i);if(!t)return;t.current++,t.responses.push(e.data.packet),t.current===t.expected&&(clearTimeout(t.timeout),t.resolve(null,t.responses),this.requests.delete(i));break}default:p("[%s] unknown response type: %s",this.uid,e.type)}}async broadcast(e,t){var s;if(!(null===(s=t.flags)||void 0===s?void 0:s.local))try{let s=await this.publishAndReturnOffset({type:a.BROADCAST,data:{packet:e,opts:u(t)}});this.addOffsetIfNecessary(e,t,s)}catch(e){return p("[%s] error while broadcasting message: %s",this.uid,e.message)}super.broadcast(e,t)}addOffsetIfNecessary(e,t,s){var i;if(!this.nsp.server.opts.connectionStateRecovery)return;let a=2===e.type,n=void 0===e.id,o=(null===(i=t.flags)||void 0===i?void 0:i.volatile)===void 0;a&&n&&o&&e.data.push(s)}broadcastWithAck(e,t,s,i){var n;if(!(null===(n=null==t?void 0:t.flags)||void 0===n?void 0:n.local)){let n=l();this.ackRequests.set(n,{clientCountCallback:s,ack:i}),this.publish({type:a.BROADCAST,data:{packet:e,requestId:n,opts:u(t)}}),setTimeout(()=>{this.ackRequests.delete(n)},t.flags.timeout)}super.broadcastWithAck(e,t,s,i)}async addSockets(e,t){var s;if(!(null===(s=e.flags)||void 0===s?void 0:s.local))try{await this.publishAndReturnOffset({type:a.SOCKETS_JOIN,data:{opts:u(e),rooms:t}})}catch(e){p("[%s] error while publishing message: %s",this.uid,e.message)}super.addSockets(e,t)}async delSockets(e,t){var s;if(!(null===(s=e.flags)||void 0===s?void 0:s.local))try{await this.publishAndReturnOffset({type:a.SOCKETS_LEAVE,data:{opts:u(e),rooms:t}})}catch(e){p("[%s] error while publishing message: %s",this.uid,e.message)}super.delSockets(e,t)}async disconnectSockets(e,t){var s;if(!(null===(s=e.flags)||void 0===s?void 0:s.local))try{await this.publishAndReturnOffset({type:a.DISCONNECT_SOCKETS,data:{opts:u(e),close:t}})}catch(e){p("[%s] error while publishing message: %s",this.uid,e.message)}super.disconnectSockets(e,t)}async fetchSockets(e){var t;let[s,i]=await Promise.all([super.fetchSockets(e),this.serverCount()]),n=i-1;if((null===(t=e.flags)||void 0===t?void 0:t.local)||n<=0)return s;let o=l();return new Promise((t,i)=>{let r=setTimeout(()=>{let e=this.requests.get(o);e&&(i(Error(`timeout reached: only ${e.current} responses received out of ${e.expected}`)),this.requests.delete(o))},e.flags.timeout||5e3),c={type:a.FETCH_SOCKETS,resolve:t,timeout:r,current:0,expected:n,responses:s};this.requests.set(o,c),this.publish({type:a.FETCH_SOCKETS,data:{opts:u(e),requestId:o}})})}async serverSideEmit(e){if("function"!=typeof e[e.length-1])return this.publish({type:a.SERVER_SIDE_EMIT,data:{packet:e}});let t=e.pop(),s=await this.serverCount()-1;if(p('[%s] waiting for %d responses to "serverSideEmit" request',this.uid,s),s<=0)return t(null,[]);let i=l(),n=setTimeout(()=>{let e=this.requests.get(i);e&&(t(Error(`timeout reached: only ${e.current} responses received out of ${e.expected}`),e.responses),this.requests.delete(i))},5e3),o={type:a.SERVER_SIDE_EMIT,resolve:t,timeout:n,current:0,expected:s,responses:[]};this.requests.set(i,o),this.publish({type:a.SERVER_SIDE_EMIT,data:{requestId:i,packet:e}})}publish(e){this.publishAndReturnOffset(e).catch(e=>{p("[%s] error while publishing message: %s",this.uid,e)})}publishAndReturnOffset(e){return e.uid=this.uid,e.nsp=this.nsp.name,this.doPublish(e)}publishResponse(e,t){t.uid=this.uid,t.nsp=this.nsp.name,this.doPublishResponse(e,t).catch(e=>{p("[%s] error while publishing response: %s",this.uid,e)})}}t.ClusterAdapter=m;class h extends m{constructor(e,t){super(e),this.nodesMap=new Map,this.customRequests=new Map,this._opts=Object.assign({heartbeatInterval:5e3,heartbeatTimeout:1e4},t),this.cleanupTimer=setInterval(()=>{let e=Date.now();this.nodesMap.forEach((t,s)=>{e-t>this._opts.heartbeatTimeout&&(p("[%s] node %s seems down",this.uid,s),this.removeNode(s))})},1e3)}init(){this.publish({type:a.INITIAL_HEARTBEAT})}scheduleHeartbeat(){this.heartbeatTimer?this.heartbeatTimer.refresh():this.heartbeatTimer=setTimeout(()=>{this.publish({type:a.HEARTBEAT})},this._opts.heartbeatInterval)}close(){this.publish({type:a.ADAPTER_CLOSE}),clearTimeout(this.heartbeatTimer),this.cleanupTimer&&clearInterval(this.cleanupTimer)}onMessage(e,t){if(e.uid===this.uid)return p("[%s] ignore message from self",this.uid);switch(e.uid&&"emitter"!==e.uid&&this.nodesMap.set(e.uid,Date.now()),p("[%s] new event of type %d from %s",this.uid,e.type,e.uid),e.type){case a.INITIAL_HEARTBEAT:this.publish({type:a.HEARTBEAT});break;case a.HEARTBEAT:break;case a.ADAPTER_CLOSE:this.removeNode(e.uid);break;default:super.onMessage(e,t)}}serverCount(){return Promise.resolve(1+this.nodesMap.size)}publish(e){return this.scheduleHeartbeat(),super.publish(e)}async serverSideEmit(e){if("function"!=typeof e[e.length-1])return this.publish({type:a.SERVER_SIDE_EMIT,data:{packet:e}});let t=e.pop(),s=this.nodesMap.size;if(p('[%s] waiting for %d responses to "serverSideEmit" request',this.uid,s),s<=0)return t(null,[]);let i=l(),n=setTimeout(()=>{let e=this.customRequests.get(i);e&&(t(Error(`timeout reached: missing ${e.missingUids.size} responses`),e.responses),this.customRequests.delete(i))},5e3),o={type:a.SERVER_SIDE_EMIT,resolve:t,timeout:n,missingUids:new Set([...this.nodesMap.keys()]),responses:[]};this.customRequests.set(i,o),this.publish({type:a.SERVER_SIDE_EMIT,data:{requestId:i,packet:e}})}async fetchSockets(e){var t;let[s,i]=await Promise.all([super.fetchSockets({rooms:e.rooms,except:e.except,flags:{local:!0}}),this.serverCount()]);if((null===(t=e.flags)||void 0===t?void 0:t.local)||i-1<=0)return s;let n=l();return new Promise((t,i)=>{let o=setTimeout(()=>{let e=this.customRequests.get(n);e&&(i(Error(`timeout reached: missing ${e.missingUids.size} responses`)),this.customRequests.delete(n))},e.flags.timeout||5e3),r={type:a.FETCH_SOCKETS,resolve:t,timeout:o,missingUids:new Set([...this.nodesMap.keys()]),responses:s};this.customRequests.set(n,r),this.publish({type:a.FETCH_SOCKETS,data:{opts:u(e),requestId:n}})})}onResponse(e){let t=e.data.requestId;switch(p("[%s] received response %s to request %s",this.uid,e.type,t),e.type){case a.FETCH_SOCKETS_RESPONSE:{let s=this.customRequests.get(t);if(!s)return;e.data.sockets.forEach(e=>s.responses.push(e)),s.missingUids.delete(e.uid),0===s.missingUids.size&&(clearTimeout(s.timeout),s.resolve(s.responses),this.customRequests.delete(t));break}case a.SERVER_SIDE_EMIT_RESPONSE:{let s=this.customRequests.get(t);if(!s)return;s.responses.push(e.data.packet),s.missingUids.delete(e.uid),0===s.missingUids.size&&(clearTimeout(s.timeout),s.resolve(null,s.responses),this.customRequests.delete(t));break}default:super.onResponse(e)}}removeNode(e){this.customRequests.forEach((t,s)=>{t.missingUids.delete(e),0===t.missingUids.size&&(clearTimeout(t.timeout),t.type===a.FETCH_SOCKETS?t.resolve(t.responses):t.type===a.SERVER_SIDE_EMIT&&t.resolve(null,t.responses),this.customRequests.delete(s))}),this.nodesMap.delete(e)}}t.ClusterAdapterWithHeartbeat=h},18244:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.yeast=t.decode=t.encode=void 0;let s="**********ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_".split(""),i={},a=0,n=0,o;function r(e){let t="";do t=s[e%64]+t,e=Math.floor(e/64);while(e>0);return t}for(t.encode=r,t.decode=function(e){let t=0;for(n=0;n<e.length;n++)t=64*t+i[e.charAt(n)];return t},t.yeast=function(){let e=r(+new Date);return e!==o?(a=0,o=e):e+"."+r(a++)};n<64;n++)i[s[n]]=n},70807:(e,t,s)=>{"use strict";var i;Object.defineProperty(t,"__esModule",{value:!0}),t.SessionAwareAdapter=t.Adapter=void 0;let a=s(82361),n=s(18244),o=s(27495),r="function"==typeof(null===(i=null==o?void 0:o.Sender)||void 0===i?void 0:i.frame);class c extends a.EventEmitter{constructor(e){super(),this.nsp=e,this.rooms=new Map,this.sids=new Map,this.encoder=e.server.encoder}init(){}close(){}serverCount(){return Promise.resolve(1)}addAll(e,t){for(let s of(this.sids.has(e)||this.sids.set(e,new Set),t))this.sids.get(e).add(s),this.rooms.has(s)||(this.rooms.set(s,new Set),this.emit("create-room",s)),this.rooms.get(s).has(e)||(this.rooms.get(s).add(e),this.emit("join-room",s,e))}del(e,t){this.sids.has(e)&&this.sids.get(e).delete(t),this._del(t,e)}_del(e,t){let s=this.rooms.get(e);null!=s&&(s.delete(t)&&this.emit("leave-room",e,t),0===s.size&&this.rooms.delete(e)&&this.emit("delete-room",e))}delAll(e){if(this.sids.has(e)){for(let t of this.sids.get(e))this._del(t,e);this.sids.delete(e)}}broadcast(e,t){let s=t.flags||{},i={preEncoded:!0,volatile:s.volatile,compress:s.compress};e.nsp=this.nsp.name;let a=this._encode(e,i);this.apply(t,t=>{"function"==typeof t.notifyOutgoingListeners&&t.notifyOutgoingListeners(e),t.client.writeToEngine(a,i)})}broadcastWithAck(e,t,s,i){let a=t.flags||{},n={preEncoded:!0,volatile:a.volatile,compress:a.compress};e.nsp=this.nsp.name,e.id=this.nsp._ids++;let o=this._encode(e,n),r=0;this.apply(t,t=>{r++,t.acks.set(e.id,i),"function"==typeof t.notifyOutgoingListeners&&t.notifyOutgoingListeners(e),t.client.writeToEngine(o,n)}),s(r)}_encode(e,t){let s=this.encoder.encode(e);if(r&&1===s.length&&"string"==typeof s[0]){let e=Buffer.from("4"+s[0]);t.wsPreEncodedFrame=o.Sender.frame(e,{readOnly:!1,mask:!1,rsv1:!1,opcode:1,fin:!0})}return s}sockets(e){let t=new Set;return this.apply({rooms:e},e=>{t.add(e.id)}),Promise.resolve(t)}socketRooms(e){return this.sids.get(e)}fetchSockets(e){let t=[];return this.apply(e,e=>{t.push(e)}),Promise.resolve(t)}addSockets(e,t){this.apply(e,e=>{e.join(t)})}delSockets(e,t){this.apply(e,e=>{t.forEach(t=>e.leave(t))})}disconnectSockets(e,t){this.apply(e,e=>{e.disconnect(t)})}apply(e,t){let s=e.rooms,i=this.computeExceptSids(e.except);if(s.size){let e=new Set;for(let a of s)if(this.rooms.has(a))for(let s of this.rooms.get(a)){if(e.has(s)||i.has(s))continue;let a=this.nsp.sockets.get(s);a&&(t(a),e.add(s))}}else for(let[e]of this.sids){if(i.has(e))continue;let s=this.nsp.sockets.get(e);s&&t(s)}}computeExceptSids(e){let t=new Set;if(e&&e.size>0)for(let s of e)this.rooms.has(s)&&this.rooms.get(s).forEach(e=>t.add(e));return t}serverSideEmit(e){console.warn("this adapter does not support the serverSideEmit() functionality")}persistSession(e){}restoreSession(e,t){return null}}t.Adapter=c;class p extends c{constructor(e){super(e),this.nsp=e,this.sessions=new Map,this.packets=[],this.maxDisconnectionDuration=e.server.opts.connectionStateRecovery.maxDisconnectionDuration,setInterval(()=>{let e=Date.now()-this.maxDisconnectionDuration;this.sessions.forEach((t,s)=>{t.disconnectedAt<e&&this.sessions.delete(s)});for(let t=this.packets.length-1;t>=0;t--)if(this.packets[t].emittedAt<e){this.packets.splice(0,t+1);break}},6e4).unref()}persistSession(e){e.disconnectedAt=Date.now(),this.sessions.set(e.pid,e)}restoreSession(e,t){let s=this.sessions.get(e);if(!s)return null;if(s.disconnectedAt+this.maxDisconnectionDuration<Date.now())return this.sessions.delete(e),null;let i=this.packets.findIndex(e=>e.id===t);if(-1===i)return null;let a=[];for(let e=i+1;e<this.packets.length;e++){let t=this.packets[e];(function(e,t){let s=0===t.rooms.size||e.some(e=>t.rooms.has(e)),i=e.every(e=>!t.except.has(e));return s&&i})(s.rooms,t.opts)&&a.push(t.data)}return Promise.resolve(Object.assign(Object.assign({},s),{missedPackets:a}))}broadcast(e,t){var s;let i=2===e.type,a=void 0===e.id,o=(null===(s=t.flags)||void 0===s?void 0:s.volatile)===void 0;if(i&&a&&o){let s=(0,n.yeast)();e.data.push(s),this.packets.push({id:s,opts:t,data:e.data,emittedAt:Date.now()})}super.broadcast(e,t)}}t.SessionAwareAdapter=p},21605:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.MessageType=t.ClusterAdapterWithHeartbeat=t.ClusterAdapter=t.SessionAwareAdapter=t.Adapter=void 0;var i=s(70807);Object.defineProperty(t,"Adapter",{enumerable:!0,get:function(){return i.Adapter}}),Object.defineProperty(t,"SessionAwareAdapter",{enumerable:!0,get:function(){return i.SessionAwareAdapter}});var a=s(99310);Object.defineProperty(t,"ClusterAdapter",{enumerable:!0,get:function(){return a.ClusterAdapter}}),Object.defineProperty(t,"ClusterAdapterWithHeartbeat",{enumerable:!0,get:function(){return a.ClusterAdapterWithHeartbeat}}),Object.defineProperty(t,"MessageType",{enumerable:!0,get:function(){return a.MessageType}})},5336:(e,t,s)=>{t.formatArgs=function(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;let s="color: "+this.color;t.splice(1,0,s,"color: inherit");let i=0,a=0;t[0].replace(/%[a-zA-Z%]/g,e=>{"%%"!==e&&(i++,"%c"===e&&(a=i))}),t.splice(a,0,s)},t.save=function(e){try{e?t.storage.setItem("debug",e):t.storage.removeItem("debug")}catch(e){}},t.load=function(){let e;try{e=t.storage.getItem("debug")}catch(e){}return!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG),e},t.useColors=function(){let e;return!("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))&&("undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof navigator&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/))},t.storage=function(){try{return localStorage}catch(e){}}(),t.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.log=console.debug||console.log||(()=>{}),e.exports=s(25190)(t);let{formatters:i}=e.exports;i.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},25190:(e,t,s)=>{e.exports=function(e){function t(e){let s,a,n;let o=null;function r(...e){if(!r.enabled)return;let i=Number(new Date),a=i-(s||i);r.diff=a,r.prev=s,r.curr=i,s=i,e[0]=t.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let n=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,(s,i)=>{if("%%"===s)return"%";n++;let a=t.formatters[i];if("function"==typeof a){let t=e[n];s=a.call(r,t),e.splice(n,1),n--}return s}),t.formatArgs.call(r,e),(r.log||t.log).apply(r,e)}return r.namespace=e,r.useColors=t.useColors(),r.color=t.selectColor(e),r.extend=i,r.destroy=t.destroy,Object.defineProperty(r,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==o?o:(a!==t.namespaces&&(a=t.namespaces,n=t.enabled(e)),n),set:e=>{o=e}}),"function"==typeof t.init&&t.init(r),r}function i(e,s){let i=t(this.namespace+(void 0===s?":":s)+e);return i.log=this.log,i}function a(e){return e.toString().substring(2,e.toString().length-2).replace(/\.\*\?$/,"*")}return t.debug=t,t.default=t,t.coerce=function(e){return e instanceof Error?e.stack||e.message:e},t.disable=function(){let e=[...t.names.map(a),...t.skips.map(a).map(e=>"-"+e)].join(",");return t.enable(""),e},t.enable=function(e){let s;t.save(e),t.namespaces=e,t.names=[],t.skips=[];let i=("string"==typeof e?e:"").split(/[\s,]+/),a=i.length;for(s=0;s<a;s++)i[s]&&("-"===(e=i[s].replace(/\*/g,".*?"))[0]?t.skips.push(RegExp("^"+e.slice(1)+"$")):t.names.push(RegExp("^"+e+"$")))},t.enabled=function(e){let s,i;if("*"===e[e.length-1])return!0;for(s=0,i=t.skips.length;s<i;s++)if(t.skips[s].test(e))return!1;for(s=0,i=t.names.length;s<i;s++)if(t.names[s].test(e))return!0;return!1},t.humanize=s(87553),t.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach(s=>{t[s]=e[s]}),t.names=[],t.skips=[],t.formatters={},t.selectColor=function(e){let s=0;for(let t=0;t<e.length;t++)s=(s<<5)-s+e.charCodeAt(t)|0;return t.colors[Math.abs(s)%t.colors.length]},t.enable(t.load()),t}},28161:(e,t,s)=>{"undefined"==typeof process||"renderer"===process.type||process.__nwjs?e.exports=s(5336):e.exports=s(1144)},1144:(e,t,s)=>{let i=s(76224),a=s(73837);t.init=function(e){e.inspectOpts={};let s=Object.keys(t.inspectOpts);for(let i=0;i<s.length;i++)e.inspectOpts[s[i]]=t.inspectOpts[s[i]]},t.log=function(...e){return process.stderr.write(a.formatWithOptions(t.inspectOpts,...e)+"\n")},t.formatArgs=function(s){let{namespace:i,useColors:a}=this;if(a){let t=this.color,a="\x1b[3"+(t<8?t:"8;5;"+t),n=`  ${a};1m${i} \u001B[0m`;s[0]=n+s[0].split("\n").join("\n"+n),s.push(a+"m+"+e.exports.humanize(this.diff)+"\x1b[0m")}else s[0]=(t.inspectOpts.hideDate?"":new Date().toISOString()+" ")+i+" "+s[0]},t.save=function(e){e?process.env.DEBUG=e:delete process.env.DEBUG},t.load=function(){return process.env.DEBUG},t.useColors=function(){return"colors"in t.inspectOpts?!!t.inspectOpts.colors:i.isatty(process.stderr.fd)},t.destroy=a.deprecate(()=>{},"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."),t.colors=[6,2,3,4,5,1];try{let e=s(46052);e&&(e.stderr||e).level>=2&&(t.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221])}catch(e){}t.inspectOpts=Object.keys(process.env).filter(e=>/^debug_/i.test(e)).reduce((e,t)=>{let s=t.substring(6).toLowerCase().replace(/_([a-z])/g,(e,t)=>t.toUpperCase()),i=process.env[t];return i=!!/^(yes|on|true|enabled)$/i.test(i)||!/^(no|off|false|disabled)$/i.test(i)&&("null"===i?null:Number(i)),e[s]=i,e},{}),e.exports=s(25190)(t);let{formatters:n}=e.exports;n.o=function(e){return this.inspectOpts.colors=this.useColors,a.inspect(e,this.inspectOpts).split("\n").map(e=>e.trim()).join(" ")},n.O=function(e){return this.inspectOpts.colors=this.useColors,a.inspect(e,this.inspectOpts)}},27495:(e,t,s)=>{"use strict";let i=s(86208);i.createWebSocketStream=s(27185),i.Server=s(78948),i.Receiver=s(83825),i.Sender=s(61407),i.WebSocket=i,i.WebSocketServer=i.Server,e.exports=i},2213:(e,t,s)=>{"use strict";let{EMPTY_BUFFER:i}=s(77376),a=Buffer[Symbol.species];function n(e,t,s,i,a){for(let n=0;n<a;n++)s[i+n]=e[n]^t[3&n]}function o(e,t){for(let s=0;s<e.length;s++)e[s]^=t[3&s]}if(e.exports={concat:function(e,t){if(0===e.length)return i;if(1===e.length)return e[0];let s=Buffer.allocUnsafe(t),n=0;for(let t=0;t<e.length;t++){let i=e[t];s.set(i,n),n+=i.length}return n<t?new a(s.buffer,s.byteOffset,n):s},mask:n,toArrayBuffer:function(e){return e.length===e.buffer.byteLength?e.buffer:e.buffer.slice(e.byteOffset,e.byteOffset+e.length)},toBuffer:function e(t){let s;return(e.readOnly=!0,Buffer.isBuffer(t))?t:(t instanceof ArrayBuffer?s=new a(t):ArrayBuffer.isView(t)?s=new a(t.buffer,t.byteOffset,t.byteLength):(s=Buffer.from(t),e.readOnly=!1),s)},unmask:o},!process.env.WS_NO_BUFFER_UTIL)try{let t=s(30107);e.exports.mask=function(e,s,i,a,o){o<48?n(e,s,i,a,o):t.mask(e,s,i,a,o)},e.exports.unmask=function(e,s){e.length<32?o(e,s):t.unmask(e,s)}}catch(e){}},77376:e=>{"use strict";e.exports={BINARY_TYPES:["nodebuffer","arraybuffer","fragments"],EMPTY_BUFFER:Buffer.alloc(0),GUID:"258EAFA5-E914-47DA-95CA-C5AB0DC85B11",kForOnEventAttribute:Symbol("kIsForOnEventAttribute"),kListener:Symbol("kListener"),kStatusCode:Symbol("status-code"),kWebSocket:Symbol("websocket"),NOOP:()=>{}}},65909:(e,t,s)=>{"use strict";let{kForOnEventAttribute:i,kListener:a}=s(77376),n=Symbol("kCode"),o=Symbol("kData"),r=Symbol("kError"),c=Symbol("kMessage"),p=Symbol("kReason"),l=Symbol("kTarget"),u=Symbol("kType"),d=Symbol("kWasClean");class m{constructor(e){this[l]=null,this[u]=e}get target(){return this[l]}get type(){return this[u]}}Object.defineProperty(m.prototype,"target",{enumerable:!0}),Object.defineProperty(m.prototype,"type",{enumerable:!0});class h extends m{constructor(e,t={}){super(e),this[n]=void 0===t.code?0:t.code,this[p]=void 0===t.reason?"":t.reason,this[d]=void 0!==t.wasClean&&t.wasClean}get code(){return this[n]}get reason(){return this[p]}get wasClean(){return this[d]}}Object.defineProperty(h.prototype,"code",{enumerable:!0}),Object.defineProperty(h.prototype,"reason",{enumerable:!0}),Object.defineProperty(h.prototype,"wasClean",{enumerable:!0});class f extends m{constructor(e,t={}){super(e),this[r]=void 0===t.error?null:t.error,this[c]=void 0===t.message?"":t.message}get error(){return this[r]}get message(){return this[c]}}Object.defineProperty(f.prototype,"error",{enumerable:!0}),Object.defineProperty(f.prototype,"message",{enumerable:!0});class v extends m{constructor(e,t={}){super(e),this[o]=void 0===t.data?null:t.data}get data(){return this[o]}}function x(e,t,s){"object"==typeof e&&e.handleEvent?e.handleEvent.call(e,s):e.call(t,s)}Object.defineProperty(v.prototype,"data",{enumerable:!0}),e.exports={CloseEvent:h,ErrorEvent:f,Event:m,EventTarget:{addEventListener(e,t,s={}){let n;for(let n of this.listeners(e))if(!s[i]&&n[a]===t&&!n[i])return;if("message"===e)n=function(e,s){let i=new v("message",{data:s?e:e.toString()});i[l]=this,x(t,this,i)};else if("close"===e)n=function(e,s){let i=new h("close",{code:e,reason:s.toString(),wasClean:this._closeFrameReceived&&this._closeFrameSent});i[l]=this,x(t,this,i)};else if("error"===e)n=function(e){let s=new f("error",{error:e,message:e.message});s[l]=this,x(t,this,s)};else{if("open"!==e)return;n=function(){let e=new m("open");e[l]=this,x(t,this,e)}}n[i]=!!s[i],n[a]=t,s.once?this.once(e,n):this.on(e,n)},removeEventListener(e,t){for(let s of this.listeners(e))if(s[a]===t&&!s[i]){this.removeListener(e,s);break}}},MessageEvent:v}},71701:(e,t,s)=>{"use strict";let{tokenChars:i}=s(14829);function a(e,t,s){void 0===e[t]?e[t]=[s]:e[t].push(s)}e.exports={format:function(e){return Object.keys(e).map(t=>{let s=e[t];return Array.isArray(s)||(s=[s]),s.map(e=>[t].concat(Object.keys(e).map(t=>{let s=e[t];return Array.isArray(s)||(s=[s]),s.map(e=>!0===e?t:`${t}=${e}`).join("; ")})).join("; ")).join(", ")}).join(", ")},parse:function(e){let t,s;let n=Object.create(null),o=Object.create(null),r=!1,c=!1,p=!1,l=-1,u=-1,d=-1,m=0;for(;m<e.length;m++)if(u=e.charCodeAt(m),void 0===t){if(-1===d&&1===i[u])-1===l&&(l=m);else if(0!==m&&(32===u||9===u))-1===d&&-1!==l&&(d=m);else if(59===u||44===u){if(-1===l)throw SyntaxError(`Unexpected character at index ${m}`);-1===d&&(d=m);let s=e.slice(l,d);44===u?(a(n,s,o),o=Object.create(null)):t=s,l=d=-1}else throw SyntaxError(`Unexpected character at index ${m}`)}else if(void 0===s){if(-1===d&&1===i[u])-1===l&&(l=m);else if(32===u||9===u)-1===d&&-1!==l&&(d=m);else if(59===u||44===u){if(-1===l)throw SyntaxError(`Unexpected character at index ${m}`);-1===d&&(d=m),a(o,e.slice(l,d),!0),44===u&&(a(n,t,o),o=Object.create(null),t=void 0),l=d=-1}else if(61===u&&-1!==l&&-1===d)s=e.slice(l,m),l=d=-1;else throw SyntaxError(`Unexpected character at index ${m}`)}else if(c){if(1!==i[u])throw SyntaxError(`Unexpected character at index ${m}`);-1===l?l=m:r||(r=!0),c=!1}else if(p){if(1===i[u])-1===l&&(l=m);else if(34===u&&-1!==l)p=!1,d=m;else if(92===u)c=!0;else throw SyntaxError(`Unexpected character at index ${m}`)}else if(34===u&&61===e.charCodeAt(m-1))p=!0;else if(-1===d&&1===i[u])-1===l&&(l=m);else if(-1!==l&&(32===u||9===u))-1===d&&(d=m);else if(59===u||44===u){if(-1===l)throw SyntaxError(`Unexpected character at index ${m}`);-1===d&&(d=m);let i=e.slice(l,d);r&&(i=i.replace(/\\/g,""),r=!1),a(o,s,i),44===u&&(a(n,t,o),o=Object.create(null),t=void 0),s=void 0,l=d=-1}else throw SyntaxError(`Unexpected character at index ${m}`);if(-1===l||p||32===u||9===u)throw SyntaxError("Unexpected end of input");-1===d&&(d=m);let h=e.slice(l,d);return void 0===t?a(n,h,o):(void 0===s?a(o,h,!0):r?a(o,s,h.replace(/\\/g,"")):a(o,s,h),a(n,t,o)),n}}},46913:e=>{"use strict";let t=Symbol("kDone"),s=Symbol("kRun");class i{constructor(e){this[t]=()=>{this.pending--,this[s]()},this.concurrency=e||1/0,this.jobs=[],this.pending=0}add(e){this.jobs.push(e),this[s]()}[s](){if(this.pending!==this.concurrency&&this.jobs.length){let e=this.jobs.shift();this.pending++,e(this[t])}}}e.exports=i},3334:(e,t,s)=>{"use strict";let i;let a=s(59796),n=s(2213),o=s(46913),{kStatusCode:r}=s(77376),c=Buffer[Symbol.species],p=Buffer.from([0,0,255,255]),l=Symbol("permessage-deflate"),u=Symbol("total-length"),d=Symbol("callback"),m=Symbol("buffers"),h=Symbol("error");class f{constructor(e,t,s){this._maxPayload=0|s,this._options=e||{},this._threshold=void 0!==this._options.threshold?this._options.threshold:1024,this._isServer=!!t,this._deflate=null,this._inflate=null,this.params=null,i||(i=new o(void 0!==this._options.concurrencyLimit?this._options.concurrencyLimit:10))}static get extensionName(){return"permessage-deflate"}offer(){let e={};return this._options.serverNoContextTakeover&&(e.server_no_context_takeover=!0),this._options.clientNoContextTakeover&&(e.client_no_context_takeover=!0),this._options.serverMaxWindowBits&&(e.server_max_window_bits=this._options.serverMaxWindowBits),this._options.clientMaxWindowBits?e.client_max_window_bits=this._options.clientMaxWindowBits:null==this._options.clientMaxWindowBits&&(e.client_max_window_bits=!0),e}accept(e){return e=this.normalizeParams(e),this.params=this._isServer?this.acceptAsServer(e):this.acceptAsClient(e),this.params}cleanup(){if(this._inflate&&(this._inflate.close(),this._inflate=null),this._deflate){let e=this._deflate[d];this._deflate.close(),this._deflate=null,e&&e(Error("The deflate stream was closed while data was being processed"))}}acceptAsServer(e){let t=this._options,s=e.find(e=>(!1!==t.serverNoContextTakeover||!e.server_no_context_takeover)&&(!e.server_max_window_bits||!1!==t.serverMaxWindowBits&&("number"!=typeof t.serverMaxWindowBits||!(t.serverMaxWindowBits>e.server_max_window_bits)))&&("number"!=typeof t.clientMaxWindowBits||!!e.client_max_window_bits));if(!s)throw Error("None of the extension offers can be accepted");return t.serverNoContextTakeover&&(s.server_no_context_takeover=!0),t.clientNoContextTakeover&&(s.client_no_context_takeover=!0),"number"==typeof t.serverMaxWindowBits&&(s.server_max_window_bits=t.serverMaxWindowBits),"number"==typeof t.clientMaxWindowBits?s.client_max_window_bits=t.clientMaxWindowBits:(!0===s.client_max_window_bits||!1===t.clientMaxWindowBits)&&delete s.client_max_window_bits,s}acceptAsClient(e){let t=e[0];if(!1===this._options.clientNoContextTakeover&&t.client_no_context_takeover)throw Error('Unexpected parameter "client_no_context_takeover"');if(t.client_max_window_bits){if(!1===this._options.clientMaxWindowBits||"number"==typeof this._options.clientMaxWindowBits&&t.client_max_window_bits>this._options.clientMaxWindowBits)throw Error('Unexpected or invalid parameter "client_max_window_bits"')}else"number"==typeof this._options.clientMaxWindowBits&&(t.client_max_window_bits=this._options.clientMaxWindowBits);return t}normalizeParams(e){return e.forEach(e=>{Object.keys(e).forEach(t=>{let s=e[t];if(s.length>1)throw Error(`Parameter "${t}" must have only a single value`);if(s=s[0],"client_max_window_bits"===t){if(!0!==s){let e=+s;if(!Number.isInteger(e)||e<8||e>15)throw TypeError(`Invalid value for parameter "${t}": ${s}`);s=e}else if(!this._isServer)throw TypeError(`Invalid value for parameter "${t}": ${s}`)}else if("server_max_window_bits"===t){let e=+s;if(!Number.isInteger(e)||e<8||e>15)throw TypeError(`Invalid value for parameter "${t}": ${s}`);s=e}else if("client_no_context_takeover"===t||"server_no_context_takeover"===t){if(!0!==s)throw TypeError(`Invalid value for parameter "${t}": ${s}`)}else throw Error(`Unknown parameter "${t}"`);e[t]=s})}),e}decompress(e,t,s){i.add(i=>{this._decompress(e,t,(e,t)=>{i(),s(e,t)})})}compress(e,t,s){i.add(i=>{this._compress(e,t,(e,t)=>{i(),s(e,t)})})}_decompress(e,t,s){let i=this._isServer?"client":"server";if(!this._inflate){let e=`${i}_max_window_bits`,t="number"!=typeof this.params[e]?a.Z_DEFAULT_WINDOWBITS:this.params[e];this._inflate=a.createInflateRaw({...this._options.zlibInflateOptions,windowBits:t}),this._inflate[l]=this,this._inflate[u]=0,this._inflate[m]=[],this._inflate.on("error",g),this._inflate.on("data",x)}this._inflate[d]=s,this._inflate.write(e),t&&this._inflate.write(p),this._inflate.flush(()=>{let e=this._inflate[h];if(e){this._inflate.close(),this._inflate=null,s(e);return}let a=n.concat(this._inflate[m],this._inflate[u]);this._inflate._readableState.endEmitted?(this._inflate.close(),this._inflate=null):(this._inflate[u]=0,this._inflate[m]=[],t&&this.params[`${i}_no_context_takeover`]&&this._inflate.reset()),s(null,a)})}_compress(e,t,s){let i=this._isServer?"server":"client";if(!this._deflate){let e=`${i}_max_window_bits`,t="number"!=typeof this.params[e]?a.Z_DEFAULT_WINDOWBITS:this.params[e];this._deflate=a.createDeflateRaw({...this._options.zlibDeflateOptions,windowBits:t}),this._deflate[u]=0,this._deflate[m]=[],this._deflate.on("data",v)}this._deflate[d]=s,this._deflate.write(e),this._deflate.flush(a.Z_SYNC_FLUSH,()=>{if(!this._deflate)return;let e=n.concat(this._deflate[m],this._deflate[u]);t&&(e=new c(e.buffer,e.byteOffset,e.length-4)),this._deflate[d]=null,this._deflate[u]=0,this._deflate[m]=[],t&&this.params[`${i}_no_context_takeover`]&&this._deflate.reset(),s(null,e)})}}function v(e){this[m].push(e),this[u]+=e.length}function x(e){if(this[u]+=e.length,this[l]._maxPayload<1||this[u]<=this[l]._maxPayload){this[m].push(e);return}this[h]=RangeError("Max payload size exceeded"),this[h].code="WS_ERR_UNSUPPORTED_MESSAGE_LENGTH",this[h][r]=1009,this.removeListener("data",x),this.reset()}function g(e){this[l]._inflate=null,e[r]=1007,this[d](e)}e.exports=f},83825:(e,t,s)=>{"use strict";let{Writable:i}=s(12781),a=s(3334),{BINARY_TYPES:n,EMPTY_BUFFER:o,kStatusCode:r,kWebSocket:c}=s(77376),{concat:p,toArrayBuffer:l,unmask:u}=s(2213),{isValidStatusCode:d,isValidUTF8:m}=s(14829),h=Buffer[Symbol.species];class f extends i{constructor(e={}){super(),this._allowSynchronousEvents=void 0===e.allowSynchronousEvents||e.allowSynchronousEvents,this._binaryType=e.binaryType||n[0],this._extensions=e.extensions||{},this._isServer=!!e.isServer,this._maxPayload=0|e.maxPayload,this._skipUTF8Validation=!!e.skipUTF8Validation,this[c]=void 0,this._bufferedBytes=0,this._buffers=[],this._compressed=!1,this._payloadLength=0,this._mask=void 0,this._fragmented=0,this._masked=!1,this._fin=!1,this._opcode=0,this._totalPayloadLength=0,this._messageLength=0,this._fragments=[],this._errored=!1,this._loop=!1,this._state=0}_write(e,t,s){if(8===this._opcode&&0==this._state)return s();this._bufferedBytes+=e.length,this._buffers.push(e),this.startLoop(s)}consume(e){if(this._bufferedBytes-=e,e===this._buffers[0].length)return this._buffers.shift();if(e<this._buffers[0].length){let t=this._buffers[0];return this._buffers[0]=new h(t.buffer,t.byteOffset+e,t.length-e),new h(t.buffer,t.byteOffset,e)}let t=Buffer.allocUnsafe(e);do{let s=this._buffers[0],i=t.length-e;e>=s.length?t.set(this._buffers.shift(),i):(t.set(new Uint8Array(s.buffer,s.byteOffset,e),i),this._buffers[0]=new h(s.buffer,s.byteOffset+e,s.length-e)),e-=s.length}while(e>0);return t}startLoop(e){this._loop=!0;do switch(this._state){case 0:this.getInfo(e);break;case 1:this.getPayloadLength16(e);break;case 2:this.getPayloadLength64(e);break;case 3:this.getMask();break;case 4:this.getData(e);break;case 5:case 6:this._loop=!1;return}while(this._loop);this._errored||e()}getInfo(e){if(this._bufferedBytes<2){this._loop=!1;return}let t=this.consume(2);if((48&t[0])!=0){e(this.createError(RangeError,"RSV2 and RSV3 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_2_3"));return}let s=(64&t[0])==64;if(s&&!this._extensions[a.extensionName]){e(this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1"));return}if(this._fin=(128&t[0])==128,this._opcode=15&t[0],this._payloadLength=127&t[1],0===this._opcode){if(s){e(this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1"));return}if(!this._fragmented){e(this.createError(RangeError,"invalid opcode 0",!0,1002,"WS_ERR_INVALID_OPCODE"));return}this._opcode=this._fragmented}else if(1===this._opcode||2===this._opcode){if(this._fragmented){e(this.createError(RangeError,`invalid opcode ${this._opcode}`,!0,1002,"WS_ERR_INVALID_OPCODE"));return}this._compressed=s}else if(this._opcode>7&&this._opcode<11){if(!this._fin){e(this.createError(RangeError,"FIN must be set",!0,1002,"WS_ERR_EXPECTED_FIN"));return}if(s){e(this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1"));return}if(this._payloadLength>125||8===this._opcode&&1===this._payloadLength){e(this.createError(RangeError,`invalid payload length ${this._payloadLength}`,!0,1002,"WS_ERR_INVALID_CONTROL_PAYLOAD_LENGTH"));return}}else{e(this.createError(RangeError,`invalid opcode ${this._opcode}`,!0,1002,"WS_ERR_INVALID_OPCODE"));return}if(this._fin||this._fragmented||(this._fragmented=this._opcode),this._masked=(128&t[1])==128,this._isServer){if(!this._masked){e(this.createError(RangeError,"MASK must be set",!0,1002,"WS_ERR_EXPECTED_MASK"));return}}else if(this._masked){e(this.createError(RangeError,"MASK must be clear",!0,1002,"WS_ERR_UNEXPECTED_MASK"));return}126===this._payloadLength?this._state=1:127===this._payloadLength?this._state=2:this.haveLength(e)}getPayloadLength16(e){if(this._bufferedBytes<2){this._loop=!1;return}this._payloadLength=this.consume(2).readUInt16BE(0),this.haveLength(e)}getPayloadLength64(e){if(this._bufferedBytes<8){this._loop=!1;return}let t=this.consume(8),s=t.readUInt32BE(0);if(s>2097151){e(this.createError(RangeError,"Unsupported WebSocket frame: payload length > 2^53 - 1",!1,1009,"WS_ERR_UNSUPPORTED_DATA_PAYLOAD_LENGTH"));return}this._payloadLength=***********s+t.readUInt32BE(4),this.haveLength(e)}haveLength(e){if(this._payloadLength&&this._opcode<8&&(this._totalPayloadLength+=this._payloadLength,this._totalPayloadLength>this._maxPayload&&this._maxPayload>0)){e(this.createError(RangeError,"Max payload size exceeded",!1,1009,"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH"));return}this._masked?this._state=3:this._state=4}getMask(){if(this._bufferedBytes<4){this._loop=!1;return}this._mask=this.consume(4),this._state=4}getData(e){let t=o;if(this._payloadLength){if(this._bufferedBytes<this._payloadLength){this._loop=!1;return}t=this.consume(this._payloadLength),this._masked&&(this._mask[0]|this._mask[1]|this._mask[2]|this._mask[3])!=0&&u(t,this._mask)}if(this._opcode>7){this.controlMessage(t,e);return}if(this._compressed){this._state=5,this.decompress(t,e);return}t.length&&(this._messageLength=this._totalPayloadLength,this._fragments.push(t)),this.dataMessage(e)}decompress(e,t){this._extensions[a.extensionName].decompress(e,this._fin,(e,s)=>{if(e)return t(e);if(s.length){if(this._messageLength+=s.length,this._messageLength>this._maxPayload&&this._maxPayload>0){t(this.createError(RangeError,"Max payload size exceeded",!1,1009,"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH"));return}this._fragments.push(s)}this.dataMessage(t),0===this._state&&this.startLoop(t)})}dataMessage(e){if(!this._fin){this._state=0;return}let t=this._messageLength,s=this._fragments;if(this._totalPayloadLength=0,this._messageLength=0,this._fragmented=0,this._fragments=[],2===this._opcode){let i;i="nodebuffer"===this._binaryType?p(s,t):"arraybuffer"===this._binaryType?l(p(s,t)):s,this._allowSynchronousEvents?(this.emit("message",i,!0),this._state=0):(this._state=6,setImmediate(()=>{this.emit("message",i,!0),this._state=0,this.startLoop(e)}))}else{let i=p(s,t);if(!this._skipUTF8Validation&&!m(i)){e(this.createError(Error,"invalid UTF-8 sequence",!0,1007,"WS_ERR_INVALID_UTF8"));return}5===this._state||this._allowSynchronousEvents?(this.emit("message",i,!1),this._state=0):(this._state=6,setImmediate(()=>{this.emit("message",i,!1),this._state=0,this.startLoop(e)}))}}controlMessage(e,t){if(8===this._opcode){if(0===e.length)this._loop=!1,this.emit("conclude",1005,o),this.end();else{let s=e.readUInt16BE(0);if(!d(s)){t(this.createError(RangeError,`invalid status code ${s}`,!0,1002,"WS_ERR_INVALID_CLOSE_CODE"));return}let i=new h(e.buffer,e.byteOffset+2,e.length-2);if(!this._skipUTF8Validation&&!m(i)){t(this.createError(Error,"invalid UTF-8 sequence",!0,1007,"WS_ERR_INVALID_UTF8"));return}this._loop=!1,this.emit("conclude",s,i),this.end()}this._state=0;return}this._allowSynchronousEvents?(this.emit(9===this._opcode?"ping":"pong",e),this._state=0):(this._state=6,setImmediate(()=>{this.emit(9===this._opcode?"ping":"pong",e),this._state=0,this.startLoop(t)}))}createError(e,t,s,i,a){this._loop=!1,this._errored=!0;let n=new e(s?`Invalid WebSocket frame: ${t}`:t);return Error.captureStackTrace(n,this.createError),n.code=a,n[r]=i,n}}e.exports=f},61407:(e,t,s)=>{"use strict";let i;let{Duplex:a}=s(12781),{randomFillSync:n}=s(6113),o=s(3334),{EMPTY_BUFFER:r}=s(77376),{isValidStatusCode:c}=s(14829),{mask:p,toBuffer:l}=s(2213),u=Symbol("kByteLength"),d=Buffer.alloc(4),m=8192;class h{constructor(e,t,s){this._extensions=t||{},s&&(this._generateMask=s,this._maskBuffer=Buffer.alloc(4)),this._socket=e,this._firstFragment=!0,this._compress=!1,this._bufferedBytes=0,this._deflating=!1,this._queue=[]}static frame(e,t){let s,a;let o=!1,r=2,c=!1;t.mask&&(s=t.maskBuffer||d,t.generateMask?t.generateMask(s):(8192===m&&(void 0===i&&(i=Buffer.alloc(8192)),n(i,0,8192),m=0),s[0]=i[m++],s[1]=i[m++],s[2]=i[m++],s[3]=i[m++]),c=(s[0]|s[1]|s[2]|s[3])==0,r=6),"string"==typeof e?a=(!t.mask||c)&&void 0!==t[u]?t[u]:(e=Buffer.from(e)).length:(a=e.length,o=t.mask&&t.readOnly&&!c);let l=a;a>=65536?(r+=8,l=127):a>125&&(r+=2,l=126);let h=Buffer.allocUnsafe(o?a+r:r);return(h[0]=t.fin?128|t.opcode:t.opcode,t.rsv1&&(h[0]|=64),h[1]=l,126===l?h.writeUInt16BE(a,2):127===l&&(h[2]=h[3]=0,h.writeUIntBE(a,4,6)),t.mask)?(h[1]|=128,h[r-4]=s[0],h[r-3]=s[1],h[r-2]=s[2],h[r-1]=s[3],c)?[h,e]:o?(p(e,s,h,r,a),[h]):(p(e,s,e,0,a),[h,e]):[h,e]}close(e,t,s,i){let a;if(void 0===e)a=r;else if("number"==typeof e&&c(e)){if(void 0!==t&&t.length){let s=Buffer.byteLength(t);if(s>123)throw RangeError("The message must not be greater than 123 bytes");(a=Buffer.allocUnsafe(2+s)).writeUInt16BE(e,0),"string"==typeof t?a.write(t,2):a.set(t,2)}else(a=Buffer.allocUnsafe(2)).writeUInt16BE(e,0)}else throw TypeError("First argument must be a valid error code number");let n={[u]:a.length,fin:!0,generateMask:this._generateMask,mask:s,maskBuffer:this._maskBuffer,opcode:8,readOnly:!1,rsv1:!1};this._deflating?this.enqueue([this.dispatch,a,!1,n,i]):this.sendFrame(h.frame(a,n),i)}ping(e,t,s){let i,a;if("string"==typeof e?(i=Buffer.byteLength(e),a=!1):(i=(e=l(e)).length,a=l.readOnly),i>125)throw RangeError("The data size must not be greater than 125 bytes");let n={[u]:i,fin:!0,generateMask:this._generateMask,mask:t,maskBuffer:this._maskBuffer,opcode:9,readOnly:a,rsv1:!1};this._deflating?this.enqueue([this.dispatch,e,!1,n,s]):this.sendFrame(h.frame(e,n),s)}pong(e,t,s){let i,a;if("string"==typeof e?(i=Buffer.byteLength(e),a=!1):(i=(e=l(e)).length,a=l.readOnly),i>125)throw RangeError("The data size must not be greater than 125 bytes");let n={[u]:i,fin:!0,generateMask:this._generateMask,mask:t,maskBuffer:this._maskBuffer,opcode:10,readOnly:a,rsv1:!1};this._deflating?this.enqueue([this.dispatch,e,!1,n,s]):this.sendFrame(h.frame(e,n),s)}send(e,t,s){let i,a;let n=this._extensions[o.extensionName],r=t.binary?2:1,c=t.compress;if("string"==typeof e?(i=Buffer.byteLength(e),a=!1):(i=(e=l(e)).length,a=l.readOnly),this._firstFragment?(this._firstFragment=!1,c&&n&&n.params[n._isServer?"server_no_context_takeover":"client_no_context_takeover"]&&(c=i>=n._threshold),this._compress=c):(c=!1,r=0),t.fin&&(this._firstFragment=!0),n){let n={[u]:i,fin:t.fin,generateMask:this._generateMask,mask:t.mask,maskBuffer:this._maskBuffer,opcode:r,readOnly:a,rsv1:c};this._deflating?this.enqueue([this.dispatch,e,this._compress,n,s]):this.dispatch(e,this._compress,n,s)}else this.sendFrame(h.frame(e,{[u]:i,fin:t.fin,generateMask:this._generateMask,mask:t.mask,maskBuffer:this._maskBuffer,opcode:r,readOnly:a,rsv1:!1}),s)}dispatch(e,t,s,i){if(!t){this.sendFrame(h.frame(e,s),i);return}let a=this._extensions[o.extensionName];this._bufferedBytes+=s[u],this._deflating=!0,a.compress(e,s.fin,(e,t)=>{if(this._socket.destroyed){let e=Error("The socket was closed while data was being compressed");"function"==typeof i&&i(e);for(let t=0;t<this._queue.length;t++){let s=this._queue[t],i=s[s.length-1];"function"==typeof i&&i(e)}return}this._bufferedBytes-=s[u],this._deflating=!1,s.readOnly=!1,this.sendFrame(h.frame(t,s),i),this.dequeue()})}dequeue(){for(;!this._deflating&&this._queue.length;){let e=this._queue.shift();this._bufferedBytes-=e[3][u],Reflect.apply(e[0],this,e.slice(1))}}enqueue(e){this._bufferedBytes+=e[3][u],this._queue.push(e)}sendFrame(e,t){2===e.length?(this._socket.cork(),this._socket.write(e[0]),this._socket.write(e[1],t),this._socket.uncork()):this._socket.write(e[0],t)}}e.exports=h},27185:(e,t,s)=>{"use strict";let{Duplex:i}=s(12781);function a(e){e.emit("close")}function n(){!this.destroyed&&this._writableState.finished&&this.destroy()}function o(e){this.removeListener("error",o),this.destroy(),0===this.listenerCount("error")&&this.emit("error",e)}e.exports=function(e,t){let s=!0,r=new i({...t,autoDestroy:!1,emitClose:!1,objectMode:!1,writableObjectMode:!1});return e.on("message",function(t,s){let i=!s&&r._readableState.objectMode?t.toString():t;r.push(i)||e.pause()}),e.once("error",function(e){r.destroyed||(s=!1,r.destroy(e))}),e.once("close",function(){r.destroyed||r.push(null)}),r._destroy=function(t,i){if(e.readyState===e.CLOSED){i(t),process.nextTick(a,r);return}let n=!1;e.once("error",function(e){n=!0,i(e)}),e.once("close",function(){n||i(t),process.nextTick(a,r)}),s&&e.terminate()},r._final=function(t){if(e.readyState===e.CONNECTING){e.once("open",function(){r._final(t)});return}null!==e._socket&&(e._socket._writableState.finished?(t(),r._readableState.endEmitted&&r.destroy()):(e._socket.once("finish",function(){t()}),e.close()))},r._read=function(){e.isPaused&&e.resume()},r._write=function(t,s,i){if(e.readyState===e.CONNECTING){e.once("open",function(){r._write(t,s,i)});return}e.send(t,i)},r.on("end",n),r.on("error",o),r}},28536:(e,t,s)=>{"use strict";let{tokenChars:i}=s(14829);e.exports={parse:function(e){let t=new Set,s=-1,a=-1,n=0;for(;n<e.length;n++){let o=e.charCodeAt(n);if(-1===a&&1===i[o])-1===s&&(s=n);else if(0!==n&&(32===o||9===o))-1===a&&-1!==s&&(a=n);else if(44===o){if(-1===s)throw SyntaxError(`Unexpected character at index ${n}`);-1===a&&(a=n);let i=e.slice(s,a);if(t.has(i))throw SyntaxError(`The "${i}" subprotocol is duplicated`);t.add(i),s=a=-1}else throw SyntaxError(`Unexpected character at index ${n}`)}if(-1===s||-1!==a)throw SyntaxError("Unexpected end of input");let o=e.slice(s,n);if(t.has(o))throw SyntaxError(`The "${o}" subprotocol is duplicated`);return t.add(o),t}}},14829:(e,t,s)=>{"use strict";let{isUtf8:i}=s(14300);function a(e){let t=e.length,s=0;for(;s<t;)if((128&e[s])==0)s++;else if((224&e[s])==192){if(s+1===t||(192&e[s+1])!=128||(254&e[s])==192)return!1;s+=2}else if((240&e[s])==224){if(s+2>=t||(192&e[s+1])!=128||(192&e[s+2])!=128||224===e[s]&&(224&e[s+1])==128||237===e[s]&&(224&e[s+1])==160)return!1;s+=3}else{if((248&e[s])!=240||s+3>=t||(192&e[s+1])!=128||(192&e[s+2])!=128||(192&e[s+3])!=128||240===e[s]&&(240&e[s+1])==128||244===e[s]&&e[s+1]>143||e[s]>244)return!1;s+=4}return!0}if(e.exports={isValidStatusCode:function(e){return e>=1e3&&e<=1014&&1004!==e&&1005!==e&&1006!==e||e>=3e3&&e<=4999},isValidUTF8:a,tokenChars:[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,1,1,1,1,1,0,0,1,1,0,1,1,0,1,1,1,1,1,1,1,1,1,1,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,0]},i)e.exports.isValidUTF8=function(e){return e.length<24?a(e):i(e)};else if(!process.env.WS_NO_UTF_8_VALIDATE)try{let t=s(47524);e.exports.isValidUTF8=function(e){return e.length<32?a(e):t(e)}}catch(e){}},78948:(e,t,s)=>{"use strict";let i=s(82361),a=s(13685),{Duplex:n}=s(12781),{createHash:o}=s(6113),r=s(71701),c=s(3334),p=s(28536),l=s(86208),{GUID:u,kWebSocket:d}=s(77376),m=/^[+/0-9A-Za-z]{22}==$/;class h extends i{constructor(e,t){if(super(),null==(e={allowSynchronousEvents:!0,autoPong:!0,maxPayload:*********,skipUTF8Validation:!1,perMessageDeflate:!1,handleProtocols:null,clientTracking:!0,verifyClient:null,noServer:!1,backlog:null,server:null,host:null,path:null,port:null,WebSocket:l,...e}).port&&!e.server&&!e.noServer||null!=e.port&&(e.server||e.noServer)||e.server&&e.noServer)throw TypeError('One and only one of the "port", "server", or "noServer" options must be specified');if(null!=e.port?(this._server=a.createServer((e,t)=>{let s=a.STATUS_CODES[426];t.writeHead(426,{"Content-Length":s.length,"Content-Type":"text/plain"}),t.end(s)}),this._server.listen(e.port,e.host,e.backlog,t)):e.server&&(this._server=e.server),this._server){let e=this.emit.bind(this,"connection");this._removeListeners=function(e,t){for(let s of Object.keys(t))e.on(s,t[s]);return function(){for(let s of Object.keys(t))e.removeListener(s,t[s])}}(this._server,{listening:this.emit.bind(this,"listening"),error:this.emit.bind(this,"error"),upgrade:(t,s,i)=>{this.handleUpgrade(t,s,i,e)}})}!0===e.perMessageDeflate&&(e.perMessageDeflate={}),e.clientTracking&&(this.clients=new Set,this._shouldEmitClose=!1),this.options=e,this._state=0}address(){if(this.options.noServer)throw Error('The server is operating in "noServer" mode');return this._server?this._server.address():null}close(e){if(2===this._state){e&&this.once("close",()=>{e(Error("The server is not running"))}),process.nextTick(f,this);return}if(e&&this.once("close",e),1!==this._state){if(this._state=1,this.options.noServer||this.options.server)this._server&&(this._removeListeners(),this._removeListeners=this._server=null),this.clients&&this.clients.size?this._shouldEmitClose=!0:process.nextTick(f,this);else{let e=this._server;this._removeListeners(),this._removeListeners=this._server=null,e.close(()=>{f(this)})}}}shouldHandle(e){if(this.options.path){let t=e.url.indexOf("?");if((-1!==t?e.url.slice(0,t):e.url)!==this.options.path)return!1}return!0}handleUpgrade(e,t,s,i){t.on("error",v);let a=e.headers["sec-websocket-key"],n=e.headers.upgrade,o=+e.headers["sec-websocket-version"];if("GET"!==e.method){g(this,e,t,405,"Invalid HTTP method");return}if(void 0===n||"websocket"!==n.toLowerCase()){g(this,e,t,400,"Invalid Upgrade header");return}if(void 0===a||!m.test(a)){g(this,e,t,400,"Missing or invalid Sec-WebSocket-Key header");return}if(8!==o&&13!==o){g(this,e,t,400,"Missing or invalid Sec-WebSocket-Version header");return}if(!this.shouldHandle(e)){x(t,400);return}let l=e.headers["sec-websocket-protocol"],u=new Set;if(void 0!==l)try{u=p.parse(l)}catch(s){g(this,e,t,400,"Invalid Sec-WebSocket-Protocol header");return}let d=e.headers["sec-websocket-extensions"],h={};if(this.options.perMessageDeflate&&void 0!==d){let s=new c(this.options.perMessageDeflate,!0,this.options.maxPayload);try{let e=r.parse(d);e[c.extensionName]&&(s.accept(e[c.extensionName]),h[c.extensionName]=s)}catch(s){g(this,e,t,400,"Invalid or unacceptable Sec-WebSocket-Extensions header");return}}if(this.options.verifyClient){let n={origin:e.headers[`${8===o?"sec-websocket-origin":"origin"}`],secure:!!(e.socket.authorized||e.socket.encrypted),req:e};if(2===this.options.verifyClient.length){this.options.verifyClient(n,(n,o,r,c)=>{if(!n)return x(t,o||401,r,c);this.completeUpgrade(h,a,u,e,t,s,i)});return}if(!this.options.verifyClient(n))return x(t,401)}this.completeUpgrade(h,a,u,e,t,s,i)}completeUpgrade(e,t,s,i,a,n,p){if(!a.readable||!a.writable)return a.destroy();if(a[d])throw Error("server.handleUpgrade() was called more than once with the same socket, possibly due to a misconfiguration");if(this._state>0)return x(a,503);let l=o("sha1").update(t+u).digest("base64"),m=["HTTP/1.1 101 Switching Protocols","Upgrade: websocket","Connection: Upgrade",`Sec-WebSocket-Accept: ${l}`],h=new this.options.WebSocket(null,void 0,this.options);if(s.size){let e=this.options.handleProtocols?this.options.handleProtocols(s,i):s.values().next().value;e&&(m.push(`Sec-WebSocket-Protocol: ${e}`),h._protocol=e)}if(e[c.extensionName]){let t=e[c.extensionName].params,s=r.format({[c.extensionName]:[t]});m.push(`Sec-WebSocket-Extensions: ${s}`),h._extensions=e}this.emit("headers",m,i),a.write(m.concat("\r\n").join("\r\n")),a.removeListener("error",v),h.setSocket(a,n,{allowSynchronousEvents:this.options.allowSynchronousEvents,maxPayload:this.options.maxPayload,skipUTF8Validation:this.options.skipUTF8Validation}),this.clients&&(this.clients.add(h),h.on("close",()=>{this.clients.delete(h),this._shouldEmitClose&&!this.clients.size&&process.nextTick(f,this)})),p(h,i)}}function f(e){e._state=2,e.emit("close")}function v(){this.destroy()}function x(e,t,s,i){s=s||a.STATUS_CODES[t],i={Connection:"close","Content-Type":"text/html","Content-Length":Buffer.byteLength(s),...i},e.once("finish",e.destroy),e.end(`HTTP/1.1 ${t} ${a.STATUS_CODES[t]}\r
`+Object.keys(i).map(e=>`${e}: ${i[e]}`).join("\r\n")+"\r\n\r\n"+s)}function g(e,t,s,i,a){if(e.listenerCount("wsClientError")){let i=Error(a);Error.captureStackTrace(i,g),e.emit("wsClientError",i,s,t)}else x(s,i,a)}e.exports=h},86208:(e,t,s)=>{"use strict";let i=s(82361),a=s(95687),n=s(13685),o=s(41808),r=s(24404),{randomBytes:c,createHash:p}=s(6113),{Duplex:l,Readable:u}=s(12781),{URL:d}=s(57310),m=s(3334),h=s(83825),f=s(61407),{BINARY_TYPES:v,EMPTY_BUFFER:x,GUID:g,kForOnEventAttribute:b,kListener:y,kStatusCode:_,kWebSocket:w,NOOP:k}=s(77376),{EventTarget:{addEventListener:S,removeEventListener:C}}=s(65909),{format:E,parse:O}=s(71701),{toBuffer:T}=s(2213),A=Symbol("kAborted"),j=[8,13],R=["CONNECTING","OPEN","CLOSING","CLOSED"],N=/^[!#$%&'*+\-.0-9A-Z^_`|a-z~]+$/;class P extends i{constructor(e,t,s){super(),this._binaryType=v[0],this._closeCode=1006,this._closeFrameReceived=!1,this._closeFrameSent=!1,this._closeMessage=x,this._closeTimer=null,this._extensions={},this._paused=!1,this._protocol="",this._readyState=P.CONNECTING,this._receiver=null,this._sender=null,this._socket=null,null!==e?(this._bufferedAmount=0,this._isServer=!1,this._redirects=0,void 0===t?t=[]:Array.isArray(t)||("object"==typeof t&&null!==t?(s=t,t=[]):t=[t]),function e(t,s,i,o){let r,l,u,h;let f={allowSynchronousEvents:!0,autoPong:!0,protocolVersion:j[1],maxPayload:*********,skipUTF8Validation:!1,perMessageDeflate:!0,followRedirects:!1,maxRedirects:10,...o,socketPath:void 0,hostname:void 0,protocol:void 0,timeout:void 0,method:"GET",host:void 0,path:void 0,port:void 0};if(t._autoPong=f.autoPong,!j.includes(f.protocolVersion))throw RangeError(`Unsupported protocol version: ${f.protocolVersion} (supported versions: ${j.join(", ")})`);if(s instanceof d)r=s;else try{r=new d(s)}catch(e){throw SyntaxError(`Invalid URL: ${s}`)}"http:"===r.protocol?r.protocol="ws:":"https:"===r.protocol&&(r.protocol="wss:"),t._url=r.href;let v="wss:"===r.protocol,x="ws+unix:"===r.protocol;if("ws:"===r.protocol||v||x?x&&!r.pathname?l="The URL's pathname is empty":r.hash&&(l="The URL contains a fragment identifier"):l='The URL\'s protocol must be one of "ws:", "wss:", "http:", "https", or "ws+unix:"',l){let e=SyntaxError(l);if(0===t._redirects)throw e;F(t,e);return}let b=v?443:80,y=c(16).toString("base64"),_=v?a.request:n.request,w=new Set;if(f.createConnection=f.createConnection||(v?B:I),f.defaultPort=f.defaultPort||b,f.port=r.port||b,f.host=r.hostname.startsWith("[")?r.hostname.slice(1,-1):r.hostname,f.headers={...f.headers,"Sec-WebSocket-Version":f.protocolVersion,"Sec-WebSocket-Key":y,Connection:"Upgrade",Upgrade:"websocket"},f.path=r.pathname+r.search,f.timeout=f.handshakeTimeout,f.perMessageDeflate&&(u=new m(!0!==f.perMessageDeflate?f.perMessageDeflate:{},!1,f.maxPayload),f.headers["Sec-WebSocket-Extensions"]=E({[m.extensionName]:u.offer()})),i.length){for(let e of i){if("string"!=typeof e||!N.test(e)||w.has(e))throw SyntaxError("An invalid or duplicated subprotocol was specified");w.add(e)}f.headers["Sec-WebSocket-Protocol"]=i.join(",")}if(f.origin&&(f.protocolVersion<13?f.headers["Sec-WebSocket-Origin"]=f.origin:f.headers.Origin=f.origin),(r.username||r.password)&&(f.auth=`${r.username}:${r.password}`),x){let e=f.path.split(":");f.socketPath=e[0],f.path=e[1]}if(f.followRedirects){if(0===t._redirects){t._originalIpc=x,t._originalSecure=v,t._originalHostOrSocketPath=x?f.socketPath:r.host;let e=o&&o.headers;if(o={...o,headers:{}},e)for(let[t,s]of Object.entries(e))o.headers[t.toLowerCase()]=s}else if(0===t.listenerCount("redirect")){let e=x?!!t._originalIpc&&f.socketPath===t._originalHostOrSocketPath:!t._originalIpc&&r.host===t._originalHostOrSocketPath;e&&(!t._originalSecure||v)||(delete f.headers.authorization,delete f.headers.cookie,e||delete f.headers.host,f.auth=void 0)}f.auth&&!o.headers.authorization&&(o.headers.authorization="Basic "+Buffer.from(f.auth).toString("base64")),h=t._req=_(f),t._redirects&&t.emit("redirect",t.url,h)}else h=t._req=_(f);f.timeout&&h.on("timeout",()=>{L(t,h,"Opening handshake has timed out")}),h.on("error",e=>{null===h||h[A]||(h=t._req=null,F(t,e))}),h.on("response",a=>{let n=a.headers.location,r=a.statusCode;if(n&&f.followRedirects&&r>=300&&r<400){let a;if(++t._redirects>f.maxRedirects){L(t,h,"Maximum redirects exceeded");return}h.abort();try{a=new d(n,s)}catch(e){F(t,SyntaxError(`Invalid URL: ${n}`));return}e(t,a,i,o)}else t.emit("unexpected-response",h,a)||L(t,h,`Unexpected server response: ${a.statusCode}`)}),h.on("upgrade",(e,s,i)=>{let a;if(t.emit("upgrade",e),t.readyState!==P.CONNECTING)return;h=t._req=null;let n=e.headers.upgrade;if(void 0===n||"websocket"!==n.toLowerCase()){L(t,s,"Invalid Upgrade header");return}let o=p("sha1").update(y+g).digest("base64");if(e.headers["sec-websocket-accept"]!==o){L(t,s,"Invalid Sec-WebSocket-Accept header");return}let r=e.headers["sec-websocket-protocol"];if(void 0!==r?w.size?w.has(r)||(a="Server sent an invalid subprotocol"):a="Server sent a subprotocol but none was requested":w.size&&(a="Server sent no subprotocol"),a){L(t,s,a);return}r&&(t._protocol=r);let c=e.headers["sec-websocket-extensions"];if(void 0!==c){let e;if(!u){L(t,s,"Server sent a Sec-WebSocket-Extensions header but no extension was requested");return}try{e=O(c)}catch(e){L(t,s,"Invalid Sec-WebSocket-Extensions header");return}let i=Object.keys(e);if(1!==i.length||i[0]!==m.extensionName){L(t,s,"Server indicated an extension that was not requested");return}try{u.accept(e[m.extensionName])}catch(e){L(t,s,"Invalid Sec-WebSocket-Extensions header");return}t._extensions[m.extensionName]=u}t.setSocket(s,i,{allowSynchronousEvents:f.allowSynchronousEvents,generateMask:f.generateMask,maxPayload:f.maxPayload,skipUTF8Validation:f.skipUTF8Validation})}),f.finishRequest?f.finishRequest(h,t):h.end()}(this,e,t,s)):(this._autoPong=s.autoPong,this._isServer=!0)}get binaryType(){return this._binaryType}set binaryType(e){v.includes(e)&&(this._binaryType=e,this._receiver&&(this._receiver._binaryType=e))}get bufferedAmount(){return this._socket?this._socket._writableState.length+this._sender._bufferedBytes:this._bufferedAmount}get extensions(){return Object.keys(this._extensions).join()}get isPaused(){return this._paused}get onclose(){return null}get onerror(){return null}get onopen(){return null}get onmessage(){return null}get protocol(){return this._protocol}get readyState(){return this._readyState}get url(){return this._url}setSocket(e,t,s){let i=new h({allowSynchronousEvents:s.allowSynchronousEvents,binaryType:this.binaryType,extensions:this._extensions,isServer:this._isServer,maxPayload:s.maxPayload,skipUTF8Validation:s.skipUTF8Validation});this._sender=new f(e,this._extensions,s.generateMask),this._receiver=i,this._socket=e,i[w]=this,e[w]=this,i.on("conclude",D),i.on("drain",U),i.on("error",M),i.on("message",W),i.on("ping",$),i.on("pong",V),e.setTimeout&&e.setTimeout(0),e.setNoDelay&&e.setNoDelay(),t.length>0&&e.unshift(t),e.on("close",G),e.on("data",K),e.on("end",Y),e.on("error",J),this._readyState=P.OPEN,this.emit("open")}emitClose(){if(!this._socket){this._readyState=P.CLOSED,this.emit("close",this._closeCode,this._closeMessage);return}this._extensions[m.extensionName]&&this._extensions[m.extensionName].cleanup(),this._receiver.removeAllListeners(),this._readyState=P.CLOSED,this.emit("close",this._closeCode,this._closeMessage)}close(e,t){if(this.readyState!==P.CLOSED){if(this.readyState===P.CONNECTING){L(this,this._req,"WebSocket was closed before the connection was established");return}if(this.readyState===P.CLOSING){this._closeFrameSent&&(this._closeFrameReceived||this._receiver._writableState.errorEmitted)&&this._socket.end();return}this._readyState=P.CLOSING,this._sender.close(e,t,!this._isServer,e=>{!e&&(this._closeFrameSent=!0,(this._closeFrameReceived||this._receiver._writableState.errorEmitted)&&this._socket.end())}),this._closeTimer=setTimeout(this._socket.destroy.bind(this._socket),3e4)}}pause(){this.readyState!==P.CONNECTING&&this.readyState!==P.CLOSED&&(this._paused=!0,this._socket.pause())}ping(e,t,s){if(this.readyState===P.CONNECTING)throw Error("WebSocket is not open: readyState 0 (CONNECTING)");if("function"==typeof e?(s=e,e=t=void 0):"function"==typeof t&&(s=t,t=void 0),"number"==typeof e&&(e=e.toString()),this.readyState!==P.OPEN){q(this,e,s);return}void 0===t&&(t=!this._isServer),this._sender.ping(e||x,t,s)}pong(e,t,s){if(this.readyState===P.CONNECTING)throw Error("WebSocket is not open: readyState 0 (CONNECTING)");if("function"==typeof e?(s=e,e=t=void 0):"function"==typeof t&&(s=t,t=void 0),"number"==typeof e&&(e=e.toString()),this.readyState!==P.OPEN){q(this,e,s);return}void 0===t&&(t=!this._isServer),this._sender.pong(e||x,t,s)}resume(){this.readyState!==P.CONNECTING&&this.readyState!==P.CLOSED&&(this._paused=!1,this._receiver._writableState.needDrain||this._socket.resume())}send(e,t,s){if(this.readyState===P.CONNECTING)throw Error("WebSocket is not open: readyState 0 (CONNECTING)");if("function"==typeof t&&(s=t,t={}),"number"==typeof e&&(e=e.toString()),this.readyState!==P.OPEN){q(this,e,s);return}let i={binary:"string"!=typeof e,mask:!this._isServer,compress:!0,fin:!0,...t};this._extensions[m.extensionName]||(i.compress=!1),this._sender.send(e||x,i,s)}terminate(){if(this.readyState!==P.CLOSED){if(this.readyState===P.CONNECTING){L(this,this._req,"WebSocket was closed before the connection was established");return}this._socket&&(this._readyState=P.CLOSING,this._socket.destroy())}}}function F(e,t){e._readyState=P.CLOSING,e.emit("error",t),e.emitClose()}function I(e){return e.path=e.socketPath,o.connect(e)}function B(e){return e.path=void 0,e.servername||""===e.servername||(e.servername=o.isIP(e.host)?"":e.host),r.connect(e)}function L(e,t,s){e._readyState=P.CLOSING;let i=Error(s);Error.captureStackTrace(i,L),t.setHeader?(t[A]=!0,t.abort(),t.socket&&!t.socket.destroyed&&t.socket.destroy(),process.nextTick(F,e,i)):(t.destroy(i),t.once("error",e.emit.bind(e,"error")),t.once("close",e.emitClose.bind(e)))}function q(e,t,s){if(t){let s=T(t).length;e._socket?e._sender._bufferedBytes+=s:e._bufferedAmount+=s}if(s){let t=Error(`WebSocket is not open: readyState ${e.readyState} (${R[e.readyState]})`);process.nextTick(s,t)}}function D(e,t){let s=this[w];s._closeFrameReceived=!0,s._closeMessage=t,s._closeCode=e,void 0!==s._socket[w]&&(s._socket.removeListener("data",K),process.nextTick(H,s._socket),1005===e?s.close():s.close(e,t))}function U(){let e=this[w];e.isPaused||e._socket.resume()}function M(e){let t=this[w];void 0!==t._socket[w]&&(t._socket.removeListener("data",K),process.nextTick(H,t._socket),t.close(e[_])),t.emit("error",e)}function z(){this[w].emitClose()}function W(e,t){this[w].emit("message",e,t)}function $(e){let t=this[w];t._autoPong&&t.pong(e,!this._isServer,k),t.emit("ping",e)}function V(e){this[w].emit("pong",e)}function H(e){e.resume()}function G(){let e;let t=this[w];this.removeListener("close",G),this.removeListener("data",K),this.removeListener("end",Y),t._readyState=P.CLOSING,this._readableState.endEmitted||t._closeFrameReceived||t._receiver._writableState.errorEmitted||null===(e=t._socket.read())||t._receiver.write(e),t._receiver.end(),this[w]=void 0,clearTimeout(t._closeTimer),t._receiver._writableState.finished||t._receiver._writableState.errorEmitted?t.emitClose():(t._receiver.on("error",z),t._receiver.on("finish",z))}function K(e){this[w]._receiver.write(e)||this.pause()}function Y(){let e=this[w];e._readyState=P.CLOSING,e._receiver.end(),this.end()}function J(){let e=this[w];this.removeListener("error",J),this.on("error",k),e&&(e._readyState=P.CLOSING,this.destroy())}Object.defineProperty(P,"CONNECTING",{enumerable:!0,value:R.indexOf("CONNECTING")}),Object.defineProperty(P.prototype,"CONNECTING",{enumerable:!0,value:R.indexOf("CONNECTING")}),Object.defineProperty(P,"OPEN",{enumerable:!0,value:R.indexOf("OPEN")}),Object.defineProperty(P.prototype,"OPEN",{enumerable:!0,value:R.indexOf("OPEN")}),Object.defineProperty(P,"CLOSING",{enumerable:!0,value:R.indexOf("CLOSING")}),Object.defineProperty(P.prototype,"CLOSING",{enumerable:!0,value:R.indexOf("CLOSING")}),Object.defineProperty(P,"CLOSED",{enumerable:!0,value:R.indexOf("CLOSED")}),Object.defineProperty(P.prototype,"CLOSED",{enumerable:!0,value:R.indexOf("CLOSED")}),["binaryType","bufferedAmount","extensions","isPaused","protocol","readyState","url"].forEach(e=>{Object.defineProperty(P.prototype,e,{enumerable:!0})}),["open","error","close","message"].forEach(e=>{Object.defineProperty(P.prototype,`on${e}`,{enumerable:!0,get(){for(let t of this.listeners(e))if(t[b])return t[y];return null},set(t){for(let t of this.listeners(e))if(t[b]){this.removeListener(e,t);break}"function"==typeof t&&this.addEventListener(e,t,{[b]:!0})}})}),P.prototype.addEventListener=S,P.prototype.removeEventListener=C,e.exports=P},61811:(e,t,s)=>{t.formatArgs=function(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;let s="color: "+this.color;t.splice(1,0,s,"color: inherit");let i=0,a=0;t[0].replace(/%[a-zA-Z%]/g,e=>{"%%"!==e&&(i++,"%c"===e&&(a=i))}),t.splice(a,0,s)},t.save=function(e){try{e?t.storage.setItem("debug",e):t.storage.removeItem("debug")}catch(e){}},t.load=function(){let e;try{e=t.storage.getItem("debug")}catch(e){}return!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG),e},t.useColors=function(){let e;return!("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))&&("undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof navigator&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/))},t.storage=function(){try{return localStorage}catch(e){}}(),t.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.log=console.debug||console.log||(()=>{}),e.exports=s(58446)(t);let{formatters:i}=e.exports;i.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},58446:(e,t,s)=>{e.exports=function(e){function t(e){let s,a,n;let o=null;function r(...e){if(!r.enabled)return;let i=Number(new Date),a=i-(s||i);r.diff=a,r.prev=s,r.curr=i,s=i,e[0]=t.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let n=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,(s,i)=>{if("%%"===s)return"%";n++;let a=t.formatters[i];if("function"==typeof a){let t=e[n];s=a.call(r,t),e.splice(n,1),n--}return s}),t.formatArgs.call(r,e),(r.log||t.log).apply(r,e)}return r.namespace=e,r.useColors=t.useColors(),r.color=t.selectColor(e),r.extend=i,r.destroy=t.destroy,Object.defineProperty(r,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==o?o:(a!==t.namespaces&&(a=t.namespaces,n=t.enabled(e)),n),set:e=>{o=e}}),"function"==typeof t.init&&t.init(r),r}function i(e,s){let i=t(this.namespace+(void 0===s?":":s)+e);return i.log=this.log,i}function a(e){return e.toString().substring(2,e.toString().length-2).replace(/\.\*\?$/,"*")}return t.debug=t,t.default=t,t.coerce=function(e){return e instanceof Error?e.stack||e.message:e},t.disable=function(){let e=[...t.names.map(a),...t.skips.map(a).map(e=>"-"+e)].join(",");return t.enable(""),e},t.enable=function(e){let s;t.save(e),t.namespaces=e,t.names=[],t.skips=[];let i=("string"==typeof e?e:"").split(/[\s,]+/),a=i.length;for(s=0;s<a;s++)i[s]&&("-"===(e=i[s].replace(/\*/g,".*?"))[0]?t.skips.push(RegExp("^"+e.slice(1)+"$")):t.names.push(RegExp("^"+e+"$")))},t.enabled=function(e){let s,i;if("*"===e[e.length-1])return!0;for(s=0,i=t.skips.length;s<i;s++)if(t.skips[s].test(e))return!1;for(s=0,i=t.names.length;s<i;s++)if(t.names[s].test(e))return!0;return!1},t.humanize=s(87553),t.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach(s=>{t[s]=e[s]}),t.names=[],t.skips=[],t.formatters={},t.selectColor=function(e){let s=0;for(let t=0;t<e.length;t++)s=(s<<5)-s+e.charCodeAt(t)|0;return t.colors[Math.abs(s)%t.colors.length]},t.enable(t.load()),t}},5465:(e,t,s)=>{"undefined"==typeof process||"renderer"===process.type||process.__nwjs?e.exports=s(61811):e.exports=s(18371)},18371:(e,t,s)=>{let i=s(76224),a=s(73837);t.init=function(e){e.inspectOpts={};let s=Object.keys(t.inspectOpts);for(let i=0;i<s.length;i++)e.inspectOpts[s[i]]=t.inspectOpts[s[i]]},t.log=function(...e){return process.stderr.write(a.formatWithOptions(t.inspectOpts,...e)+"\n")},t.formatArgs=function(s){let{namespace:i,useColors:a}=this;if(a){let t=this.color,a="\x1b[3"+(t<8?t:"8;5;"+t),n=`  ${a};1m${i} \u001B[0m`;s[0]=n+s[0].split("\n").join("\n"+n),s.push(a+"m+"+e.exports.humanize(this.diff)+"\x1b[0m")}else s[0]=(t.inspectOpts.hideDate?"":new Date().toISOString()+" ")+i+" "+s[0]},t.save=function(e){e?process.env.DEBUG=e:delete process.env.DEBUG},t.load=function(){return process.env.DEBUG},t.useColors=function(){return"colors"in t.inspectOpts?!!t.inspectOpts.colors:i.isatty(process.stderr.fd)},t.destroy=a.deprecate(()=>{},"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."),t.colors=[6,2,3,4,5,1];try{let e=s(46052);e&&(e.stderr||e).level>=2&&(t.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221])}catch(e){}t.inspectOpts=Object.keys(process.env).filter(e=>/^debug_/i.test(e)).reduce((e,t)=>{let s=t.substring(6).toLowerCase().replace(/_([a-z])/g,(e,t)=>t.toUpperCase()),i=process.env[t];return i=!!/^(yes|on|true|enabled)$/i.test(i)||!/^(no|off|false|disabled)$/i.test(i)&&("null"===i?null:Number(i)),e[s]=i,e},{}),e.exports=s(58446)(t);let{formatters:n}=e.exports;n.o=function(e){return this.inspectOpts.colors=this.useColors,a.inspect(e,this.inspectOpts).split("\n").map(e=>e.trim()).join(" ")},n.O=function(e){return this.inspectOpts.colors=this.useColors,a.inspect(e,this.inspectOpts)}},40021:(e,t,s)=>{t.formatArgs=function(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;let s="color: "+this.color;t.splice(1,0,s,"color: inherit");let i=0,a=0;t[0].replace(/%[a-zA-Z%]/g,e=>{"%%"!==e&&(i++,"%c"===e&&(a=i))}),t.splice(a,0,s)},t.save=function(e){try{e?t.storage.setItem("debug",e):t.storage.removeItem("debug")}catch(e){}},t.load=function(){let e;try{e=t.storage.getItem("debug")}catch(e){}return!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG),e},t.useColors=function(){let e;return!("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))&&("undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof navigator&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/))},t.storage=function(){try{return localStorage}catch(e){}}(),t.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.log=console.debug||console.log||(()=>{}),e.exports=s(38235)(t);let{formatters:i}=e.exports;i.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},38235:(e,t,s)=>{e.exports=function(e){function t(e){let s,a,n;let o=null;function r(...e){if(!r.enabled)return;let i=Number(new Date),a=i-(s||i);r.diff=a,r.prev=s,r.curr=i,s=i,e[0]=t.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let n=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,(s,i)=>{if("%%"===s)return"%";n++;let a=t.formatters[i];if("function"==typeof a){let t=e[n];s=a.call(r,t),e.splice(n,1),n--}return s}),t.formatArgs.call(r,e),(r.log||t.log).apply(r,e)}return r.namespace=e,r.useColors=t.useColors(),r.color=t.selectColor(e),r.extend=i,r.destroy=t.destroy,Object.defineProperty(r,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==o?o:(a!==t.namespaces&&(a=t.namespaces,n=t.enabled(e)),n),set:e=>{o=e}}),"function"==typeof t.init&&t.init(r),r}function i(e,s){let i=t(this.namespace+(void 0===s?":":s)+e);return i.log=this.log,i}function a(e){return e.toString().substring(2,e.toString().length-2).replace(/\.\*\?$/,"*")}return t.debug=t,t.default=t,t.coerce=function(e){return e instanceof Error?e.stack||e.message:e},t.disable=function(){let e=[...t.names.map(a),...t.skips.map(a).map(e=>"-"+e)].join(",");return t.enable(""),e},t.enable=function(e){let s;t.save(e),t.namespaces=e,t.names=[],t.skips=[];let i=("string"==typeof e?e:"").split(/[\s,]+/),a=i.length;for(s=0;s<a;s++)i[s]&&("-"===(e=i[s].replace(/\*/g,".*?"))[0]?t.skips.push(RegExp("^"+e.slice(1)+"$")):t.names.push(RegExp("^"+e+"$")))},t.enabled=function(e){let s,i;if("*"===e[e.length-1])return!0;for(s=0,i=t.skips.length;s<i;s++)if(t.skips[s].test(e))return!1;for(s=0,i=t.names.length;s<i;s++)if(t.names[s].test(e))return!0;return!1},t.humanize=s(87553),t.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach(s=>{t[s]=e[s]}),t.names=[],t.skips=[],t.formatters={},t.selectColor=function(e){let s=0;for(let t=0;t<e.length;t++)s=(s<<5)-s+e.charCodeAt(t)|0;return t.colors[Math.abs(s)%t.colors.length]},t.enable(t.load()),t}},49097:(e,t,s)=>{"undefined"==typeof process||"renderer"===process.type||process.__nwjs?e.exports=s(40021):e.exports=s(70131)},70131:(e,t,s)=>{let i=s(76224),a=s(73837);t.init=function(e){e.inspectOpts={};let s=Object.keys(t.inspectOpts);for(let i=0;i<s.length;i++)e.inspectOpts[s[i]]=t.inspectOpts[s[i]]},t.log=function(...e){return process.stderr.write(a.formatWithOptions(t.inspectOpts,...e)+"\n")},t.formatArgs=function(s){let{namespace:i,useColors:a}=this;if(a){let t=this.color,a="\x1b[3"+(t<8?t:"8;5;"+t),n=`  ${a};1m${i} \u001B[0m`;s[0]=n+s[0].split("\n").join("\n"+n),s.push(a+"m+"+e.exports.humanize(this.diff)+"\x1b[0m")}else s[0]=(t.inspectOpts.hideDate?"":new Date().toISOString()+" ")+i+" "+s[0]},t.save=function(e){e?process.env.DEBUG=e:delete process.env.DEBUG},t.load=function(){return process.env.DEBUG},t.useColors=function(){return"colors"in t.inspectOpts?!!t.inspectOpts.colors:i.isatty(process.stderr.fd)},t.destroy=a.deprecate(()=>{},"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."),t.colors=[6,2,3,4,5,1];try{let e=s(46052);e&&(e.stderr||e).level>=2&&(t.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221])}catch(e){}t.inspectOpts=Object.keys(process.env).filter(e=>/^debug_/i.test(e)).reduce((e,t)=>{let s=t.substring(6).toLowerCase().replace(/_([a-z])/g,(e,t)=>t.toUpperCase()),i=process.env[t];return i=!!/^(yes|on|true|enabled)$/i.test(i)||!/^(no|off|false|disabled)$/i.test(i)&&("null"===i?null:Number(i)),e[s]=i,e},{}),e.exports=s(38235)(t);let{formatters:n}=e.exports;n.o=function(e){return this.inspectOpts.colors=this.useColors,a.inspect(e,this.inspectOpts).split("\n").map(e=>e.trim()).join(" ")},n.O=function(e){return this.inspectOpts.colors=this.useColors,a.inspect(e,this.inspectOpts)}},46052:(e,t,s)=>{"use strict";let i;let a=s(22037),n=s(76224),o=s(10753),{env:r}=process;function c(e){return 0!==e&&{level:e,hasBasic:!0,has256:e>=2,has16m:e>=3}}function p(e,t){if(0===i)return 0;if(o("color=16m")||o("color=full")||o("color=truecolor"))return 3;if(o("color=256"))return 2;if(e&&!t&&void 0===i)return 0;let s=i||0;if("dumb"===r.TERM)return s;if("win32"===process.platform){let e=a.release().split(".");return Number(e[0])>=10&&Number(e[2])>=10586?Number(e[2])>=14931?3:2:1}if("CI"in r)return["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","GITHUB_ACTIONS","BUILDKITE"].some(e=>e in r)||"codeship"===r.CI_NAME?1:s;if("TEAMCITY_VERSION"in r)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(r.TEAMCITY_VERSION)?1:0;if("truecolor"===r.COLORTERM)return 3;if("TERM_PROGRAM"in r){let e=parseInt((r.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(r.TERM_PROGRAM){case"iTerm.app":return e>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(r.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(r.TERM)||"COLORTERM"in r?1:s}o("no-color")||o("no-colors")||o("color=false")||o("color=never")?i=0:(o("color")||o("colors")||o("color=true")||o("color=always"))&&(i=1),"FORCE_COLOR"in r&&(i="true"===r.FORCE_COLOR?1:"false"===r.FORCE_COLOR?0:0===r.FORCE_COLOR.length?1:Math.min(parseInt(r.FORCE_COLOR,10),3)),e.exports={supportsColor:function(e){return c(p(e,e&&e.isTTY))},stdout:c(p(!0,n.isatty(1))),stderr:c(p(!0,n.isatty(2)))}},15198:e=>{"use strict";/*!
 * vary
 * Copyright(c) 2014-2017 Douglas Christopher Wilson
 * MIT Licensed
 */e.exports=function(e,t){if(!e||!e.getHeader||!e.setHeader)throw TypeError("res argument is required");var i=e.getHeader("Vary")||"";(i=s(Array.isArray(i)?i.join(", "):String(i),t))&&e.setHeader("Vary",i)},e.exports.append=s;var t=/^[!#$%&'*+\-.^_`|~0-9A-Za-z]+$/;function s(e,s){if("string"!=typeof e)throw TypeError("header argument is required");if(!s)throw TypeError("field argument is required");for(var a=Array.isArray(s)?s:i(String(s)),n=0;n<a.length;n++)if(!t.test(a[n]))throw TypeError("field argument contains an invalid header name");if("*"===e)return e;var o=e,r=i(e.toLowerCase());if(-1!==a.indexOf("*")||-1!==r.indexOf("*"))return"*";for(var c=0;c<a.length;c++){var p=a[c].toLowerCase();-1===r.indexOf(p)&&(r.push(p),o=o?o+", "+a[c]:a[c])}return o}function i(e){for(var t=0,s=[],i=0,a=0,n=e.length;a<n;a++)switch(e.charCodeAt(a)){case 32:i===t&&(i=t=a+1);break;case 44:s.push(e.substring(i,t)),i=t=a+1;break;default:t=a+1}return s.push(e.substring(i,t)),s}},83371:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ERROR_PACKET=t.PACKET_TYPES_REVERSE=t.PACKET_TYPES=void 0;let s=Object.create(null);t.PACKET_TYPES=s,s.open="0",s.close="1",s.ping="2",s.pong="3",s.message="4",s.upgrade="5",s.noop="6";let i=Object.create(null);t.PACKET_TYPES_REVERSE=i,Object.keys(s).forEach(e=>{i[s[e]]=e}),t.ERROR_PACKET={type:"error",data:"parser error"}},74986:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.decodePacket=void 0;let i=s(83371);t.decodePacket=(e,t)=>{if("string"!=typeof e)return{type:"message",data:a(e,t)};let s=e.charAt(0);return"b"===s?{type:"message",data:a(Buffer.from(e.substring(1),"base64"),t)}:i.PACKET_TYPES_REVERSE[s]?e.length>1?{type:i.PACKET_TYPES_REVERSE[s],data:e.substring(1)}:{type:i.PACKET_TYPES_REVERSE[s]}:i.ERROR_PACKET};let a=(e,t)=>"arraybuffer"===t?e instanceof ArrayBuffer?e:Buffer.isBuffer(e)?e.buffer.slice(e.byteOffset,e.byteOffset+e.byteLength):e.buffer:Buffer.isBuffer(e)?e:Buffer.from(e)},57894:(e,t,s)=>{"use strict";let i;Object.defineProperty(t,"__esModule",{value:!0}),t.encodePacket=void 0,t.encodePacketToBinary=function(e,s){if(e.data instanceof ArrayBuffer||ArrayBuffer.isView(e.data))return s(n(e.data,!1));(0,t.encodePacket)(e,!0,e=>{i||(i=new TextEncoder),s(i.encode(e))})};let a=s(83371);t.encodePacket=({type:e,data:t},s,i)=>i(t instanceof ArrayBuffer||ArrayBuffer.isView(t)?s?t:"b"+n(t,!0).toString("base64"):a.PACKET_TYPES[e]+(t||""));let n=(e,t)=>Buffer.isBuffer(e)||e instanceof Uint8Array&&!t?e:e instanceof ArrayBuffer?Buffer.from(e):Buffer.from(e.buffer,e.byteOffset,e.byteLength)},53167:(e,t,s)=>{"use strict";let i;Object.defineProperty(t,"__esModule",{value:!0}),t.decodePayload=t.decodePacket=t.encodePayload=t.encodePacket=t.protocol=void 0,t.createPacketEncoderStream=function(){return new TransformStream({transform(e,t){(0,a.encodePacketToBinary)(e,s=>{let i;let a=s.length;if(a<126)i=new Uint8Array(1),new DataView(i.buffer).setUint8(0,a);else if(a<65536){i=new Uint8Array(3);let e=new DataView(i.buffer);e.setUint8(0,126),e.setUint16(1,a)}else{i=new Uint8Array(9);let e=new DataView(i.buffer);e.setUint8(0,127),e.setBigUint64(1,BigInt(a))}e.data&&"string"!=typeof e.data&&(i[0]|=128),t.enqueue(i),t.enqueue(s)})}})},t.createPacketDecoderStream=function(e,t){i||(i=new TextDecoder);let s=[],a=0,p=-1,l=!1;return new TransformStream({transform(u,d){for(s.push(u);;){if(0===a){if(1>r(s))break;let e=c(s,1);l=(128&e[0])==128,a=(p=127&e[0])<126?3:126===p?1:2}else if(1===a){if(2>r(s))break;let e=c(s,2);p=new DataView(e.buffer,e.byteOffset,e.length).getUint16(0),a=3}else if(2===a){if(8>r(s))break;let e=c(s,8),t=new DataView(e.buffer,e.byteOffset,e.length),i=t.getUint32(0);if(i>2097151){d.enqueue(o.ERROR_PACKET);break}p=***********i+t.getUint32(4),a=3}else{if(r(s)<p)break;let e=c(s,p);d.enqueue((0,n.decodePacket)(l?e:i.decode(e),t)),a=0}if(0===p||p>e){d.enqueue(o.ERROR_PACKET);break}}}})};let a=s(57894);Object.defineProperty(t,"encodePacket",{enumerable:!0,get:function(){return a.encodePacket}});let n=s(74986);Object.defineProperty(t,"decodePacket",{enumerable:!0,get:function(){return n.decodePacket}});let o=s(83371);function r(e){return e.reduce((e,t)=>e+t.length,0)}function c(e,t){if(e[0].length===t)return e.shift();let s=new Uint8Array(t),i=0;for(let a=0;a<t;a++)s[a]=e[0][i++],i===e[0].length&&(e.shift(),i=0);return e.length&&i<e[0].length&&(e[0]=e[0].slice(i)),s}t.encodePayload=(e,t)=>{let s=e.length,i=Array(s),n=0;e.forEach((e,o)=>{(0,a.encodePacket)(e,!1,e=>{i[o]=e,++n===s&&t(i.join("\x1e"))})})},t.decodePayload=(e,t)=>{let s=e.split("\x1e"),i=[];for(let e=0;e<s.length;e++){let a=(0,n.decodePacket)(s[e],t);if(i.push(a),"error"===a.type)break}return i},t.protocol=4},66028:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.protocol=t.Transport=t.Socket=t.uServer=t.parser=t.transports=t.Server=void 0,t.listen=function(e,t,s){"function"==typeof t&&(s=t,t={});let a=(0,i.createServer)(function(e,t){t.writeHead(501),t.end("Not Implemented")}),n=l(a,t);return n.httpServer=a,a.listen(e,s),n},t.attach=l;let i=s(13685),a=s(83915);Object.defineProperty(t,"Server",{enumerable:!0,get:function(){return a.Server}});let n=s(1246);t.transports=n.default;let o=s(53167);t.parser=o;var r=s(64798);Object.defineProperty(t,"uServer",{enumerable:!0,get:function(){return r.uServer}});var c=s(42742);Object.defineProperty(t,"Socket",{enumerable:!0,get:function(){return c.Socket}});var p=s(22057);function l(e,t){let s=new a.Server(t);return s.attach(e,t),s}Object.defineProperty(t,"Transport",{enumerable:!0,get:function(){return p.Transport}}),t.protocol=o.protocol},29416:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.packets=t.protocol=void 0,t.encodePacket=c,t.encodeBase64Packet=l,t.decodePacket=u,t.decodeBase64Packet=d,t.encodePayload=function(e,t,s){return("function"==typeof t&&(s=t,t=null),t&&a(e))?f(e,s):e.length?void m(e,function(e,s){c(e,t,!1,function(e){s(null,e.length+":"+e)})},function(e,t){return s(t.join(""))}):s("0:")},t.decodePayload=function(e,t,s){if("string"!=typeof e)return x(e,t,s);if("function"==typeof t&&(s=t,t=null),""===e)return s(o,0,1);for(var i,a,n,r="",c=0,p=e.length;c<p;c++){var l=e.charAt(c);if(":"!==l){r+=l;continue}if(""===r||r!=(i=Number(r))||r!=(a=e.slice(c+1,c+1+i)).length)return s(o,0,1);if(a.length){if(n=u(a,t,!1),o.type===n.type&&o.data===n.data)return s(o,0,1);if(!1===s(n,c+i,p))return}c+=i,r=""}if(""!==r)return s(o,0,1)},t.encodePayloadAsBinary=f,t.decodePayloadAsBinary=x;var i=s(84432);t.protocol=3;let a=e=>{for(let t of e)if(t.data instanceof ArrayBuffer||ArrayBuffer.isView(t.data))return!0;return!1};t.packets={open:0,close:1,ping:2,pong:3,message:4,upgrade:5,noop:6};var n=Object.keys(t.packets),o={type:"error",data:"parser error"};let r=Buffer.concat([]);function c(e,s,a,n){if("function"==typeof s&&(n=s,s=null),"function"==typeof a&&(n=a,a=null),Buffer.isBuffer(e.data))return p(e,s,n);if(e.data&&(e.data.buffer||e.data)instanceof ArrayBuffer)return p({type:e.type,data:h(e.data)},s,n);var o=t.packets[e.type];return void 0!==e.data&&(o+=a?i.encode(String(e.data),{strict:!1}):String(e.data)),n(""+o)}function p(e,s,i){if(!s)return l(e,i);var a=e.data,n=Buffer.allocUnsafe(1);return n[0]=t.packets[e.type],i(Buffer.concat([n,a]))}function l(e,s){var i=Buffer.isBuffer(e.data)?e.data:h(e.data),a="b"+t.packets[e.type];return s(a+=i.toString("base64"))}function u(e,t,s){if(void 0===e)return o;if("string"==typeof e)return"b"===(a=e.charAt(0))?d(e.slice(1),t):s&&!1===(e=function(e){try{e=i.decode(e,{strict:!1})}catch(e){return!1}return e}(e))||Number(a)!=a||!n[a]?o:e.length>1?{type:n[a],data:e.slice(1)}:{type:n[a]};if("arraybuffer"===t){var a,r=new Uint8Array(e);return{type:n[a=r[0]],data:r.buffer.slice(1)}}return e instanceof ArrayBuffer&&(e=h(e)),{type:n[a=e[0]],data:e.slice(1)}}function d(e,t){var s=n[e.charAt(0)],i=Buffer.from(e.slice(1),"base64");if("arraybuffer"===t){for(var a=new Uint8Array(i.length),o=0;o<a.length;o++)a[o]=i[o];i=a.buffer}return{type:s,data:i}}function m(e,t,s){let i=Array(e.length),a=0;for(let n=0;n<e.length;n++)t(e[n],(t,o)=>{i[n]=o,++a===e.length&&s(null,i)})}function h(e){var t=e.byteLength||e.length,s=e.byteOffset||0;return Buffer.from(e.buffer||e,s,t)}function f(e,t){if(!e.length)return t(r);m(e,v,function(e,s){return t(Buffer.concat(s))})}function v(e,t){c(e,!0,!0,function(e){var s,i=""+e.length;if("string"==typeof e){(s=Buffer.allocUnsafe(i.length+2))[0]=0;for(var a=0;a<i.length;a++)s[a+1]=parseInt(i[a],10);return s[s.length-1]=255,t(null,Buffer.concat([s,function(e){for(var t=Buffer.allocUnsafe(e.length),s=0,i=e.length;s<i;s++)t.writeUInt8(e.charCodeAt(s),s);return t}(e)]))}(s=Buffer.allocUnsafe(i.length+2))[0]=1;for(var a=0;a<i.length;a++)s[a+1]=parseInt(i[a],10);s[s.length-1]=255,t(null,Buffer.concat([s,e]))})}function x(e,t,s){"function"==typeof t&&(s=t,t=null);for(var i,a=e,n=[];a.length>0;){var r="",c=0===a[0];for(i=1;255!==a[i];i++){if(r.length>310)return s(o,0,1);r+=""+a[i]}a=a.slice(r.length+1);var p=parseInt(r,10),l=a.slice(1,p+1);c&&(l=function(e){for(var t="",s=0,i=e.length;s<i;s++)t+=String.fromCharCode(e[s]);return t}(l)),n.push(l),a=a.slice(p+1)}var d=n.length;for(i=0;i<d;i++)s(u(n[i],t,!0),i,d)}},84432:e=>{/*! https://mths.be/utf8js v2.1.2 by @mathias */var t,s,i,a=String.fromCharCode;function n(e){for(var t,s,i=[],a=0,n=e.length;a<n;)(t=e.charCodeAt(a++))>=55296&&t<=56319&&a<n?(64512&(s=e.charCodeAt(a++)))==56320?i.push(((1023&t)<<10)+(1023&s)+65536):(i.push(t),a--):i.push(t);return i}function o(e,t){if(e>=55296&&e<=57343){if(t)throw Error("Lone surrogate U+"+e.toString(16).toUpperCase()+" is not a scalar value");return!1}return!0}function r(e,t){return a(e>>t&63|128)}function c(){if(i>=s)throw Error("Invalid byte index");var e=255&t[i];if(i++,(192&e)==128)return 63&e;throw Error("Invalid continuation byte")}e.exports={version:"2.1.2",encode:function(e,t){for(var s=!1!==(t=t||{}).strict,i=n(e),c=i.length,p=-1,l="";++p<c;)l+=function(e,t){if((4294967168&e)==0)return a(e);var s="";return(4294965248&e)==0?s=a(e>>6&31|192):(4294901760&e)==0?(o(e,t)||(e=65533),s=a(e>>12&15|224)+r(e,6)):(4292870144&e)==0&&(s=a(e>>18&7|240)+r(e,12)+r(e,6)),s+=a(63&e|128)}(i[p],s);return l},decode:function(e,r){var p,l=!1!==(r=r||{}).strict;s=(t=n(e)).length,i=0;for(var u=[];!1!==(p=function(e){var a,n,r,p;if(i>s)throw Error("Invalid byte index");if(i==s)return!1;if(a=255&t[i],i++,(128&a)==0)return a;if((224&a)==192){if((p=(31&a)<<6|(n=c()))>=128)return p;throw Error("Invalid continuation byte")}if((240&a)==224){if((p=(15&a)<<12|(n=c())<<6|c())>=2048)return o(p,e)?p:65533;throw Error("Invalid continuation byte")}if((248&a)==240&&(p=(7&a)<<18|(n=c())<<12|c()<<6|c())>=65536&&p<=1114111)return p;throw Error("Invalid UTF-8 detected")}(l));)u.push(p);return function(e){for(var t,s=e.length,i=-1,n="";++i<s;)(t=e[i])>65535&&(t-=65536,n+=a(t>>>10&1023|55296),t=56320|1023&t),n+=a(t);return n}(u)}}},83915:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Server=t.BaseServer=void 0;let i=s(63477),a=s(57310),n=s(49324),o=s(1246),r=s(82361),c=s(42742),p=s(34989),l=s(83191),u=s(1670),d=s(7979),m=s(53167),h=(0,p.default)("engine"),f=Symbol("responseHeaders");class v extends r.EventEmitter{constructor(e={}){super(),this.middlewares=[],this.clients={},this.clientsCount=0,this.opts=Object.assign({wsEngine:u.Server,pingTimeout:2e4,pingInterval:25e3,upgradeTimeout:1e4,maxHttpBufferSize:1e6,transports:["polling","websocket"],allowUpgrades:!0,httpCompression:{threshold:1024},cors:!1,allowEIO3:!1},e),e.cookie&&(this.opts.cookie=Object.assign({name:"io",path:"/",httpOnly:!1!==e.cookie.path,sameSite:"lax"},e.cookie)),this.opts.cors&&this.use(s(79879)(this.opts.cors)),e.perMessageDeflate&&(this.opts.perMessageDeflate=Object.assign({threshold:1024},e.perMessageDeflate)),this.init()}_computePath(e){let t=(e.path||"/engine.io").replace(/\/$/,"");return!1!==e.addTrailingSlash&&(t+="/"),t}upgrades(e){return this.opts.allowUpgrades&&o.default[e].upgradesTo||[]}verify(e,t,s){let i=e._query.transport;if(!~this.opts.transports.indexOf(i)||"webtransport"===i)return h('unknown transport "%s"',i),s(g.errors.UNKNOWN_TRANSPORT,{transport:i});if(function(e){if((e+="").length<1)return!1;if(!_[e.charCodeAt(0)])return h('invalid header, index 0, char "%s"',e.charCodeAt(0)),!0;if(e.length<2)return!1;if(!_[e.charCodeAt(1)])return h('invalid header, index 1, char "%s"',e.charCodeAt(1)),!0;if(e.length<3)return!1;if(!_[e.charCodeAt(2)])return h('invalid header, index 2, char "%s"',e.charCodeAt(2)),!0;if(e.length<4)return!1;if(!_[e.charCodeAt(3)])return h('invalid header, index 3, char "%s"',e.charCodeAt(3)),!0;for(let t=4;t<e.length;++t)if(!_[e.charCodeAt(t)])return h('invalid header, index "%i", char "%s"',t,e.charCodeAt(t)),!0;return!1}(e.headers.origin)){let t=e.headers.origin;return e.headers.origin=null,h("origin header invalid"),s(g.errors.BAD_REQUEST,{name:"INVALID_ORIGIN",origin:t})}let a=e._query.sid;if(!a)return"GET"!==e.method?s(g.errors.BAD_HANDSHAKE_METHOD,{method:e.method}):"websocket"!==i||t?this.opts.allowRequest?this.opts.allowRequest(e,(e,t)=>{if(!t)return s(g.errors.FORBIDDEN,{message:e});s()}):s():(h("invalid transport upgrade"),s(g.errors.BAD_REQUEST,{name:"TRANSPORT_HANDSHAKE_ERROR"}));{if(!this.clients.hasOwnProperty(a))return h('unknown sid "%s"',a),s(g.errors.UNKNOWN_SID,{sid:a});let e=this.clients[a].transport.name;if(!t&&e!==i)return h("bad request: unexpected transport without upgrade"),s(g.errors.BAD_REQUEST,{name:"TRANSPORT_MISMATCH",transport:i,previousTransport:e})}s()}use(e){this.middlewares.push(e)}_applyMiddlewares(e,t,s){if(0===this.middlewares.length)return h("no middleware to apply, skipping"),s();let i=a=>{h("applying middleware n\xb0%d",a+1),this.middlewares[a](e,t,e=>{if(e)return s(e);a+1<this.middlewares.length?i(a+1):s()})};i(0)}close(){for(let e in h("closing all open clients"),this.clients)this.clients.hasOwnProperty(e)&&this.clients[e].close(!0);return this.cleanup(),this}generateId(e){return n.generateId()}async handshake(e,t,s){let i;let a="4"===t._query.EIO?4:3;if(3===a&&!this.opts.allowEIO3){h("unsupported protocol version"),this.emit("connection_error",{req:t,code:g.errors.UNSUPPORTED_PROTOCOL_VERSION,message:g.errorMessages[g.errors.UNSUPPORTED_PROTOCOL_VERSION],context:{protocol:a}}),s(g.errors.UNSUPPORTED_PROTOCOL_VERSION);return}try{i=await this.generateId(t)}catch(e){h("error while generating an id"),this.emit("connection_error",{req:t,code:g.errors.BAD_REQUEST,message:g.errorMessages[g.errors.BAD_REQUEST],context:{name:"ID_GENERATION_ERROR",error:e}}),s(g.errors.BAD_REQUEST);return}h('handshaking client "%s"',i);try{var n=this.createTransport(e,t);"polling"===e?(n.maxHttpBufferSize=this.opts.maxHttpBufferSize,n.httpCompression=this.opts.httpCompression):"websocket"===e&&(n.perMessageDeflate=this.opts.perMessageDeflate)}catch(i){h('error handshaking to transport "%s"',e),this.emit("connection_error",{req:t,code:g.errors.BAD_REQUEST,message:g.errorMessages[g.errors.BAD_REQUEST],context:{name:"TRANSPORT_HANDSHAKE_ERROR",error:i}}),s(g.errors.BAD_REQUEST);return}let o=new c.Socket(i,this,n,t,a);return n.on("headers",(e,t)=>{t._query.sid||(this.opts.cookie&&(e["Set-Cookie"]=[(0,l.serialize)(this.opts.cookie.name,i,this.opts.cookie)]),this.emit("initial_headers",e,t)),this.emit("headers",e,t)}),n.onRequest(t),this.clients[i]=o,this.clientsCount++,o.once("close",()=>{delete this.clients[i],this.clientsCount--}),this.emit("connection",o),n}async onWebTransportSession(e){let t=setTimeout(()=>{h("the client failed to establish a bidirectional stream in the given period"),e.close()},this.opts.upgradeTimeout),s=e.incomingBidirectionalStreams.getReader(),i=await s.read();if(i.done){h("session is closed");return}let a=i.value,o=(0,m.createPacketDecoderStream)(this.opts.maxHttpBufferSize,"nodebuffer"),r=a.readable.pipeThrough(o).getReader(),{value:p,done:l}=await r.read();if(l){h("stream is closed");return}if(clearTimeout(t),"open"!==p.type)return h("invalid WebTransport handshake"),e.close();if(void 0===p.data){let t=new d.WebTransport(e,a,r),s=n.generateId();h('handshaking client "%s" (WebTransport)',s);let i=new c.Socket(s,this,t,null,4);this.clients[s]=i,this.clientsCount++,i.once("close",()=>{delete this.clients[s],this.clientsCount--}),this.emit("connection",i);return}let u=function(e){try{let t=JSON.parse(e);if("string"==typeof t.sid)return t.sid}catch(e){}}(p.data);if(!u)return h("invalid WebTransport handshake"),e.close();let f=this.clients[u];if(f){if(f.upgrading)h("transport has already been trying to upgrade"),e.close();else if(f.upgraded)h("transport had already been upgraded"),e.close();else{h("upgrading existing transport");let t=new d.WebTransport(e,a,r);f._maybeUpgrade(t)}}else h("upgrade attempt for closed client"),e.close()}}t.BaseServer=v,v.errors={UNKNOWN_TRANSPORT:0,UNKNOWN_SID:1,BAD_HANDSHAKE_METHOD:2,BAD_REQUEST:3,FORBIDDEN:4,UNSUPPORTED_PROTOCOL_VERSION:5},v.errorMessages={0:"Transport unknown",1:"Session ID unknown",2:"Bad handshake method",3:"Bad request",4:"Forbidden",5:"Unsupported protocol version"};class x{constructor(e,t){this.req=e,this.socket=t,e[f]={}}setHeader(e,t){this.req[f][e]=t}getHeader(e){return this.req[f][e]}removeHeader(e){delete this.req[f][e]}write(){}writeHead(){}end(){this.socket.destroy()}}class g extends v{init(){~this.opts.transports.indexOf("websocket")&&(this.ws&&this.ws.close(),this.ws=new this.opts.wsEngine({noServer:!0,clientTracking:!1,perMessageDeflate:this.opts.perMessageDeflate,maxPayload:this.opts.maxHttpBufferSize}),"function"==typeof this.ws.on&&this.ws.on("headers",(e,t)=>{let s=t[f]||{};delete t[f],t._query.sid||this.emit("initial_headers",s,t),this.emit("headers",s,t),h("writing headers: %j",s),Object.keys(s).forEach(t=>{e.push(`${t}: ${s[t]}`)})}))}cleanup(){this.ws&&(h("closing webSocketServer"),this.ws.close())}prepare(e){e._query||(e._query=~e.url.indexOf("?")?i.parse((0,a.parse)(e.url).query):{})}createTransport(e,t){return new o.default[e](t)}handleRequest(e,t){h('handling "%s" http request "%s"',e.method,e.url),this.prepare(e),e.res=t;let s=(s,i)=>{if(void 0!==s){this.emit("connection_error",{req:e,code:s,message:g.errorMessages[s],context:i}),b(t,s,i);return}e._query.sid?(h("setting new request for existing client"),this.clients[e._query.sid].transport.onRequest(e)):this.handshake(e._query.transport,e,(e,s)=>b(t,e,s))};this._applyMiddlewares(e,t,t=>{t?s(g.errors.BAD_REQUEST,{name:"MIDDLEWARE_FAILURE"}):this.verify(e,!1,s)})}handleUpgrade(e,t,s){this.prepare(e);let i=new x(e,t),a=(a,n)=>{if(void 0!==a){this.emit("connection_error",{req:e,code:a,message:g.errorMessages[a],context:n}),y(t,a,n);return}let o=Buffer.from(s);s=null,i.writeHead(),this.ws.handleUpgrade(e,t,o,s=>{this.onWebSocket(e,t,s)})};this._applyMiddlewares(e,i,t=>{t?a(g.errors.BAD_REQUEST,{name:"MIDDLEWARE_FAILURE"}):this.verify(e,!0,a)})}onWebSocket(e,t,s){if(s.on("error",a),void 0!==o.default[e._query.transport]&&!o.default[e._query.transport].prototype.handlesUpgrades){h("transport doesnt handle upgraded requests"),s.close();return}let i=e._query.sid;if(e.websocket=s,i){let t=this.clients[i];if(t){if(t.upgrading)h("transport has already been trying to upgrade"),s.close();else if(t.upgraded)h("transport had already been upgraded"),s.close();else{h("upgrading existing transport"),s.removeListener("error",a);let i=this.createTransport(e._query.transport,e);i.perMessageDeflate=this.opts.perMessageDeflate,t._maybeUpgrade(i)}}else h("upgrade attempt for closed client"),s.close()}else this.handshake(e._query.transport,e,(e,s)=>y(t,e,s));function a(){h("websocket error before upgrade")}}attach(e,t={}){let s=this._computePath(t),i=t.destroyUpgradeTimeout||1e3;function a(e){return s===e.url.slice(0,s.length)}let n=e.listeners("request").slice(0);e.removeAllListeners("request"),e.on("close",this.close.bind(this)),e.on("listening",this.init.bind(this)),e.on("request",(t,i)=>{if(a(t))h('intercepting request for path "%s"',s),this.handleRequest(t,i);else{let s=0,a=n.length;for(;s<a;s++)n[s].call(e,t,i)}}),~this.opts.transports.indexOf("websocket")&&e.on("upgrade",(e,s,n)=>{a(e)?this.handleUpgrade(e,s,n):!1!==t.destroyUpgrade&&setTimeout(function(){if(s.writable&&s.bytesWritten<=0)return s.on("error",e=>{h("error while destroying upgrade: %s",e.message)}),s.end()},i)})}}function b(e,t,s){let i=t===g.errors.FORBIDDEN?403:400,a=s&&s.message?s.message:g.errorMessages[t];e.writeHead(i,{"Content-Type":"application/json"}),e.end(JSON.stringify({code:t,message:a}))}function y(e,t,s={}){if(e.on("error",()=>{h("ignoring error from closed connection")}),e.writable){let i=s.message||g.errorMessages[t],a=Buffer.byteLength(i);e.write("HTTP/1.1 400 Bad Request\r\nConnection: close\r\nContent-type: text/html\r\nContent-Length: "+a+"\r\n\r\n"+i)}e.destroy()}t.Server=g;let _=[0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1]},42742:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Socket=void 0;let i=s(82361),a=s(34989),n=s(39512),o=(0,a.default)("engine:socket");class r extends i.EventEmitter{get readyState(){return this._readyState}set readyState(e){o("readyState updated from %s to %s",this._readyState,e),this._readyState=e}constructor(e,t,s,i,a){super(),this._readyState="opening",this.upgrading=!1,this.upgraded=!1,this.writeBuffer=[],this.packetsFn=[],this.sentCallbackFn=[],this.cleanupFn=[],this.id=e,this.server=t,this.request=i,this.protocol=a,i&&(i.websocket&&i.websocket._socket?this.remoteAddress=i.websocket._socket.remoteAddress:this.remoteAddress=i.connection.remoteAddress),this.pingTimeoutTimer=null,this.pingIntervalTimer=null,this.setTransport(s),this.onOpen()}onOpen(){this.readyState="open",this.transport.sid=this.id,this.sendPacket("open",JSON.stringify({sid:this.id,upgrades:this.getAvailableUpgrades(),pingInterval:this.server.opts.pingInterval,pingTimeout:this.server.opts.pingTimeout,maxPayload:this.server.opts.maxHttpBufferSize})),this.server.opts.initialPacket&&this.sendPacket("message",this.server.opts.initialPacket),this.emit("open"),3===this.protocol?this.resetPingTimeout():this.schedulePing()}onPacket(e){if("open"!==this.readyState)return o("packet received with closed socket");switch(o(`received packet ${e.type}`),this.emit("packet",e),e.type){case"ping":if(3!==this.transport.protocol){this.onError(Error("invalid heartbeat direction"));return}o("got ping"),this.pingTimeoutTimer.refresh(),this.sendPacket("pong"),this.emit("heartbeat");break;case"pong":if(3===this.transport.protocol){this.onError(Error("invalid heartbeat direction"));return}o("got pong"),(0,n.clearTimeout)(this.pingTimeoutTimer),this.pingIntervalTimer.refresh(),this.emit("heartbeat");break;case"error":this.onClose("parse error");break;case"message":this.emit("data",e.data),this.emit("message",e.data)}}onError(e){o("transport error"),this.onClose("transport error",e)}schedulePing(){this.pingIntervalTimer=(0,n.setTimeout)(()=>{o("writing ping packet - expecting pong within %sms",this.server.opts.pingTimeout),this.sendPacket("ping"),this.resetPingTimeout()},this.server.opts.pingInterval)}resetPingTimeout(){(0,n.clearTimeout)(this.pingTimeoutTimer),this.pingTimeoutTimer=(0,n.setTimeout)(()=>{"closed"!==this.readyState&&this.onClose("ping timeout")},3===this.protocol?this.server.opts.pingInterval+this.server.opts.pingTimeout:this.server.opts.pingTimeout)}setTransport(e){let t=this.onError.bind(this),s=()=>this.flush(),i=this.onPacket.bind(this),a=this.onDrain.bind(this),n=this.onClose.bind(this,"transport close");this.transport=e,this.transport.once("error",t),this.transport.on("ready",s),this.transport.on("packet",i),this.transport.on("drain",a),this.transport.once("close",n),this.cleanupFn.push(function(){e.removeListener("error",t),e.removeListener("ready",s),e.removeListener("packet",i),e.removeListener("drain",a),e.removeListener("close",n)})}onDrain(){if(this.sentCallbackFn.length>0){o("executing batch send callback");let e=this.sentCallbackFn.shift();if(e)for(let t=0;t<e.length;t++)e[t](this.transport)}}_maybeUpgrade(e){let t;o('might upgrade socket transport from "%s" to "%s"',this.transport.name,e.name),this.upgrading=!0;let s=(0,n.setTimeout)(()=>{o("client did not complete upgrade - closing transport"),r(),"open"===e.readyState&&e.close()},this.server.opts.upgradeTimeout),i=s=>{"ping"===s.type&&"probe"===s.data?(o("got probe ping packet, sending pong"),e.send([{type:"pong",data:"probe"}]),this.emit("upgrading",e),clearInterval(t),t=setInterval(a,100)):"upgrade"===s.type&&"closed"!==this.readyState?(o("got upgrade packet - upgrading"),r(),this.transport.discard(),this.upgraded=!0,this.clearTransport(),this.setTransport(e),this.emit("upgrade",e),this.flush(),"closing"===this.readyState&&e.close(()=>{this.onClose("forced close")})):(r(),e.close())},a=()=>{"polling"===this.transport.name&&this.transport.writable&&(o("writing a noop packet to polling for fast upgrade"),this.transport.send([{type:"noop"}]))},r=()=>{this.upgrading=!1,clearInterval(t),(0,n.clearTimeout)(s),e.removeListener("packet",i),e.removeListener("close",p),e.removeListener("error",c),this.removeListener("close",l)},c=t=>{o("client did not complete upgrade - %s",t),r(),e.close(),e=null},p=()=>{c("transport closed")},l=()=>{c("socket closed")};e.on("packet",i),e.once("close",p),e.once("error",c),this.once("close",l)}clearTransport(){let e=this.cleanupFn.length;for(let t=0;t<e;t++)this.cleanupFn.shift()();this.transport.on("error",function(){o("error triggered by discarded transport")}),this.transport.close(),(0,n.clearTimeout)(this.pingTimeoutTimer)}onClose(e,t){"closed"!==this.readyState&&(this.readyState="closed",(0,n.clearTimeout)(this.pingIntervalTimer),(0,n.clearTimeout)(this.pingTimeoutTimer),process.nextTick(()=>{this.writeBuffer=[]}),this.packetsFn=[],this.sentCallbackFn=[],this.clearTransport(),this.emit("close",e,t))}send(e,t,s){return this.sendPacket("message",e,t,s),this}write(e,t,s){return this.sendPacket("message",e,t,s),this}sendPacket(e,t,s={},i){if("function"==typeof s&&(i=s,s={}),"closing"!==this.readyState&&"closed"!==this.readyState){o('sending packet "%s" (%s)',e,t),s.compress=!1!==s.compress;let a={type:e,options:s};t&&(a.data=t),this.emit("packetCreate",a),this.writeBuffer.push(a),"function"==typeof i&&this.packetsFn.push(i),this.flush()}}flush(){if("closed"!==this.readyState&&this.transport.writable&&this.writeBuffer.length){o("flushing buffer to transport"),this.emit("flush",this.writeBuffer),this.server.emit("flush",this,this.writeBuffer);let e=this.writeBuffer;this.writeBuffer=[],this.packetsFn.length?(this.sentCallbackFn.push(this.packetsFn),this.packetsFn=[]):this.sentCallbackFn.push(null),this.transport.send(e),this.emit("drain"),this.server.emit("drain",this)}}getAvailableUpgrades(){let e=[],t=this.server.upgrades(this.transport.name);for(let s=0;s<t.length;++s){let i=t[s];-1!==this.server.opts.transports.indexOf(i)&&e.push(i)}return e}close(e){if(e&&("open"===this.readyState||"closing"===this.readyState))return this.closeTransport(e);if("open"===this.readyState){if(this.readyState="closing",this.writeBuffer.length){o("there are %d remaining packets in the buffer, waiting for the 'drain' event",this.writeBuffer.length),this.once("drain",()=>{o("all packets have been sent, closing the transport"),this.closeTransport(e)});return}o("the buffer is empty, closing the transport right away"),this.closeTransport(e)}}closeTransport(e){o("closing the transport (discard? %s)",!!e),e&&this.transport.discard(),this.transport.close(this.onClose.bind(this,"forced close"))}}t.Socket=r},22057:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Transport=void 0;let i=s(82361),a=s(53167),n=s(29416),o=(0,s(34989).default)("engine:transport");function r(){}class c extends i.EventEmitter{get readyState(){return this._readyState}set readyState(e){o("readyState updated from %s to %s (%s)",this._readyState,e,this.name),this._readyState=e}constructor(e){super(),this.writable=!1,this._readyState="open",this.discarded=!1,this.protocol="4"===e._query.EIO?4:3,this.parser=4===this.protocol?a:n,this.supportsBinary=!(e._query&&e._query.b64)}discard(){this.discarded=!0}onRequest(e){}close(e){"closed"!==this.readyState&&"closing"!==this.readyState&&(this.readyState="closing",this.doClose(e||r))}onError(e,t){if(this.listeners("error").length){let s=Error(e);s.type="TransportError",s.description=t,this.emit("error",s)}else o("ignored transport error %s (%s)",e,t)}onPacket(e){this.emit("packet",e)}onData(e){this.onPacket(this.parser.decodePacket(e))}onClose(){this.readyState="closed",this.emit("close")}}t.Transport=c},42921:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let i=s(73204),a=s(48962);t.default={polling:i.Polling,websocket:a.WebSocket}},73204:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Polling=void 0;let i=s(22057),a=s(59796),n=s(10523),o=(0,s(34989).default)("engine:polling"),r={gzip:a.createGzip,deflate:a.createDeflate};class c extends i.Transport{constructor(e){super(e),this.closeTimeout=3e4}get name(){return"polling"}onRequest(e){let t=e.res;e.res=null,"get"===e.getMethod()?this.onPollRequest(e,t):"post"===e.getMethod()?this.onDataRequest(e,t):(t.writeStatus("500 Internal Server Error"),t.end())}onPollRequest(e,t){if(this.req){o("request overlap"),this.onError("overlap from client"),t.writeStatus("500 Internal Server Error"),t.end();return}o("setting request"),this.req=e,this.res=t,e.cleanup=()=>{this.req=this.res=null},t.onAborted(()=>{this.writable=!1,this.onError("poll connection closed prematurely")}),this.writable=!0,this.emit("ready"),this.writable&&this.shouldClose&&(o("triggering empty send to append close packet"),this.send([{type:"noop"}]))}onDataRequest(e,t){let s;if(this.dataReq){this.onError("data request overlap from client"),t.writeStatus("500 Internal Server Error"),t.end();return}let i=Number(e.headers["content-length"]);if(!i){this.onError("content-length header required"),t.writeStatus("411 Length Required").end();return}if(i>this.maxHttpBufferSize){this.onError("payload too large"),t.writeStatus("413 Payload Too Large").end();return}if("application/octet-stream"===e.headers["content-type"]&&4===this.protocol)return this.onError("invalid content");this.dataReq=e,this.dataRes=t;let a=0,n={"Content-Type":"text/html"};for(let s in this.headers(e,n),n)t.writeHeader(s,String(n[s]));let o=e=>{this.onData(e.toString()),this.onDataRequestCleanup(),t.cork(()=>{t.end("ok")})};t.onAborted(()=>{this.onDataRequestCleanup(),this.onError("data request connection closed prematurely")}),t.onData((e,n)=>{let r=a+e.byteLength;if(r>i){this.onError("content-length mismatch"),t.close();return}if(!s){if(n){o(Buffer.from(e));return}s=Buffer.allocUnsafe(i)}if(Buffer.from(e).copy(s,a),n){if(r!=i){this.onError("content-length mismatch"),t.writeStatus("400 Content-Length Mismatch").end(),this.onDataRequestCleanup();return}o(s);return}a=r})}onDataRequestCleanup(){this.dataReq=this.dataRes=null}onData(e){o('received "%s"',e);let t=e=>{if("close"===e.type)return o("got xhr close packet"),this.onClose(),!1;this.onPacket(e)};3===this.protocol?this.parser.decodePayload(e,t):this.parser.decodePayload(e).forEach(t)}onClose(){this.writable&&this.send([{type:"noop"}]),super.onClose()}send(e){this.writable=!1,this.shouldClose&&(o("appending close packet to payload"),e.push({type:"close"}),this.shouldClose(),this.shouldClose=null);let t=t=>{let s=e.some(e=>e.options&&e.options.compress);this.write(t,{compress:s})};3===this.protocol?this.parser.encodePayload(e,this.supportsBinary,t):this.parser.encodePayload(e,t)}write(e,t){o('writing "%s"',e),this.doWrite(e,t,()=>{this.req.cleanup(),this.emit("drain")})}doWrite(e,t,s){let i="string"==typeof e,a={"Content-Type":i?"text/plain; charset=UTF-8":"application/octet-stream"},o=e=>{this.headers(this.req,a),this.res.cork(()=>{Object.keys(a).forEach(e=>{this.res.writeHeader(e,String(a[e]))}),this.res.end(e)}),s()};if(!this.httpCompression||!t.compress||(i?Buffer.byteLength(e):e.length)<this.httpCompression.threshold){o(e);return}let r=n(this.req).encodings(["gzip","deflate"]);if(!r){o(e);return}this.compress(e,r,(e,t)=>{if(e){this.res.writeStatus("500 Internal Server Error"),this.res.end(),s(e);return}a["Content-Encoding"]=r,o(t)})}compress(e,t,s){o("compressing");let i=[],a=0;r[t](this.httpCompression).on("error",s).on("data",function(e){i.push(e),a+=e.length}).on("end",function(){s(null,Buffer.concat(i,a))}).end(e)}doClose(e){let t;o("closing");let s=()=>{clearTimeout(t),e(),this.onClose()};this.writable?(o("transport writable - closing right away"),this.send([{type:"close"}]),s()):this.discarded?(o("transport discarded - closing right away"),s()):(o("transport not writable - buffering orderly close"),this.shouldClose=s,t=setTimeout(s,this.closeTimeout))}headers(e,t){t=t||{};let s=e.headers["user-agent"];return s&&(~s.indexOf(";MSIE")||~s.indexOf("Trident/"))&&(t["X-XSS-Protection"]="0"),t["cache-control"]="no-store",this.emit("headers",t,e),t}}t.Polling=c},48962:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.WebSocket=void 0;let i=s(22057),a=(0,s(34989).default)("engine:ws");class n extends i.Transport{constructor(e){super(e),this.writable=!1,this.perMessageDeflate=null}get name(){return"websocket"}get handlesUpgrades(){return!0}send(e){this.writable=!1;for(let t=0;t<e.length;t++){let s=e[t],i=t+1===e.length,n=e=>{let t="string"!=typeof e,s=this.perMessageDeflate&&Buffer.byteLength(e)>this.perMessageDeflate.threshold;a('writing "%s"',e),this.socket.send(e,t,s),i&&(this.emit("drain"),this.writable=!0,this.emit("ready"))};s.options&&"string"==typeof s.options.wsPreEncoded?n(s.options.wsPreEncoded):this.parser.encodePacket(s,this.supportsBinary,n)}}doClose(e){a("closing"),e&&e(),this.socket.end()}}t.WebSocket=n},1246:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let i=s(97417),a=s(47206),n=s(93344),o=s(7979);function r(e){return"string"==typeof e._query.j?new a.JSONP(e):new i.Polling(e)}t.default={polling:r,websocket:n.WebSocket,webtransport:o.WebTransport},r.upgradesTo=["websocket","webtransport"]},47206:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.JSONP=void 0;let i=s(97417),a=s(63477),n=/\\\\n/g,o=/(\\)?\\n/g;class r extends i.Polling{constructor(e){super(e),this.head="___eio["+(e._query.j||"").replace(/[^0-9]/g,"")+"](",this.foot=");"}onData(e){"string"==typeof(e=a.parse(e).d)&&(e=e.replace(o,function(e,t){return t?e:"\n"}),super.onData(e.replace(n,"\\n")))}doWrite(e,t,s){let i=JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029");e=this.head+i+this.foot,super.doWrite(e,t,s)}}t.JSONP=r},97417:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Polling=void 0;let i=s(22057),a=s(59796),n=s(10523),o=(0,s(34989).default)("engine:polling"),r={gzip:a.createGzip,deflate:a.createDeflate};class c extends i.Transport{constructor(e){super(e),this.closeTimeout=3e4}get name(){return"polling"}onRequest(e){let t=e.res;e.res=null,"GET"===e.method?this.onPollRequest(e,t):"POST"===e.method?this.onDataRequest(e,t):(t.writeHead(500),t.end())}onPollRequest(e,t){if(this.req){o("request overlap"),this.onError("overlap from client"),t.writeHead(400),t.end();return}o("setting request"),this.req=e,this.res=t;let s=()=>{this.onError("poll connection closed prematurely")};e.cleanup=()=>{e.removeListener("close",s),this.req=this.res=null},e.on("close",s),this.writable=!0,this.emit("ready"),this.writable&&this.shouldClose&&(o("triggering empty send to append close packet"),this.send([{type:"noop"}]))}onDataRequest(e,t){if(this.dataReq){this.onError("data request overlap from client"),t.writeHead(400),t.end();return}let s="application/octet-stream"===e.headers["content-type"];if(s&&4===this.protocol)return this.onError("invalid content");this.dataReq=e,this.dataRes=t;let i=s?Buffer.concat([]):"",a=()=>{e.removeListener("data",o),e.removeListener("end",r),e.removeListener("close",n),this.dataReq=this.dataRes=i=null},n=()=>{a(),this.onError("data request connection closed prematurely")},o=e=>{let n;s?n=(i=Buffer.concat([i,e])).length:(i+=e,n=Buffer.byteLength(i)),n>this.maxHttpBufferSize&&(t.writeHead(413).end(),a())},r=()=>{this.onData(i),t.writeHead(200,this.headers(e,{"Content-Type":"text/html","Content-Length":"2"})),t.end("ok"),a()};e.on("close",n),s||e.setEncoding("utf8"),e.on("data",o),e.on("end",r)}onData(e){o('received "%s"',e);let t=e=>{if("close"===e.type)return o("got xhr close packet"),this.onClose(),!1;this.onPacket(e)};3===this.protocol?this.parser.decodePayload(e,t):this.parser.decodePayload(e).forEach(t)}onClose(){this.writable&&this.send([{type:"noop"}]),super.onClose()}send(e){this.writable=!1,this.shouldClose&&(o("appending close packet to payload"),e.push({type:"close"}),this.shouldClose(),this.shouldClose=null);let t=t=>{let s=e.some(e=>e.options&&e.options.compress);this.write(t,{compress:s})};3===this.protocol?this.parser.encodePayload(e,this.supportsBinary,t):this.parser.encodePayload(e,t)}write(e,t){o('writing "%s"',e),this.doWrite(e,t,()=>{this.req.cleanup(),this.emit("drain")})}doWrite(e,t,s){let i="string"==typeof e,a={"Content-Type":i?"text/plain; charset=UTF-8":"application/octet-stream"},o=e=>{a["Content-Length"]="string"==typeof e?Buffer.byteLength(e):e.length,this.res.writeHead(200,this.headers(this.req,a)),this.res.end(e),s()};if(!this.httpCompression||!t.compress||(i?Buffer.byteLength(e):e.length)<this.httpCompression.threshold){o(e);return}let r=n(this.req).encodings(["gzip","deflate"]);if(!r){o(e);return}this.compress(e,r,(e,t)=>{if(e){this.res.writeHead(500),this.res.end(),s(e);return}a["Content-Encoding"]=r,o(t)})}compress(e,t,s){o("compressing");let i=[],a=0;r[t](this.httpCompression).on("error",s).on("data",function(e){i.push(e),a+=e.length}).on("end",function(){s(null,Buffer.concat(i,a))}).end(e)}doClose(e){let t;o("closing"),this.dataReq&&(o("aborting ongoing data request"),this.dataReq.destroy());let s=()=>{clearTimeout(t),e(),this.onClose()};this.writable?(o("transport writable - closing right away"),this.send([{type:"close"}]),s()):this.discarded?(o("transport discarded - closing right away"),s()):(o("transport not writable - buffering orderly close"),this.shouldClose=s,t=setTimeout(s,this.closeTimeout))}headers(e,t={}){let s=e.headers["user-agent"];return s&&(~s.indexOf(";MSIE")||~s.indexOf("Trident/"))&&(t["X-XSS-Protection"]="0"),t["cache-control"]="no-store",this.emit("headers",t,e),t}}t.Polling=c},93344:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.WebSocket=void 0;let i=s(22057),a=(0,s(34989).default)("engine:ws");class n extends i.Transport{constructor(e){super(e),this._doSend=e=>{this.socket.send(e,this._onSent)},this._doSendLast=e=>{this.socket.send(e,this._onSentLast)},this._onSent=e=>{e&&this.onError("write error",e.stack)},this._onSentLast=e=>{e?this.onError("write error",e.stack):(this.emit("drain"),this.writable=!0,this.emit("ready"))},this.socket=e.websocket,this.socket.on("message",(e,t)=>{let s=t?e:e.toString();a('received "%s"',s),super.onData(s)}),this.socket.once("close",this.onClose.bind(this)),this.socket.on("error",this.onError.bind(this)),this.writable=!0,this.perMessageDeflate=null}get name(){return"websocket"}get handlesUpgrades(){return!0}send(e){this.writable=!1;for(let t=0;t<e.length;t++){let s=e[t],i=t+1===e.length;this._canSendPreEncodedFrame(s)?this.socket._sender.sendFrame(s.options.wsPreEncodedFrame,i?this._onSentLast:this._onSent):this.parser.encodePacket(s,this.supportsBinary,i?this._doSendLast:this._doSend)}}_canSendPreEncodedFrame(e){var t,s,i;return!this.perMessageDeflate&&"function"==typeof(null===(s=null===(t=this.socket)||void 0===t?void 0:t._sender)||void 0===s?void 0:s.sendFrame)&&(null===(i=e.options)||void 0===i?void 0:i.wsPreEncodedFrame)!==void 0}doClose(e){a("closing"),this.socket.close(),e&&e()}}t.WebSocket=n},7979:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.WebTransport=void 0;let i=s(22057),a=s(34989),n=s(53167),o=(0,a.default)("engine:webtransport");class r extends i.Transport{constructor(e,t,s){super({_query:{EIO:"4"}}),this.session=e;let i=(0,n.createPacketEncoderStream)();i.readable.pipeTo(t.writable).catch(()=>{o("the stream was closed")}),this.writer=i.writable.getWriter(),(async()=>{try{for(;;){let{value:e,done:t}=await s.read();if(t){o("session is closed");break}o("received chunk: %o",e),this.onPacket(e)}}catch(e){o("error while reading: %s",e.message)}})(),e.closed.then(()=>this.onClose()),this.writable=!0}get name(){return"webtransport"}async send(e){this.writable=!1;try{for(let t=0;t<e.length;t++){let s=e[t];await this.writer.write(s)}}catch(e){o("error while writing: %s",e.message)}this.emit("drain"),this.writable=!0,this.emit("ready")}doClose(e){o("closing WebTransport session"),this.session.close(),e&&e()}}t.WebTransport=r},64798:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.uServer=void 0;let i=s(34989),a=s(83915),n=s(42921),o=(0,i.default)("engine:uws");class r extends a.BaseServer{init(){}cleanup(){}prepare(e,t){e.method=e.getMethod().toUpperCase(),e.url=e.getUrl();let s=new URLSearchParams(e.getQuery());e._query=Object.fromEntries(s.entries()),e.headers={},e.forEach((t,s)=>{e.headers[t]=s}),e.connection={remoteAddress:Buffer.from(t.getRemoteAddressAsText()).toString()},t.onAborted(()=>{o("response has been aborted")})}createTransport(e,t){return new n.default[e](t)}attach(e,t={}){let s=this._computePath(t);e.any(s,this.handleRequest.bind(this)).ws(s,{compression:t.compression,idleTimeout:t.idleTimeout,maxBackpressure:t.maxBackpressure,maxPayloadLength:this.opts.maxHttpBufferSize,upgrade:this.handleUpgrade.bind(this),open:e=>{let t=e.getUserData().transport;t.socket=e,t.writable=!0,t.emit("ready")},message:(e,t,s)=>{e.getUserData().transport.onData(s?t:Buffer.from(t).toString())},close:(e,t,s)=>{e.getUserData().transport.onClose(t,s)}})}_applyMiddlewares(e,t,s){if(0===this.middlewares.length)return s();e.res=new c(t),super._applyMiddlewares(e,e.res,t=>{e.res.writeHead(),s(t)})}handleRequest(e,t){o('handling "%s" http request "%s"',t.getMethod(),t.getUrl()),this.prepare(t,e),t.res=e;let s=(s,i)=>{if(void 0!==s){this.emit("connection_error",{req:t,code:s,message:a.Server.errorMessages[s],context:i}),this.abortRequest(t.res,s,i);return}t._query.sid?(o("setting new request for existing client"),this.clients[t._query.sid].transport.onRequest(t)):this.handshake(t._query.transport,t,(t,s)=>this.abortRequest(e,t,s))};this._applyMiddlewares(t,e,e=>{e?s(a.Server.errors.BAD_REQUEST,{name:"MIDDLEWARE_FAILURE"}):this.verify(t,!1,s)})}handleUpgrade(e,t,s){o("on upgrade"),this.prepare(t,e),t.res=e;let i=async(i,n)=>{let r;if(void 0!==i){this.emit("connection_error",{req:t,code:i,message:a.Server.errorMessages[i],context:n}),this.abortRequest(e,i,n);return}let c=t._query.sid;if(c){let s=this.clients[c];if(!s)return o("upgrade attempt for closed client"),e.close();if(s.upgrading)return o("transport has already been trying to upgrade"),e.close();if(s.upgraded)return o("transport had already been upgraded"),e.close();o("upgrading existing transport"),r=this.createTransport(t._query.transport,t),s._maybeUpgrade(r)}else if(!(r=await this.handshake(t._query.transport,t,(t,s)=>this.abortRequest(e,t,s))))return;t.res.writeStatus("101 Switching Protocols"),e.upgrade({transport:r},t.getHeader("sec-websocket-key"),t.getHeader("sec-websocket-protocol"),t.getHeader("sec-websocket-extensions"),s)};this._applyMiddlewares(t,e,e=>{e?i(a.Server.errors.BAD_REQUEST,{name:"MIDDLEWARE_FAILURE"}):this.verify(t,!0,i)})}abortRequest(e,t,s){let i=t===a.Server.errors.FORBIDDEN?"403 Forbidden":"400 Bad Request",n=s&&s.message?s.message:a.Server.errorMessages[t];e.writeStatus(i),e.writeHeader("Content-Type","application/json"),e.end(JSON.stringify({code:t,message:n}))}}t.uServer=r;class c{constructor(e){this.res=e,this.statusWritten=!1,this.headers=[],this.isAborted=!1}set statusCode(e){e&&this.writeStatus(200===e?"200 OK":"204 No Content")}writeHead(e){this.statusCode=e}setHeader(e,t){Array.isArray(t)?t.forEach(t=>{this.writeHeader(e,t)}):this.writeHeader(e,t)}removeHeader(){}getHeader(){}writeStatus(e){if(!this.isAborted)return this.res.writeStatus(e),this.statusWritten=!0,this.writeBufferedHeaders(),this}writeHeader(e,t){this.isAborted||"Content-Length"===e||(this.statusWritten?this.res.writeHeader(e,t):this.headers.push([e,t]))}writeBufferedHeaders(){this.headers.forEach(([e,t])=>{this.res.writeHeader(e,t)})}end(e){this.isAborted||this.res.cork(()=>{this.statusWritten||this.writeBufferedHeaders(),this.res.end(e)})}onData(e){this.isAborted||this.res.onData(e)}onAborted(e){this.isAborted||this.res.onAborted(()=>{this.isAborted=!0,e()})}cork(e){this.isAborted||this.res.cork(e)}}},89227:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.reconstructPacket=t.deconstructPacket=void 0;let i=s(3637);t.deconstructPacket=function(e){let t=[],s=e.data;return e.data=function e(t,s){if(!t)return t;if((0,i.isBinary)(t)){let e={_placeholder:!0,num:s.length};return s.push(t),e}if(Array.isArray(t)){let i=Array(t.length);for(let a=0;a<t.length;a++)i[a]=e(t[a],s);return i}if("object"==typeof t&&!(t instanceof Date)){let i={};for(let a in t)Object.prototype.hasOwnProperty.call(t,a)&&(i[a]=e(t[a],s));return i}return t}(s,t),e.attachments=t.length,{packet:e,buffers:t}},t.reconstructPacket=function(e,t){return e.data=function e(t,s){if(!t)return t;if(t&&!0===t._placeholder){if("number"==typeof t.num&&t.num>=0&&t.num<s.length)return s[t.num];throw Error("illegal attachments")}if(Array.isArray(t))for(let i=0;i<t.length;i++)t[i]=e(t[i],s);else if("object"==typeof t)for(let i in t)Object.prototype.hasOwnProperty.call(t,i)&&(t[i]=e(t[i],s));return t}(e.data,t),delete e.attachments,e}},18268:(e,t,s)=>{"use strict";var i;Object.defineProperty(t,"__esModule",{value:!0}),t.Decoder=t.Encoder=t.PacketType=t.protocol=void 0;let a=s(51848),n=s(89227),o=s(3637),r=(0,s(5465).default)("socket.io-parser"),c=["connect","connect_error","disconnect","disconnecting","newListener","removeListener"];t.protocol=5,function(e){e[e.CONNECT=0]="CONNECT",e[e.DISCONNECT=1]="DISCONNECT",e[e.EVENT=2]="EVENT",e[e.ACK=3]="ACK",e[e.CONNECT_ERROR=4]="CONNECT_ERROR",e[e.BINARY_EVENT=5]="BINARY_EVENT",e[e.BINARY_ACK=6]="BINARY_ACK"}(i=t.PacketType||(t.PacketType={}));class p{constructor(e){this.replacer=e}encode(e){return(r("encoding packet %j",e),(e.type===i.EVENT||e.type===i.ACK)&&(0,o.hasBinary)(e))?this.encodeAsBinary({type:e.type===i.EVENT?i.BINARY_EVENT:i.BINARY_ACK,nsp:e.nsp,data:e.data,id:e.id}):[this.encodeAsString(e)]}encodeAsString(e){let t=""+e.type;return(e.type===i.BINARY_EVENT||e.type===i.BINARY_ACK)&&(t+=e.attachments+"-"),e.nsp&&"/"!==e.nsp&&(t+=e.nsp+","),null!=e.id&&(t+=e.id),null!=e.data&&(t+=JSON.stringify(e.data,this.replacer)),r("encoded %j as %s",e,t),t}encodeAsBinary(e){let t=(0,n.deconstructPacket)(e),s=this.encodeAsString(t.packet),i=t.buffers;return i.unshift(s),i}}function l(e){return"[object Object]"===Object.prototype.toString.call(e)}t.Encoder=p;class u extends a.Emitter{constructor(e){super(),this.reviver=e}add(e){let t;if("string"==typeof e){if(this.reconstructor)throw Error("got plaintext data when reconstructing a packet");let s=(t=this.decodeString(e)).type===i.BINARY_EVENT;s||t.type===i.BINARY_ACK?(t.type=s?i.EVENT:i.ACK,this.reconstructor=new d(t),0===t.attachments&&super.emitReserved("decoded",t)):super.emitReserved("decoded",t)}else if((0,o.isBinary)(e)||e.base64){if(this.reconstructor)(t=this.reconstructor.takeBinaryData(e))&&(this.reconstructor=null,super.emitReserved("decoded",t));else throw Error("got binary data when not reconstructing a packet")}else throw Error("Unknown type: "+e)}decodeString(e){let t=0,s={type:Number(e.charAt(0))};if(void 0===i[s.type])throw Error("unknown packet type "+s.type);if(s.type===i.BINARY_EVENT||s.type===i.BINARY_ACK){let i=t+1;for(;"-"!==e.charAt(++t)&&t!=e.length;);let a=e.substring(i,t);if(a!=Number(a)||"-"!==e.charAt(t))throw Error("Illegal attachments");s.attachments=Number(a)}if("/"===e.charAt(t+1)){let i=t+1;for(;++t&&","!==e.charAt(t)&&t!==e.length;);s.nsp=e.substring(i,t)}else s.nsp="/";let a=e.charAt(t+1);if(""!==a&&Number(a)==a){let i=t+1;for(;++t;){let s=e.charAt(t);if(null==s||Number(s)!=s){--t;break}if(t===e.length)break}s.id=Number(e.substring(i,t+1))}if(e.charAt(++t)){let i=this.tryParse(e.substr(t));if(u.isPayloadValid(s.type,i))s.data=i;else throw Error("invalid payload")}return r("decoded %s as %j",e,s),s}tryParse(e){try{return JSON.parse(e,this.reviver)}catch(e){return!1}}static isPayloadValid(e,t){switch(e){case i.CONNECT:return l(t);case i.DISCONNECT:return void 0===t;case i.CONNECT_ERROR:return"string"==typeof t||l(t);case i.EVENT:case i.BINARY_EVENT:return Array.isArray(t)&&("number"==typeof t[0]||"string"==typeof t[0]&&-1===c.indexOf(t[0]));case i.ACK:case i.BINARY_ACK:return Array.isArray(t)}}destroy(){this.reconstructor&&(this.reconstructor.finishedReconstruction(),this.reconstructor=null)}}t.Decoder=u;class d{constructor(e){this.packet=e,this.buffers=[],this.reconPack=e}takeBinaryData(e){if(this.buffers.push(e),this.buffers.length===this.reconPack.attachments){let e=(0,n.reconstructPacket)(this.reconPack,this.buffers);return this.finishedReconstruction(),e}return null}finishedReconstruction(){this.reconPack=null,this.buffers=[]}}},3637:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.hasBinary=t.isBinary=void 0;let s="function"==typeof ArrayBuffer,i=e=>"function"==typeof ArrayBuffer.isView?ArrayBuffer.isView(e):e.buffer instanceof ArrayBuffer,a=Object.prototype.toString,n="function"==typeof Blob||"undefined"!=typeof Blob&&"[object BlobConstructor]"===a.call(Blob),o="function"==typeof File||"undefined"!=typeof File&&"[object FileConstructor]"===a.call(File);function r(e){return s&&(e instanceof ArrayBuffer||i(e))||n&&e instanceof Blob||o&&e instanceof File}t.isBinary=r,t.hasBinary=function e(t,s){if(!t||"object"!=typeof t)return!1;if(Array.isArray(t)){for(let s=0,i=t.length;s<i;s++)if(e(t[s]))return!0;return!1}if(r(t))return!0;if(t.toJSON&&"function"==typeof t.toJSON&&1==arguments.length)return e(t.toJSON(),!0);for(let s in t)if(Object.prototype.hasOwnProperty.call(t,s)&&e(t[s]))return!0;return!1}},24046:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.RemoteSocket=t.BroadcastOperator=void 0;let i=s(96802),a=s(18268);class n{constructor(e,t=new Set,s=new Set,i={}){this.adapter=e,this.rooms=t,this.exceptRooms=s,this.flags=i}to(e){let t=new Set(this.rooms);return Array.isArray(e)?e.forEach(e=>t.add(e)):t.add(e),new n(this.adapter,t,this.exceptRooms,this.flags)}in(e){return this.to(e)}except(e){let t=new Set(this.exceptRooms);return Array.isArray(e)?e.forEach(e=>t.add(e)):t.add(e),new n(this.adapter,this.rooms,t,this.flags)}compress(e){let t=Object.assign({},this.flags,{compress:e});return new n(this.adapter,this.rooms,this.exceptRooms,t)}get volatile(){let e=Object.assign({},this.flags,{volatile:!0});return new n(this.adapter,this.rooms,this.exceptRooms,e)}get local(){let e=Object.assign({},this.flags,{local:!0});return new n(this.adapter,this.rooms,this.exceptRooms,e)}timeout(e){let t=Object.assign({},this.flags,{timeout:e});return new n(this.adapter,this.rooms,this.exceptRooms,t)}emit(e,...t){if(i.RESERVED_EVENTS.has(e))throw Error(`"${String(e)}" is a reserved event name`);let s=[e,...t],n={type:a.PacketType.EVENT,data:s};if("function"!=typeof s[s.length-1])return this.adapter.broadcast(n,{rooms:this.rooms,except:this.exceptRooms,flags:this.flags}),!0;let o=s.pop(),r=!1,c=[],p=setTimeout(()=>{r=!0,o.apply(this,[Error("operation has timed out"),this.flags.expectSingleResponse?null:c])},this.flags.timeout),l=-1,u=0,d=0,m=()=>{r||l!==u||c.length!==d||(clearTimeout(p),o.apply(this,[null,this.flags.expectSingleResponse?c[0]:c]))};return this.adapter.broadcastWithAck(n,{rooms:this.rooms,except:this.exceptRooms,flags:this.flags},e=>{d+=e,u++,m()},e=>{c.push(e),m()}),this.adapter.serverCount().then(e=>{l=e,m()}),!0}emitWithAck(e,...t){return new Promise((s,i)=>{t.push((e,t)=>e?(e.responses=t,i(e)):s(t)),this.emit(e,...t)})}allSockets(){if(!this.adapter)throw Error("No adapter for this namespace, are you trying to get the list of clients of a dynamic namespace?");return this.adapter.sockets(this.rooms)}fetchSockets(){return this.adapter.fetchSockets({rooms:this.rooms,except:this.exceptRooms,flags:this.flags}).then(e=>e.map(e=>e.server?e:new o(this.adapter,e)))}socketsJoin(e){this.adapter.addSockets({rooms:this.rooms,except:this.exceptRooms,flags:this.flags},Array.isArray(e)?e:[e])}socketsLeave(e){this.adapter.delSockets({rooms:this.rooms,except:this.exceptRooms,flags:this.flags},Array.isArray(e)?e:[e])}disconnectSockets(e=!1){this.adapter.disconnectSockets({rooms:this.rooms,except:this.exceptRooms,flags:this.flags},e)}}t.BroadcastOperator=n;class o{constructor(e,t){this.id=t.id,this.handshake=t.handshake,this.rooms=new Set(t.rooms),this.data=t.data,this.operator=new n(e,new Set([this.id]),new Set,{expectSingleResponse:!0})}timeout(e){return this.operator.timeout(e)}emit(e,...t){return this.operator.emit(e,...t)}join(e){return this.operator.socketsJoin(e)}leave(e){return this.operator.socketsLeave(e)}disconnect(e=!1){return this.operator.disconnectSockets(e),this}}t.RemoteSocket=o},58557:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Client=void 0;let i=s(18268),a=s(49097),n=s(57310),o=a("socket.io:client");class r{constructor(e,t){this.sockets=new Map,this.nsps=new Map,this.server=e,this.conn=t,this.encoder=e.encoder,this.decoder=new e._parser.Decoder,this.id=t.id,this.setup()}get request(){return this.conn.request}setup(){this.onclose=this.onclose.bind(this),this.ondata=this.ondata.bind(this),this.onerror=this.onerror.bind(this),this.ondecoded=this.ondecoded.bind(this),this.decoder.on("decoded",this.ondecoded),this.conn.on("data",this.ondata),this.conn.on("error",this.onerror),this.conn.on("close",this.onclose),this.connectTimeout=setTimeout(()=>{0===this.nsps.size?(o("no namespace joined yet, close the client"),this.close()):o("the client has already joined a namespace, nothing to do")},this.server._connectTimeout)}connect(e,t={}){if(this.server._nsps.has(e))return o("connecting to namespace %s",e),this.doConnect(e,t);this.server._checkNamespace(e,t,s=>{s?this.doConnect(e,t):(o("creation of namespace %s was denied",e),this._packet({type:i.PacketType.CONNECT_ERROR,nsp:e,data:{message:"Invalid namespace"}}))})}doConnect(e,t){let s=this.server.of(e);s._add(this,t,e=>{this.sockets.set(e.id,e),this.nsps.set(s.name,e),this.connectTimeout&&(clearTimeout(this.connectTimeout),this.connectTimeout=void 0)})}_disconnect(){for(let e of this.sockets.values())e.disconnect();this.sockets.clear(),this.close()}_remove(e){if(this.sockets.has(e.id)){let t=this.sockets.get(e.id).nsp.name;this.sockets.delete(e.id),this.nsps.delete(t)}else o("ignoring remove for %s",e.id)}close(){"open"===this.conn.readyState&&(o("forcing transport close"),this.conn.close(),this.onclose("forced server close"))}_packet(e,t={}){if("open"!==this.conn.readyState){o("ignoring packet write %j",e);return}let s=t.preEncoded?e:this.encoder.encode(e);this.writeToEngine(s,t)}writeToEngine(e,t){if(t.volatile&&!this.conn.transport.writable){o("volatile packet is discarded since the transport is not currently writable");return}for(let s of Array.isArray(e)?e:[e])this.conn.write(s,t)}ondata(e){try{this.decoder.add(e)}catch(e){o("invalid packet format"),this.onerror(e)}}ondecoded(e){let t,s;if(3===this.conn.protocol){let i=n.parse(e.nsp,!0);t=i.pathname,s=i.query}else t=e.nsp,s=e.data;let a=this.nsps.get(t);a||e.type!==i.PacketType.CONNECT?a&&e.type!==i.PacketType.CONNECT&&e.type!==i.PacketType.CONNECT_ERROR?process.nextTick(function(){a._onpacket(e)}):(o("invalid state (packet type: %s)",e.type),this.close()):this.connect(t,s)}onerror(e){for(let t of this.sockets.values())t._onerror(e);this.conn.close()}onclose(e,t){for(let s of(o("client close with reason %s",e),this.destroy(),this.sockets.values()))s._onclose(e,t);this.sockets.clear(),this.decoder.destroy()}destroy(){this.conn.removeListener("data",this.ondata),this.conn.removeListener("error",this.onerror),this.conn.removeListener("close",this.onclose),this.decoder.removeListener("decoded",this.ondecoded),this.connectTimeout&&(clearTimeout(this.connectTimeout),this.connectTimeout=void 0)}}t.Client=r},26371:function(e,t,s){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,s,i){void 0===i&&(i=s);var a=Object.getOwnPropertyDescriptor(t,s);(!a||("get"in a?!t.__esModule:a.writable||a.configurable))&&(a={enumerable:!0,get:function(){return t[s]}}),Object.defineProperty(e,i,a)}:function(e,t,s,i){void 0===i&&(i=s),e[i]=t[s]}),a=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),n=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var s in e)"default"!==s&&Object.prototype.hasOwnProperty.call(e,s)&&i(t,e,s);return a(t,e),t},o=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.Namespace=t.Socket=t.Server=void 0;let r=s(13685),c=s(57147),p=s(59796),l=s(10523),u=s(12781),d=s(71017),m=s(66028),h=s(58557),f=s(82361),v=s(582);Object.defineProperty(t,"Namespace",{enumerable:!0,get:function(){return v.Namespace}});let x=s(72907),g=s(21605),b=n(s(18268)),y=o(s(49097)),_=s(49736);Object.defineProperty(t,"Socket",{enumerable:!0,get:function(){return _.Socket}});let w=s(77160),k=s(37393),S=o(s(79879)),C=(0,y.default)("socket.io:server"),E=s(49863).i8,O=/\.map/;class T extends w.StrictEventEmitter{constructor(e,t={}){super(),this._nsps=new Map,this.parentNsps=new Map,this.parentNamespacesFromRegExp=new Map,"object"==typeof e&&e instanceof Object&&!e.listen&&(t=e,e=void 0),this.path(t.path||"/socket.io"),this.connectTimeout(t.connectTimeout||45e3),this.serveClient(!1!==t.serveClient),this._parser=t.parser||b,this.encoder=new this._parser.Encoder,this.opts=t,t.connectionStateRecovery?(t.connectionStateRecovery=Object.assign({maxDisconnectionDuration:12e4,skipMiddlewares:!0},t.connectionStateRecovery),this.adapter(t.adapter||g.SessionAwareAdapter)):this.adapter(t.adapter||g.Adapter),t.cleanupEmptyChildNamespaces=!!t.cleanupEmptyChildNamespaces,this.sockets=this.of("/"),(e||"number"==typeof e)&&this.attach(e),this.opts.cors&&(this._corsMiddleware=(0,S.default)(this.opts.cors))}get _opts(){return this.opts}serveClient(e){return arguments.length?(this._serveClient=e,this):this._serveClient}_checkNamespace(e,t,s){if(0===this.parentNsps.size)return s(!1);let i=this.parentNsps.keys(),a=()=>{let n=i.next();if(n.done)return s(!1);n.value(e,t,(t,i)=>{if(t||!i)return a();if(this._nsps.has(e))return C("dynamic namespace %s already exists",e),s(this._nsps.get(e));let o=this.parentNsps.get(n.value).createChild(e);C("dynamic namespace %s was created",e),s(o)})};a()}path(e){if(!arguments.length)return this._path;this._path=e.replace(/\/$/,"");let t=this._path.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&");return this.clientPathRegex=RegExp("^"+t+"/socket\\.io(\\.msgpack|\\.esm)?(\\.min)?\\.js(\\.map)?(?:\\?|$)"),this}connectTimeout(e){return void 0===e?this._connectTimeout:(this._connectTimeout=e,this)}adapter(e){if(!arguments.length)return this._adapter;for(let t of(this._adapter=e,this._nsps.values()))t._initAdapter();return this}listen(e,t={}){return this.attach(e,t)}attach(e,t={}){if("function"==typeof e)throw Error("You are trying to attach socket.io to an express request handler function. Please pass a http.Server instance.");if(Number(e)==e&&(e=Number(e)),"number"==typeof e){C("creating http server and binding to %d",e);let t=e;(e=r.createServer((e,t)=>{t.writeHead(404),t.end()})).listen(t)}return Object.assign(t,this.opts),t.path=t.path||this._path,this.initEngine(e,t),this}attachApp(e,t={}){Object.assign(t,this.opts),t.path=t.path||this._path,C("creating uWebSockets.js-based engine with opts %j",t);let s=new m.uServer(t);s.attach(e,t),this.bind(s),this._serveClient&&e.get(`${this._path}/*`,(e,t)=>{if(!this.clientPathRegex.test(t.getUrl())){t.setYield(!0);return}let s=t.getUrl().replace(this._path,"").replace(/\?.*$/,"").replace(/^\//,""),i=O.test(s),a=i?"map":"source",n='"'+E+'"',o="W/"+n,r=t.getHeader("if-none-match");if(r&&(n===r||o===r)){C("serve client %s 304",a),e.writeStatus("304 Not Modified"),e.end();return}C("serve client %s",a),e.writeHeader("cache-control","public, max-age=0"),e.writeHeader("content-type","application/"+(i?"json":"javascript")+"; charset=utf-8"),e.writeHeader("etag",n);let c=d.join(__dirname,"../client-dist/",s);(0,k.serveFile)(e,c)}),(0,k.patchAdapter)(e)}initEngine(e,t){C("creating engine.io instance with opts %j",t),this.eio=(0,m.attach)(e,t),this._serveClient&&this.attachServe(e),this.httpServer=e,this.bind(this.eio)}attachServe(e){C("attaching client serving req handler");let t=e.listeners("request").slice(0);e.removeAllListeners("request"),e.on("request",(s,i)=>{if(this.clientPathRegex.test(s.url))this._corsMiddleware?this._corsMiddleware(s,i,()=>{this.serve(s,i)}):this.serve(s,i);else for(let a=0;a<t.length;a++)t[a].call(e,s,i)})}serve(e,t){let s=e.url.replace(this._path,"").replace(/\?.*$/,""),i=O.test(s),a=i?"map":"source",n='"'+E+'"',o="W/"+n,r=e.headers["if-none-match"];if(r&&(n===r||o===r)){C("serve client %s 304",a),t.writeHead(304),t.end();return}C("serve client %s",a),t.setHeader("Cache-Control","public, max-age=0"),t.setHeader("Content-Type","application/"+(i?"json":"javascript")+"; charset=utf-8"),t.setHeader("ETag",n),T.sendFile(s,e,t)}static sendFile(e,t,s){let i=(0,c.createReadStream)(d.join(__dirname,"../client-dist/",e)),a=l(t).encodings(["br","gzip","deflate"]),n=e=>{e&&s.end()};switch(a){case"br":s.writeHead(200,{"content-encoding":"br"}),(0,u.pipeline)(i,(0,p.createBrotliCompress)(),s,n);break;case"gzip":s.writeHead(200,{"content-encoding":"gzip"}),(0,u.pipeline)(i,(0,p.createGzip)(),s,n);break;case"deflate":s.writeHead(200,{"content-encoding":"deflate"}),(0,u.pipeline)(i,(0,p.createDeflate)(),s,n);break;default:s.writeHead(200),(0,u.pipeline)(i,s,n)}}bind(e){return this.engine=e,this.engine.on("connection",this.onconnection.bind(this)),this}onconnection(e){C("incoming connection with id %s",e.id);let t=new h.Client(this,e);return 3===e.protocol&&t.connect("/"),this}of(e,t){if("function"==typeof e||e instanceof RegExp){let s=new x.ParentNamespace(this);return C("initializing parent namespace %s",s.name),"function"==typeof e?this.parentNsps.set(e,s):(this.parentNsps.set((t,s,i)=>i(null,e.test(t)),s),this.parentNamespacesFromRegExp.set(e,s)),t&&s.on("connect",t),s}"/"!==String(e)[0]&&(e="/"+e);let s=this._nsps.get(e);if(!s){for(let[t,s]of this.parentNamespacesFromRegExp)if(t.test(e))return C("attaching namespace %s to parent namespace %s",e,t),s.createChild(e);C("initializing namespace %s",e),s=new v.Namespace(this,e),this._nsps.set(e,s),"/"!==e&&this.sockets.emitReserved("new_namespace",s)}return t&&s.on("connect",t),s}async close(e){await Promise.allSettled([...this._nsps.values()].map(async e=>{e.sockets.forEach(e=>{e._onclose("server shutting down")}),await e.adapter.close()})),this.engine.close(),(0,k.restoreAdapter)(),this.httpServer?this.httpServer.close(e):e&&e()}use(e){return this.sockets.use(e),this}to(e){return this.sockets.to(e)}in(e){return this.sockets.in(e)}except(e){return this.sockets.except(e)}send(...e){return this.sockets.emit("message",...e),this}write(...e){return this.sockets.emit("message",...e),this}serverSideEmit(e,...t){return this.sockets.serverSideEmit(e,...t)}serverSideEmitWithAck(e,...t){return this.sockets.serverSideEmitWithAck(e,...t)}allSockets(){return this.sockets.allSockets()}compress(e){return this.sockets.compress(e)}get volatile(){return this.sockets.volatile}get local(){return this.sockets.local}timeout(e){return this.sockets.timeout(e)}fetchSockets(){return this.sockets.fetchSockets()}socketsJoin(e){return this.sockets.socketsJoin(e)}socketsLeave(e){return this.sockets.socketsLeave(e)}disconnectSockets(e=!1){return this.sockets.disconnectSockets(e)}}t.Server=T,Object.keys(f.EventEmitter.prototype).filter(function(e){return"function"==typeof f.EventEmitter.prototype[e]}).forEach(function(e){T.prototype[e]=function(){return this.sockets[e].apply(this.sockets,arguments)}}),e.exports=(e,t)=>new T(e,t),e.exports.Server=T,e.exports.Namespace=v.Namespace,e.exports.Socket=_.Socket},582:function(e,t,s){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.Namespace=t.RESERVED_EVENTS=void 0;let a=s(49736),n=s(77160),o=i(s(49097)),r=s(24046),c=(0,o.default)("socket.io:namespace");t.RESERVED_EVENTS=new Set(["connect","connection","new_namespace"]);class p extends n.StrictEventEmitter{constructor(e,t){super(),this.sockets=new Map,this._preConnectSockets=new Map,this._fns=[],this._ids=0,this.server=e,this.name=t,this._initAdapter()}_initAdapter(){this.adapter=new(this.server.adapter())(this)}use(e){return this._fns.push(e),this}run(e,t){if(!this._fns.length)return t();let s=this._fns.slice(0);!function i(a){s[a](e,e=>e?t(e):s[a+1]?void i(a+1):t())}(0)}to(e){return new r.BroadcastOperator(this.adapter).to(e)}in(e){return new r.BroadcastOperator(this.adapter).in(e)}except(e){return new r.BroadcastOperator(this.adapter).except(e)}async _add(e,t,s){var i;c("adding socket to nsp %s",this.name);let a=await this._createSocket(e,t);if(this._preConnectSockets.set(a.id,a),(null===(i=this.server.opts.connectionStateRecovery)||void 0===i?void 0:i.skipMiddlewares)&&a.recovered&&"open"===e.conn.readyState)return this._doConnect(a,s);this.run(a,t=>{process.nextTick(()=>{if("open"!==e.conn.readyState){c("next called after client was closed - ignoring socket"),a._cleanup();return}if(t)return(c("middleware error, sending CONNECT_ERROR packet to the client"),a._cleanup(),3===e.conn.protocol)?a._error(t.data||t.message):a._error({message:t.message,data:t.data});this._doConnect(a,s)})})}async _createSocket(e,t){let s=t.pid,i=t.offset;if(this.server.opts.connectionStateRecovery&&"string"==typeof s&&"string"==typeof i){let n;try{n=await this.adapter.restoreSession(s,i)}catch(e){c("error while restoring session: %s",e)}if(n)return c("connection state recovered for sid %s",n.sid),new a.Socket(this,e,t,n)}return new a.Socket(this,e,t)}_doConnect(e,t){this._preConnectSockets.delete(e.id),this.sockets.set(e.id,e),e._onconnect(),t&&t(e),this.emitReserved("connect",e),this.emitReserved("connection",e)}_remove(e){this.sockets.delete(e.id)||this._preConnectSockets.delete(e.id)}emit(e,...t){return new r.BroadcastOperator(this.adapter).emit(e,...t)}send(...e){return this.emit("message",...e),this}write(...e){return this.emit("message",...e),this}serverSideEmit(e,...s){if(t.RESERVED_EVENTS.has(e))throw Error(`"${String(e)}" is a reserved event name`);return s.unshift(e),this.adapter.serverSideEmit(s),!0}serverSideEmitWithAck(e,...t){return new Promise((s,i)=>{t.push((e,t)=>e?(e.responses=t,i(e)):s(t)),this.serverSideEmit(e,...t)})}_onServerSideEmit(e){super.emitUntyped.apply(this,e)}allSockets(){return new r.BroadcastOperator(this.adapter).allSockets()}compress(e){return new r.BroadcastOperator(this.adapter).compress(e)}get volatile(){return new r.BroadcastOperator(this.adapter).volatile}get local(){return new r.BroadcastOperator(this.adapter).local}timeout(e){return new r.BroadcastOperator(this.adapter).timeout(e)}fetchSockets(){return new r.BroadcastOperator(this.adapter).fetchSockets()}socketsJoin(e){return new r.BroadcastOperator(this.adapter).socketsJoin(e)}socketsLeave(e){return new r.BroadcastOperator(this.adapter).socketsLeave(e)}disconnectSockets(e=!1){return new r.BroadcastOperator(this.adapter).disconnectSockets(e)}}t.Namespace=p},72907:function(e,t,s){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.ParentNamespace=void 0;let a=s(582),n=s(21605),o=(0,i(s(49097)).default)("socket.io:parent-namespace");class r extends a.Namespace{constructor(e){super(e,"/_"+r.count++),this.children=new Set}_initAdapter(){this.adapter=new c(this)}emit(e,...t){return this.children.forEach(s=>{s.emit(e,...t)}),!0}createChild(e){o("creating child namespace %s",e);let t=new a.Namespace(this.server,e);if(this._fns.forEach(e=>t.use(e)),this.listeners("connect").forEach(e=>t.on("connect",e)),this.listeners("connection").forEach(e=>t.on("connection",e)),this.children.add(t),this.server._opts.cleanupEmptyChildNamespaces){let s=t._remove;t._remove=i=>{s.call(t,i),0===t.sockets.size&&(o("closing child namespace %s",e),t.adapter.close(),this.server._nsps.delete(t.name),this.children.delete(t))}}return this.server._nsps.set(e,t),this.server.sockets.emitReserved("new_namespace",t),t}fetchSockets(){throw Error("fetchSockets() is not supported on parent namespaces")}}t.ParentNamespace=r,r.count=0;class c extends n.Adapter{broadcast(e,t){this.nsp.children.forEach(s=>{s.adapter.broadcast(e,t)})}}},96802:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.RESERVED_EVENTS=void 0,t.RESERVED_EVENTS=new Set(["connect","connect_error","disconnect","disconnecting","newListener","removeListener"])},49736:function(e,t,s){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.Socket=void 0;let a=s(18268),n=i(s(49097)),o=s(77160),r=i(s(49324)),c=s(24046),p=s(96802),l=(0,n.default)("socket.io:socket"),u=new Set(["transport error","transport close","forced close","ping timeout","server shutting down","forced server close"]);function d(){}class m extends o.StrictEventEmitter{constructor(e,t,s,i){super(),this.nsp=e,this.client=t,this.recovered=!1,this.data={},this.connected=!1,this.acks=new Map,this.fns=[],this.flags={},this.server=e.server,this.adapter=this.nsp.adapter,i?(this.id=i.sid,this.pid=i.pid,i.rooms.forEach(e=>this.join(e)),this.data=i.data,i.missedPackets.forEach(e=>{this.packet({type:a.PacketType.EVENT,data:e})}),this.recovered=!0):(3===t.conn.protocol?this.id="/"!==e.name?e.name+"#"+t.id:t.id:this.id=r.default.generateId(),this.server._opts.connectionStateRecovery&&(this.pid=r.default.generateId())),this.handshake=this.buildHandshake(s),this.on("error",d)}buildHandshake(e){var t,s,i,a;return{headers:(null===(t=this.request)||void 0===t?void 0:t.headers)||{},time:new Date+"",address:this.conn.remoteAddress,xdomain:!!(null===(s=this.request)||void 0===s?void 0:s.headers.origin),secure:!this.request||!!this.request.connection.encrypted,issued:+new Date,url:null===(i=this.request)||void 0===i?void 0:i.url,query:(null===(a=this.request)||void 0===a?void 0:a._query)||{},auth:e}}emit(e,...t){if(p.RESERVED_EVENTS.has(e))throw Error(`"${String(e)}" is a reserved event name`);let s=[e,...t],i={type:a.PacketType.EVENT,data:s};if("function"==typeof s[s.length-1]){let e=this.nsp._ids++;l("emitting packet with ack id %d",e),this.registerAckCallback(e,s.pop()),i.id=e}let n=Object.assign({},this.flags);return this.flags={},this.nsp.server.opts.connectionStateRecovery?this.adapter.broadcast(i,{rooms:new Set([this.id]),except:new Set,flags:n}):(this.notifyOutgoingListeners(i),this.packet(i,n)),!0}emitWithAck(e,...t){let s=void 0!==this.flags.timeout;return new Promise((i,a)=>{t.push((e,t)=>s?e?a(e):i(t):i(e)),this.emit(e,...t)})}registerAckCallback(e,t){let s=this.flags.timeout;if(void 0===s){this.acks.set(e,t);return}let i=setTimeout(()=>{l("event with ack id %d has timed out after %d ms",e,s),this.acks.delete(e),t.call(this,Error("operation has timed out"))},s);this.acks.set(e,(...e)=>{clearTimeout(i),t.apply(this,[null,...e])})}to(e){return this.newBroadcastOperator().to(e)}in(e){return this.newBroadcastOperator().in(e)}except(e){return this.newBroadcastOperator().except(e)}send(...e){return this.emit("message",...e),this}write(...e){return this.emit("message",...e),this}packet(e,t={}){e.nsp=this.nsp.name,t.compress=!1!==t.compress,this.client._packet(e,t)}join(e){return l("join room %s",e),this.adapter.addAll(this.id,new Set(Array.isArray(e)?e:[e]))}leave(e){return l("leave room %s",e),this.adapter.del(this.id,e)}leaveAll(){this.adapter.delAll(this.id)}_onconnect(){l("socket connected - writing packet"),this.connected=!0,this.join(this.id),3===this.conn.protocol?this.packet({type:a.PacketType.CONNECT}):this.packet({type:a.PacketType.CONNECT,data:{sid:this.id,pid:this.pid}})}_onpacket(e){switch(l("got packet %j",e),e.type){case a.PacketType.EVENT:case a.PacketType.BINARY_EVENT:this.onevent(e);break;case a.PacketType.ACK:case a.PacketType.BINARY_ACK:this.onack(e);break;case a.PacketType.DISCONNECT:this.ondisconnect()}}onevent(e){let t=e.data||[];if(l("emitting event %j",t),null!=e.id&&(l("attaching ack callback to event"),t.push(this.ack(e.id))),this._anyListeners&&this._anyListeners.length)for(let e of this._anyListeners.slice())e.apply(this,t);this.dispatch(t)}ack(e){let t=this,s=!1;return function(){if(s)return;let i=Array.prototype.slice.call(arguments);l("sending ack %j",i),t.packet({id:e,type:a.PacketType.ACK,data:i}),s=!0}}onack(e){let t=this.acks.get(e.id);"function"==typeof t?(l("calling ack %s with %j",e.id,e.data),t.apply(this,e.data),this.acks.delete(e.id)):l("bad ack %s",e.id)}ondisconnect(){l("got disconnect packet"),this._onclose("client namespace disconnect")}_onerror(e){this.emitReserved("error",e)}_onclose(e,t){if(!this.connected)return this;l("closing socket - reason %s",e),this.emitReserved("disconnecting",e,t),this.server._opts.connectionStateRecovery&&u.has(e)&&(l("connection state recovery is enabled for sid %s",this.id),this.adapter.persistSession({sid:this.id,pid:this.pid,rooms:[...this.rooms],data:this.data})),this._cleanup(),this.client._remove(this),this.connected=!1,this.emitReserved("disconnect",e,t)}_cleanup(){this.leaveAll(),this.nsp._remove(this),this.join=d}_error(e){this.packet({type:a.PacketType.CONNECT_ERROR,data:e})}disconnect(e=!1){return this.connected&&(e?this.client._disconnect():(this.packet({type:a.PacketType.DISCONNECT}),this._onclose("server namespace disconnect"))),this}compress(e){return this.flags.compress=e,this}get volatile(){return this.flags.volatile=!0,this}get broadcast(){return this.newBroadcastOperator()}get local(){return this.newBroadcastOperator().local}timeout(e){return this.flags.timeout=e,this}dispatch(e){l("dispatching an event %j",e),this.run(e,t=>{process.nextTick(()=>{if(t)return this._onerror(t);this.connected?super.emitUntyped.apply(this,e):l("ignore packet received after disconnection")})})}use(e){return this.fns.push(e),this}run(e,t){if(!this.fns.length)return t();let s=this.fns.slice(0);!function i(a){s[a](e,e=>e?t(e):s[a+1]?void i(a+1):t())}(0)}get disconnected(){return!this.connected}get request(){return this.client.request}get conn(){return this.client.conn}get rooms(){return this.adapter.socketRooms(this.id)||new Set}onAny(e){return this._anyListeners=this._anyListeners||[],this._anyListeners.push(e),this}prependAny(e){return this._anyListeners=this._anyListeners||[],this._anyListeners.unshift(e),this}offAny(e){if(!this._anyListeners)return this;if(e){let t=this._anyListeners;for(let s=0;s<t.length;s++)if(e===t[s]){t.splice(s,1);break}}else this._anyListeners=[];return this}listenersAny(){return this._anyListeners||[]}onAnyOutgoing(e){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.push(e),this}prependAnyOutgoing(e){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.unshift(e),this}offAnyOutgoing(e){if(!this._anyOutgoingListeners)return this;if(e){let t=this._anyOutgoingListeners;for(let s=0;s<t.length;s++)if(e===t[s]){t.splice(s,1);break}}else this._anyOutgoingListeners=[];return this}listenersAnyOutgoing(){return this._anyOutgoingListeners||[]}notifyOutgoingListeners(e){if(this._anyOutgoingListeners&&this._anyOutgoingListeners.length)for(let t of this._anyOutgoingListeners.slice())t.apply(this,e.data)}newBroadcastOperator(){let e=Object.assign({},this.flags);return this.flags={},new c.BroadcastOperator(this.adapter,new Set,new Set([this.id]),e)}}t.Socket=m},77160:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.StrictEventEmitter=void 0;let i=s(82361);class a extends i.EventEmitter{on(e,t){return super.on(e,t)}once(e,t){return super.once(e,t)}emit(e,...t){return super.emit(e,...t)}emitReserved(e,...t){return super.emit(e,...t)}emitUntyped(e,...t){return super.emit(e,...t)}listeners(e){return super.listeners(e)}}t.StrictEventEmitter=a},37393:function(e,t,s){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.patchAdapter=function(e){a.Adapter.prototype.addAll=function(e,t){let s=!this.sids.has(e);r.call(this,e,t);let i=this.nsp.sockets.get(e)||this.nsp._preConnectSockets.get(e);if(i){if("websocket"===i.conn.transport.name){l(this.nsp.name,i,s,t);return}s&&i.conn.on("upgrade",()=>{let t=this.sids.get(e);t&&l(this.nsp.name,i,s,t)})}},a.Adapter.prototype.del=function(e,t){c.call(this,e,t);let s=this.nsp.sockets.get(e)||this.nsp._preConnectSockets.get(e);if(s&&"websocket"===s.conn.transport.name){let e=s.conn.id,i=s.conn.transport.socket,a=`${this.nsp.name}${t}`;o("unsubscribe connection %s from topic %s",e,a),i.unsubscribe(a)}},a.Adapter.prototype.broadcast=function(t,s){if(!(s.rooms.size<=1&&0===s.except.size)){p.call(this,t,s);return}let i=s.flags||{},a={preEncoded:!0,volatile:i.volatile,compress:i.compress};t.nsp=this.nsp.name;let n=this.encoder.encode(t),r=0===s.rooms.size?this.nsp.name:`${this.nsp.name}${s.rooms.keys().next().value}`;o("fast publish to %s",r),n.forEach(t=>{let s="string"!=typeof t;e.publish(r,s?t:"4"+t,s)}),this.apply(s,e=>{"websocket"!==e.conn.transport.name&&e.client.writeToEngine(n,a)})}},t.restoreAdapter=function(){a.Adapter.prototype.addAll=r,a.Adapter.prototype.del=c,a.Adapter.prototype.broadcast=p},t.serveFile=function(e,t){let{size:s}=(0,n.statSync)(t),i=(0,n.createReadStream)(t),a=()=>!i.destroyed&&i.destroy();e.onAborted(a),i.on("data",t=>{let a=u(t);e.cork(()=>{let t=e.getWriteOffset(),[n,o]=e.tryEnd(a,s);o||n||(i.pause(),e.onWritable(n=>{let[o,r]=e.tryEnd(a.slice(n-t),s);return!r&&o&&i.resume(),o}))})}).on("error",e=>{throw a(),e}).on("end",a)};let a=s(21605),n=s(57147),o=(0,i(s(49097)).default)("socket.io:adapter-uws"),{addAll:r,del:c,broadcast:p}=a.Adapter.prototype;function l(e,t,s,i){let a=t.conn.id,n=t.conn.transport.socket;s&&(o("subscribe connection %s to topic %s",a,e),n.subscribe(e)),i.forEach(t=>{let s=`${e}${t}`;o("subscribe connection %s to topic %s",a,s),n.subscribe(s)})}let u=e=>{let{buffer:t,byteOffset:s,byteLength:i}=e;return t.slice(s,s+i)}},51848:(e,t,s)=>{"use strict";function i(e){if(e)return function(e){for(var t in i.prototype)e[t]=i.prototype[t];return e}(e)}s.r(t),s.d(t,{Emitter:()=>i}),i.prototype.on=i.prototype.addEventListener=function(e,t){return this._callbacks=this._callbacks||{},(this._callbacks["$"+e]=this._callbacks["$"+e]||[]).push(t),this},i.prototype.once=function(e,t){function s(){this.off(e,s),t.apply(this,arguments)}return s.fn=t,this.on(e,s),this},i.prototype.off=i.prototype.removeListener=i.prototype.removeAllListeners=i.prototype.removeEventListener=function(e,t){if(this._callbacks=this._callbacks||{},0==arguments.length)return this._callbacks={},this;var s,i=this._callbacks["$"+e];if(!i)return this;if(1==arguments.length)return delete this._callbacks["$"+e],this;for(var a=0;a<i.length;a++)if((s=i[a])===t||s.fn===t){i.splice(a,1);break}return 0===i.length&&delete this._callbacks["$"+e],this},i.prototype.emit=function(e){this._callbacks=this._callbacks||{};for(var t=Array(arguments.length-1),s=this._callbacks["$"+e],i=1;i<arguments.length;i++)t[i-1]=arguments[i];if(s){s=s.slice(0);for(var i=0,a=s.length;i<a;++i)s[i].apply(this,t)}return this},i.prototype.emitReserved=i.prototype.emit,i.prototype.listeners=function(e){return this._callbacks=this._callbacks||{},this._callbacks["$"+e]||[]},i.prototype.hasListeners=function(e){return!!this.listeners(e).length}},2753:e=>{"use strict";e.exports=JSON.parse('{"application/1d-interleaved-parityfec":{"source":"iana"},"application/3gpdash-qoe-report+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/3gpp-ims+xml":{"source":"iana","compressible":true},"application/3gpphal+json":{"source":"iana","compressible":true},"application/3gpphalforms+json":{"source":"iana","compressible":true},"application/a2l":{"source":"iana"},"application/ace+cbor":{"source":"iana"},"application/activemessage":{"source":"iana"},"application/activity+json":{"source":"iana","compressible":true},"application/alto-costmap+json":{"source":"iana","compressible":true},"application/alto-costmapfilter+json":{"source":"iana","compressible":true},"application/alto-directory+json":{"source":"iana","compressible":true},"application/alto-endpointcost+json":{"source":"iana","compressible":true},"application/alto-endpointcostparams+json":{"source":"iana","compressible":true},"application/alto-endpointprop+json":{"source":"iana","compressible":true},"application/alto-endpointpropparams+json":{"source":"iana","compressible":true},"application/alto-error+json":{"source":"iana","compressible":true},"application/alto-networkmap+json":{"source":"iana","compressible":true},"application/alto-networkmapfilter+json":{"source":"iana","compressible":true},"application/alto-updatestreamcontrol+json":{"source":"iana","compressible":true},"application/alto-updatestreamparams+json":{"source":"iana","compressible":true},"application/aml":{"source":"iana"},"application/andrew-inset":{"source":"iana","extensions":["ez"]},"application/applefile":{"source":"iana"},"application/applixware":{"source":"apache","extensions":["aw"]},"application/at+jwt":{"source":"iana"},"application/atf":{"source":"iana"},"application/atfx":{"source":"iana"},"application/atom+xml":{"source":"iana","compressible":true,"extensions":["atom"]},"application/atomcat+xml":{"source":"iana","compressible":true,"extensions":["atomcat"]},"application/atomdeleted+xml":{"source":"iana","compressible":true,"extensions":["atomdeleted"]},"application/atomicmail":{"source":"iana"},"application/atomsvc+xml":{"source":"iana","compressible":true,"extensions":["atomsvc"]},"application/atsc-dwd+xml":{"source":"iana","compressible":true,"extensions":["dwd"]},"application/atsc-dynamic-event-message":{"source":"iana"},"application/atsc-held+xml":{"source":"iana","compressible":true,"extensions":["held"]},"application/atsc-rdt+json":{"source":"iana","compressible":true},"application/atsc-rsat+xml":{"source":"iana","compressible":true,"extensions":["rsat"]},"application/atxml":{"source":"iana"},"application/auth-policy+xml":{"source":"iana","compressible":true},"application/bacnet-xdd+zip":{"source":"iana","compressible":false},"application/batch-smtp":{"source":"iana"},"application/bdoc":{"compressible":false,"extensions":["bdoc"]},"application/beep+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/calendar+json":{"source":"iana","compressible":true},"application/calendar+xml":{"source":"iana","compressible":true,"extensions":["xcs"]},"application/call-completion":{"source":"iana"},"application/cals-1840":{"source":"iana"},"application/captive+json":{"source":"iana","compressible":true},"application/cbor":{"source":"iana"},"application/cbor-seq":{"source":"iana"},"application/cccex":{"source":"iana"},"application/ccmp+xml":{"source":"iana","compressible":true},"application/ccxml+xml":{"source":"iana","compressible":true,"extensions":["ccxml"]},"application/cdfx+xml":{"source":"iana","compressible":true,"extensions":["cdfx"]},"application/cdmi-capability":{"source":"iana","extensions":["cdmia"]},"application/cdmi-container":{"source":"iana","extensions":["cdmic"]},"application/cdmi-domain":{"source":"iana","extensions":["cdmid"]},"application/cdmi-object":{"source":"iana","extensions":["cdmio"]},"application/cdmi-queue":{"source":"iana","extensions":["cdmiq"]},"application/cdni":{"source":"iana"},"application/cea":{"source":"iana"},"application/cea-2018+xml":{"source":"iana","compressible":true},"application/cellml+xml":{"source":"iana","compressible":true},"application/cfw":{"source":"iana"},"application/city+json":{"source":"iana","compressible":true},"application/clr":{"source":"iana"},"application/clue+xml":{"source":"iana","compressible":true},"application/clue_info+xml":{"source":"iana","compressible":true},"application/cms":{"source":"iana"},"application/cnrp+xml":{"source":"iana","compressible":true},"application/coap-group+json":{"source":"iana","compressible":true},"application/coap-payload":{"source":"iana"},"application/commonground":{"source":"iana"},"application/conference-info+xml":{"source":"iana","compressible":true},"application/cose":{"source":"iana"},"application/cose-key":{"source":"iana"},"application/cose-key-set":{"source":"iana"},"application/cpl+xml":{"source":"iana","compressible":true,"extensions":["cpl"]},"application/csrattrs":{"source":"iana"},"application/csta+xml":{"source":"iana","compressible":true},"application/cstadata+xml":{"source":"iana","compressible":true},"application/csvm+json":{"source":"iana","compressible":true},"application/cu-seeme":{"source":"apache","extensions":["cu"]},"application/cwt":{"source":"iana"},"application/cybercash":{"source":"iana"},"application/dart":{"compressible":true},"application/dash+xml":{"source":"iana","compressible":true,"extensions":["mpd"]},"application/dash-patch+xml":{"source":"iana","compressible":true,"extensions":["mpp"]},"application/dashdelta":{"source":"iana"},"application/davmount+xml":{"source":"iana","compressible":true,"extensions":["davmount"]},"application/dca-rft":{"source":"iana"},"application/dcd":{"source":"iana"},"application/dec-dx":{"source":"iana"},"application/dialog-info+xml":{"source":"iana","compressible":true},"application/dicom":{"source":"iana"},"application/dicom+json":{"source":"iana","compressible":true},"application/dicom+xml":{"source":"iana","compressible":true},"application/dii":{"source":"iana"},"application/dit":{"source":"iana"},"application/dns":{"source":"iana"},"application/dns+json":{"source":"iana","compressible":true},"application/dns-message":{"source":"iana"},"application/docbook+xml":{"source":"apache","compressible":true,"extensions":["dbk"]},"application/dots+cbor":{"source":"iana"},"application/dskpp+xml":{"source":"iana","compressible":true},"application/dssc+der":{"source":"iana","extensions":["dssc"]},"application/dssc+xml":{"source":"iana","compressible":true,"extensions":["xdssc"]},"application/dvcs":{"source":"iana"},"application/ecmascript":{"source":"iana","compressible":true,"extensions":["es","ecma"]},"application/edi-consent":{"source":"iana"},"application/edi-x12":{"source":"iana","compressible":false},"application/edifact":{"source":"iana","compressible":false},"application/efi":{"source":"iana"},"application/elm+json":{"source":"iana","charset":"UTF-8","compressible":true},"application/elm+xml":{"source":"iana","compressible":true},"application/emergencycalldata.cap+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/emergencycalldata.comment+xml":{"source":"iana","compressible":true},"application/emergencycalldata.control+xml":{"source":"iana","compressible":true},"application/emergencycalldata.deviceinfo+xml":{"source":"iana","compressible":true},"application/emergencycalldata.ecall.msd":{"source":"iana"},"application/emergencycalldata.providerinfo+xml":{"source":"iana","compressible":true},"application/emergencycalldata.serviceinfo+xml":{"source":"iana","compressible":true},"application/emergencycalldata.subscriberinfo+xml":{"source":"iana","compressible":true},"application/emergencycalldata.veds+xml":{"source":"iana","compressible":true},"application/emma+xml":{"source":"iana","compressible":true,"extensions":["emma"]},"application/emotionml+xml":{"source":"iana","compressible":true,"extensions":["emotionml"]},"application/encaprtp":{"source":"iana"},"application/epp+xml":{"source":"iana","compressible":true},"application/epub+zip":{"source":"iana","compressible":false,"extensions":["epub"]},"application/eshop":{"source":"iana"},"application/exi":{"source":"iana","extensions":["exi"]},"application/expect-ct-report+json":{"source":"iana","compressible":true},"application/express":{"source":"iana","extensions":["exp"]},"application/fastinfoset":{"source":"iana"},"application/fastsoap":{"source":"iana"},"application/fdt+xml":{"source":"iana","compressible":true,"extensions":["fdt"]},"application/fhir+json":{"source":"iana","charset":"UTF-8","compressible":true},"application/fhir+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/fido.trusted-apps+json":{"compressible":true},"application/fits":{"source":"iana"},"application/flexfec":{"source":"iana"},"application/font-sfnt":{"source":"iana"},"application/font-tdpfr":{"source":"iana","extensions":["pfr"]},"application/font-woff":{"source":"iana","compressible":false},"application/framework-attributes+xml":{"source":"iana","compressible":true},"application/geo+json":{"source":"iana","compressible":true,"extensions":["geojson"]},"application/geo+json-seq":{"source":"iana"},"application/geopackage+sqlite3":{"source":"iana"},"application/geoxacml+xml":{"source":"iana","compressible":true},"application/gltf-buffer":{"source":"iana"},"application/gml+xml":{"source":"iana","compressible":true,"extensions":["gml"]},"application/gpx+xml":{"source":"apache","compressible":true,"extensions":["gpx"]},"application/gxf":{"source":"apache","extensions":["gxf"]},"application/gzip":{"source":"iana","compressible":false,"extensions":["gz"]},"application/h224":{"source":"iana"},"application/held+xml":{"source":"iana","compressible":true},"application/hjson":{"extensions":["hjson"]},"application/http":{"source":"iana"},"application/hyperstudio":{"source":"iana","extensions":["stk"]},"application/ibe-key-request+xml":{"source":"iana","compressible":true},"application/ibe-pkg-reply+xml":{"source":"iana","compressible":true},"application/ibe-pp-data":{"source":"iana"},"application/iges":{"source":"iana"},"application/im-iscomposing+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/index":{"source":"iana"},"application/index.cmd":{"source":"iana"},"application/index.obj":{"source":"iana"},"application/index.response":{"source":"iana"},"application/index.vnd":{"source":"iana"},"application/inkml+xml":{"source":"iana","compressible":true,"extensions":["ink","inkml"]},"application/iotp":{"source":"iana"},"application/ipfix":{"source":"iana","extensions":["ipfix"]},"application/ipp":{"source":"iana"},"application/isup":{"source":"iana"},"application/its+xml":{"source":"iana","compressible":true,"extensions":["its"]},"application/java-archive":{"source":"apache","compressible":false,"extensions":["jar","war","ear"]},"application/java-serialized-object":{"source":"apache","compressible":false,"extensions":["ser"]},"application/java-vm":{"source":"apache","compressible":false,"extensions":["class"]},"application/javascript":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["js","mjs"]},"application/jf2feed+json":{"source":"iana","compressible":true},"application/jose":{"source":"iana"},"application/jose+json":{"source":"iana","compressible":true},"application/jrd+json":{"source":"iana","compressible":true},"application/jscalendar+json":{"source":"iana","compressible":true},"application/json":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["json","map"]},"application/json-patch+json":{"source":"iana","compressible":true},"application/json-seq":{"source":"iana"},"application/json5":{"extensions":["json5"]},"application/jsonml+json":{"source":"apache","compressible":true,"extensions":["jsonml"]},"application/jwk+json":{"source":"iana","compressible":true},"application/jwk-set+json":{"source":"iana","compressible":true},"application/jwt":{"source":"iana"},"application/kpml-request+xml":{"source":"iana","compressible":true},"application/kpml-response+xml":{"source":"iana","compressible":true},"application/ld+json":{"source":"iana","compressible":true,"extensions":["jsonld"]},"application/lgr+xml":{"source":"iana","compressible":true,"extensions":["lgr"]},"application/link-format":{"source":"iana"},"application/load-control+xml":{"source":"iana","compressible":true},"application/lost+xml":{"source":"iana","compressible":true,"extensions":["lostxml"]},"application/lostsync+xml":{"source":"iana","compressible":true},"application/lpf+zip":{"source":"iana","compressible":false},"application/lxf":{"source":"iana"},"application/mac-binhex40":{"source":"iana","extensions":["hqx"]},"application/mac-compactpro":{"source":"apache","extensions":["cpt"]},"application/macwriteii":{"source":"iana"},"application/mads+xml":{"source":"iana","compressible":true,"extensions":["mads"]},"application/manifest+json":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["webmanifest"]},"application/marc":{"source":"iana","extensions":["mrc"]},"application/marcxml+xml":{"source":"iana","compressible":true,"extensions":["mrcx"]},"application/mathematica":{"source":"iana","extensions":["ma","nb","mb"]},"application/mathml+xml":{"source":"iana","compressible":true,"extensions":["mathml"]},"application/mathml-content+xml":{"source":"iana","compressible":true},"application/mathml-presentation+xml":{"source":"iana","compressible":true},"application/mbms-associated-procedure-description+xml":{"source":"iana","compressible":true},"application/mbms-deregister+xml":{"source":"iana","compressible":true},"application/mbms-envelope+xml":{"source":"iana","compressible":true},"application/mbms-msk+xml":{"source":"iana","compressible":true},"application/mbms-msk-response+xml":{"source":"iana","compressible":true},"application/mbms-protection-description+xml":{"source":"iana","compressible":true},"application/mbms-reception-report+xml":{"source":"iana","compressible":true},"application/mbms-register+xml":{"source":"iana","compressible":true},"application/mbms-register-response+xml":{"source":"iana","compressible":true},"application/mbms-schedule+xml":{"source":"iana","compressible":true},"application/mbms-user-service-description+xml":{"source":"iana","compressible":true},"application/mbox":{"source":"iana","extensions":["mbox"]},"application/media-policy-dataset+xml":{"source":"iana","compressible":true,"extensions":["mpf"]},"application/media_control+xml":{"source":"iana","compressible":true},"application/mediaservercontrol+xml":{"source":"iana","compressible":true,"extensions":["mscml"]},"application/merge-patch+json":{"source":"iana","compressible":true},"application/metalink+xml":{"source":"apache","compressible":true,"extensions":["metalink"]},"application/metalink4+xml":{"source":"iana","compressible":true,"extensions":["meta4"]},"application/mets+xml":{"source":"iana","compressible":true,"extensions":["mets"]},"application/mf4":{"source":"iana"},"application/mikey":{"source":"iana"},"application/mipc":{"source":"iana"},"application/missing-blocks+cbor-seq":{"source":"iana"},"application/mmt-aei+xml":{"source":"iana","compressible":true,"extensions":["maei"]},"application/mmt-usd+xml":{"source":"iana","compressible":true,"extensions":["musd"]},"application/mods+xml":{"source":"iana","compressible":true,"extensions":["mods"]},"application/moss-keys":{"source":"iana"},"application/moss-signature":{"source":"iana"},"application/mosskey-data":{"source":"iana"},"application/mosskey-request":{"source":"iana"},"application/mp21":{"source":"iana","extensions":["m21","mp21"]},"application/mp4":{"source":"iana","extensions":["mp4s","m4p"]},"application/mpeg4-generic":{"source":"iana"},"application/mpeg4-iod":{"source":"iana"},"application/mpeg4-iod-xmt":{"source":"iana"},"application/mrb-consumer+xml":{"source":"iana","compressible":true},"application/mrb-publish+xml":{"source":"iana","compressible":true},"application/msc-ivr+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/msc-mixer+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/msword":{"source":"iana","compressible":false,"extensions":["doc","dot"]},"application/mud+json":{"source":"iana","compressible":true},"application/multipart-core":{"source":"iana"},"application/mxf":{"source":"iana","extensions":["mxf"]},"application/n-quads":{"source":"iana","extensions":["nq"]},"application/n-triples":{"source":"iana","extensions":["nt"]},"application/nasdata":{"source":"iana"},"application/news-checkgroups":{"source":"iana","charset":"US-ASCII"},"application/news-groupinfo":{"source":"iana","charset":"US-ASCII"},"application/news-transmission":{"source":"iana"},"application/nlsml+xml":{"source":"iana","compressible":true},"application/node":{"source":"iana","extensions":["cjs"]},"application/nss":{"source":"iana"},"application/oauth-authz-req+jwt":{"source":"iana"},"application/oblivious-dns-message":{"source":"iana"},"application/ocsp-request":{"source":"iana"},"application/ocsp-response":{"source":"iana"},"application/octet-stream":{"source":"iana","compressible":false,"extensions":["bin","dms","lrf","mar","so","dist","distz","pkg","bpk","dump","elc","deploy","exe","dll","deb","dmg","iso","img","msi","msp","msm","buffer"]},"application/oda":{"source":"iana","extensions":["oda"]},"application/odm+xml":{"source":"iana","compressible":true},"application/odx":{"source":"iana"},"application/oebps-package+xml":{"source":"iana","compressible":true,"extensions":["opf"]},"application/ogg":{"source":"iana","compressible":false,"extensions":["ogx"]},"application/omdoc+xml":{"source":"apache","compressible":true,"extensions":["omdoc"]},"application/onenote":{"source":"apache","extensions":["onetoc","onetoc2","onetmp","onepkg"]},"application/opc-nodeset+xml":{"source":"iana","compressible":true},"application/oscore":{"source":"iana"},"application/oxps":{"source":"iana","extensions":["oxps"]},"application/p21":{"source":"iana"},"application/p21+zip":{"source":"iana","compressible":false},"application/p2p-overlay+xml":{"source":"iana","compressible":true,"extensions":["relo"]},"application/parityfec":{"source":"iana"},"application/passport":{"source":"iana"},"application/patch-ops-error+xml":{"source":"iana","compressible":true,"extensions":["xer"]},"application/pdf":{"source":"iana","compressible":false,"extensions":["pdf"]},"application/pdx":{"source":"iana"},"application/pem-certificate-chain":{"source":"iana"},"application/pgp-encrypted":{"source":"iana","compressible":false,"extensions":["pgp"]},"application/pgp-keys":{"source":"iana","extensions":["asc"]},"application/pgp-signature":{"source":"iana","extensions":["asc","sig"]},"application/pics-rules":{"source":"apache","extensions":["prf"]},"application/pidf+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/pidf-diff+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/pkcs10":{"source":"iana","extensions":["p10"]},"application/pkcs12":{"source":"iana"},"application/pkcs7-mime":{"source":"iana","extensions":["p7m","p7c"]},"application/pkcs7-signature":{"source":"iana","extensions":["p7s"]},"application/pkcs8":{"source":"iana","extensions":["p8"]},"application/pkcs8-encrypted":{"source":"iana"},"application/pkix-attr-cert":{"source":"iana","extensions":["ac"]},"application/pkix-cert":{"source":"iana","extensions":["cer"]},"application/pkix-crl":{"source":"iana","extensions":["crl"]},"application/pkix-pkipath":{"source":"iana","extensions":["pkipath"]},"application/pkixcmp":{"source":"iana","extensions":["pki"]},"application/pls+xml":{"source":"iana","compressible":true,"extensions":["pls"]},"application/poc-settings+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/postscript":{"source":"iana","compressible":true,"extensions":["ai","eps","ps"]},"application/ppsp-tracker+json":{"source":"iana","compressible":true},"application/problem+json":{"source":"iana","compressible":true},"application/problem+xml":{"source":"iana","compressible":true},"application/provenance+xml":{"source":"iana","compressible":true,"extensions":["provx"]},"application/prs.alvestrand.titrax-sheet":{"source":"iana"},"application/prs.cww":{"source":"iana","extensions":["cww"]},"application/prs.cyn":{"source":"iana","charset":"7-BIT"},"application/prs.hpub+zip":{"source":"iana","compressible":false},"application/prs.nprend":{"source":"iana"},"application/prs.plucker":{"source":"iana"},"application/prs.rdf-xml-crypt":{"source":"iana"},"application/prs.xsf+xml":{"source":"iana","compressible":true},"application/pskc+xml":{"source":"iana","compressible":true,"extensions":["pskcxml"]},"application/pvd+json":{"source":"iana","compressible":true},"application/qsig":{"source":"iana"},"application/raml+yaml":{"compressible":true,"extensions":["raml"]},"application/raptorfec":{"source":"iana"},"application/rdap+json":{"source":"iana","compressible":true},"application/rdf+xml":{"source":"iana","compressible":true,"extensions":["rdf","owl"]},"application/reginfo+xml":{"source":"iana","compressible":true,"extensions":["rif"]},"application/relax-ng-compact-syntax":{"source":"iana","extensions":["rnc"]},"application/remote-printing":{"source":"iana"},"application/reputon+json":{"source":"iana","compressible":true},"application/resource-lists+xml":{"source":"iana","compressible":true,"extensions":["rl"]},"application/resource-lists-diff+xml":{"source":"iana","compressible":true,"extensions":["rld"]},"application/rfc+xml":{"source":"iana","compressible":true},"application/riscos":{"source":"iana"},"application/rlmi+xml":{"source":"iana","compressible":true},"application/rls-services+xml":{"source":"iana","compressible":true,"extensions":["rs"]},"application/route-apd+xml":{"source":"iana","compressible":true,"extensions":["rapd"]},"application/route-s-tsid+xml":{"source":"iana","compressible":true,"extensions":["sls"]},"application/route-usd+xml":{"source":"iana","compressible":true,"extensions":["rusd"]},"application/rpki-ghostbusters":{"source":"iana","extensions":["gbr"]},"application/rpki-manifest":{"source":"iana","extensions":["mft"]},"application/rpki-publication":{"source":"iana"},"application/rpki-roa":{"source":"iana","extensions":["roa"]},"application/rpki-updown":{"source":"iana"},"application/rsd+xml":{"source":"apache","compressible":true,"extensions":["rsd"]},"application/rss+xml":{"source":"apache","compressible":true,"extensions":["rss"]},"application/rtf":{"source":"iana","compressible":true,"extensions":["rtf"]},"application/rtploopback":{"source":"iana"},"application/rtx":{"source":"iana"},"application/samlassertion+xml":{"source":"iana","compressible":true},"application/samlmetadata+xml":{"source":"iana","compressible":true},"application/sarif+json":{"source":"iana","compressible":true},"application/sarif-external-properties+json":{"source":"iana","compressible":true},"application/sbe":{"source":"iana"},"application/sbml+xml":{"source":"iana","compressible":true,"extensions":["sbml"]},"application/scaip+xml":{"source":"iana","compressible":true},"application/scim+json":{"source":"iana","compressible":true},"application/scvp-cv-request":{"source":"iana","extensions":["scq"]},"application/scvp-cv-response":{"source":"iana","extensions":["scs"]},"application/scvp-vp-request":{"source":"iana","extensions":["spq"]},"application/scvp-vp-response":{"source":"iana","extensions":["spp"]},"application/sdp":{"source":"iana","extensions":["sdp"]},"application/secevent+jwt":{"source":"iana"},"application/senml+cbor":{"source":"iana"},"application/senml+json":{"source":"iana","compressible":true},"application/senml+xml":{"source":"iana","compressible":true,"extensions":["senmlx"]},"application/senml-etch+cbor":{"source":"iana"},"application/senml-etch+json":{"source":"iana","compressible":true},"application/senml-exi":{"source":"iana"},"application/sensml+cbor":{"source":"iana"},"application/sensml+json":{"source":"iana","compressible":true},"application/sensml+xml":{"source":"iana","compressible":true,"extensions":["sensmlx"]},"application/sensml-exi":{"source":"iana"},"application/sep+xml":{"source":"iana","compressible":true},"application/sep-exi":{"source":"iana"},"application/session-info":{"source":"iana"},"application/set-payment":{"source":"iana"},"application/set-payment-initiation":{"source":"iana","extensions":["setpay"]},"application/set-registration":{"source":"iana"},"application/set-registration-initiation":{"source":"iana","extensions":["setreg"]},"application/sgml":{"source":"iana"},"application/sgml-open-catalog":{"source":"iana"},"application/shf+xml":{"source":"iana","compressible":true,"extensions":["shf"]},"application/sieve":{"source":"iana","extensions":["siv","sieve"]},"application/simple-filter+xml":{"source":"iana","compressible":true},"application/simple-message-summary":{"source":"iana"},"application/simplesymbolcontainer":{"source":"iana"},"application/sipc":{"source":"iana"},"application/slate":{"source":"iana"},"application/smil":{"source":"iana"},"application/smil+xml":{"source":"iana","compressible":true,"extensions":["smi","smil"]},"application/smpte336m":{"source":"iana"},"application/soap+fastinfoset":{"source":"iana"},"application/soap+xml":{"source":"iana","compressible":true},"application/sparql-query":{"source":"iana","extensions":["rq"]},"application/sparql-results+xml":{"source":"iana","compressible":true,"extensions":["srx"]},"application/spdx+json":{"source":"iana","compressible":true},"application/spirits-event+xml":{"source":"iana","compressible":true},"application/sql":{"source":"iana"},"application/srgs":{"source":"iana","extensions":["gram"]},"application/srgs+xml":{"source":"iana","compressible":true,"extensions":["grxml"]},"application/sru+xml":{"source":"iana","compressible":true,"extensions":["sru"]},"application/ssdl+xml":{"source":"apache","compressible":true,"extensions":["ssdl"]},"application/ssml+xml":{"source":"iana","compressible":true,"extensions":["ssml"]},"application/stix+json":{"source":"iana","compressible":true},"application/swid+xml":{"source":"iana","compressible":true,"extensions":["swidtag"]},"application/tamp-apex-update":{"source":"iana"},"application/tamp-apex-update-confirm":{"source":"iana"},"application/tamp-community-update":{"source":"iana"},"application/tamp-community-update-confirm":{"source":"iana"},"application/tamp-error":{"source":"iana"},"application/tamp-sequence-adjust":{"source":"iana"},"application/tamp-sequence-adjust-confirm":{"source":"iana"},"application/tamp-status-query":{"source":"iana"},"application/tamp-status-response":{"source":"iana"},"application/tamp-update":{"source":"iana"},"application/tamp-update-confirm":{"source":"iana"},"application/tar":{"compressible":true},"application/taxii+json":{"source":"iana","compressible":true},"application/td+json":{"source":"iana","compressible":true},"application/tei+xml":{"source":"iana","compressible":true,"extensions":["tei","teicorpus"]},"application/tetra_isi":{"source":"iana"},"application/thraud+xml":{"source":"iana","compressible":true,"extensions":["tfi"]},"application/timestamp-query":{"source":"iana"},"application/timestamp-reply":{"source":"iana"},"application/timestamped-data":{"source":"iana","extensions":["tsd"]},"application/tlsrpt+gzip":{"source":"iana"},"application/tlsrpt+json":{"source":"iana","compressible":true},"application/tnauthlist":{"source":"iana"},"application/token-introspection+jwt":{"source":"iana"},"application/toml":{"compressible":true,"extensions":["toml"]},"application/trickle-ice-sdpfrag":{"source":"iana"},"application/trig":{"source":"iana","extensions":["trig"]},"application/ttml+xml":{"source":"iana","compressible":true,"extensions":["ttml"]},"application/tve-trigger":{"source":"iana"},"application/tzif":{"source":"iana"},"application/tzif-leap":{"source":"iana"},"application/ubjson":{"compressible":false,"extensions":["ubj"]},"application/ulpfec":{"source":"iana"},"application/urc-grpsheet+xml":{"source":"iana","compressible":true},"application/urc-ressheet+xml":{"source":"iana","compressible":true,"extensions":["rsheet"]},"application/urc-targetdesc+xml":{"source":"iana","compressible":true,"extensions":["td"]},"application/urc-uisocketdesc+xml":{"source":"iana","compressible":true},"application/vcard+json":{"source":"iana","compressible":true},"application/vcard+xml":{"source":"iana","compressible":true},"application/vemmi":{"source":"iana"},"application/vividence.scriptfile":{"source":"apache"},"application/vnd.1000minds.decision-model+xml":{"source":"iana","compressible":true,"extensions":["1km"]},"application/vnd.3gpp-prose+xml":{"source":"iana","compressible":true},"application/vnd.3gpp-prose-pc3ch+xml":{"source":"iana","compressible":true},"application/vnd.3gpp-v2x-local-service-information":{"source":"iana"},"application/vnd.3gpp.5gnas":{"source":"iana"},"application/vnd.3gpp.access-transfer-events+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.bsf+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.gmop+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.gtpc":{"source":"iana"},"application/vnd.3gpp.interworking-data":{"source":"iana"},"application/vnd.3gpp.lpp":{"source":"iana"},"application/vnd.3gpp.mc-signalling-ear":{"source":"iana"},"application/vnd.3gpp.mcdata-affiliation-command+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcdata-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcdata-payload":{"source":"iana"},"application/vnd.3gpp.mcdata-service-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcdata-signalling":{"source":"iana"},"application/vnd.3gpp.mcdata-ue-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcdata-user-profile+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-affiliation-command+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-floor-request+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-location-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-mbms-usage-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-service-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-signed+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-ue-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-ue-init-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-user-profile+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-affiliation-command+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-affiliation-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-location-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-mbms-usage-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-service-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-transmission-request+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-ue-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-user-profile+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mid-call+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.ngap":{"source":"iana"},"application/vnd.3gpp.pfcp":{"source":"iana"},"application/vnd.3gpp.pic-bw-large":{"source":"iana","extensions":["plb"]},"application/vnd.3gpp.pic-bw-small":{"source":"iana","extensions":["psb"]},"application/vnd.3gpp.pic-bw-var":{"source":"iana","extensions":["pvb"]},"application/vnd.3gpp.s1ap":{"source":"iana"},"application/vnd.3gpp.sms":{"source":"iana"},"application/vnd.3gpp.sms+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.srvcc-ext+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.srvcc-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.state-and-event-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.ussd+xml":{"source":"iana","compressible":true},"application/vnd.3gpp2.bcmcsinfo+xml":{"source":"iana","compressible":true},"application/vnd.3gpp2.sms":{"source":"iana"},"application/vnd.3gpp2.tcap":{"source":"iana","extensions":["tcap"]},"application/vnd.3lightssoftware.imagescal":{"source":"iana"},"application/vnd.3m.post-it-notes":{"source":"iana","extensions":["pwn"]},"application/vnd.accpac.simply.aso":{"source":"iana","extensions":["aso"]},"application/vnd.accpac.simply.imp":{"source":"iana","extensions":["imp"]},"application/vnd.acucobol":{"source":"iana","extensions":["acu"]},"application/vnd.acucorp":{"source":"iana","extensions":["atc","acutc"]},"application/vnd.adobe.air-application-installer-package+zip":{"source":"apache","compressible":false,"extensions":["air"]},"application/vnd.adobe.flash.movie":{"source":"iana"},"application/vnd.adobe.formscentral.fcdt":{"source":"iana","extensions":["fcdt"]},"application/vnd.adobe.fxp":{"source":"iana","extensions":["fxp","fxpl"]},"application/vnd.adobe.partial-upload":{"source":"iana"},"application/vnd.adobe.xdp+xml":{"source":"iana","compressible":true,"extensions":["xdp"]},"application/vnd.adobe.xfdf":{"source":"iana","extensions":["xfdf"]},"application/vnd.aether.imp":{"source":"iana"},"application/vnd.afpc.afplinedata":{"source":"iana"},"application/vnd.afpc.afplinedata-pagedef":{"source":"iana"},"application/vnd.afpc.cmoca-cmresource":{"source":"iana"},"application/vnd.afpc.foca-charset":{"source":"iana"},"application/vnd.afpc.foca-codedfont":{"source":"iana"},"application/vnd.afpc.foca-codepage":{"source":"iana"},"application/vnd.afpc.modca":{"source":"iana"},"application/vnd.afpc.modca-cmtable":{"source":"iana"},"application/vnd.afpc.modca-formdef":{"source":"iana"},"application/vnd.afpc.modca-mediummap":{"source":"iana"},"application/vnd.afpc.modca-objectcontainer":{"source":"iana"},"application/vnd.afpc.modca-overlay":{"source":"iana"},"application/vnd.afpc.modca-pagesegment":{"source":"iana"},"application/vnd.age":{"source":"iana","extensions":["age"]},"application/vnd.ah-barcode":{"source":"iana"},"application/vnd.ahead.space":{"source":"iana","extensions":["ahead"]},"application/vnd.airzip.filesecure.azf":{"source":"iana","extensions":["azf"]},"application/vnd.airzip.filesecure.azs":{"source":"iana","extensions":["azs"]},"application/vnd.amadeus+json":{"source":"iana","compressible":true},"application/vnd.amazon.ebook":{"source":"apache","extensions":["azw"]},"application/vnd.amazon.mobi8-ebook":{"source":"iana"},"application/vnd.americandynamics.acc":{"source":"iana","extensions":["acc"]},"application/vnd.amiga.ami":{"source":"iana","extensions":["ami"]},"application/vnd.amundsen.maze+xml":{"source":"iana","compressible":true},"application/vnd.android.ota":{"source":"iana"},"application/vnd.android.package-archive":{"source":"apache","compressible":false,"extensions":["apk"]},"application/vnd.anki":{"source":"iana"},"application/vnd.anser-web-certificate-issue-initiation":{"source":"iana","extensions":["cii"]},"application/vnd.anser-web-funds-transfer-initiation":{"source":"apache","extensions":["fti"]},"application/vnd.antix.game-component":{"source":"iana","extensions":["atx"]},"application/vnd.apache.arrow.file":{"source":"iana"},"application/vnd.apache.arrow.stream":{"source":"iana"},"application/vnd.apache.thrift.binary":{"source":"iana"},"application/vnd.apache.thrift.compact":{"source":"iana"},"application/vnd.apache.thrift.json":{"source":"iana"},"application/vnd.api+json":{"source":"iana","compressible":true},"application/vnd.aplextor.warrp+json":{"source":"iana","compressible":true},"application/vnd.apothekende.reservation+json":{"source":"iana","compressible":true},"application/vnd.apple.installer+xml":{"source":"iana","compressible":true,"extensions":["mpkg"]},"application/vnd.apple.keynote":{"source":"iana","extensions":["key"]},"application/vnd.apple.mpegurl":{"source":"iana","extensions":["m3u8"]},"application/vnd.apple.numbers":{"source":"iana","extensions":["numbers"]},"application/vnd.apple.pages":{"source":"iana","extensions":["pages"]},"application/vnd.apple.pkpass":{"compressible":false,"extensions":["pkpass"]},"application/vnd.arastra.swi":{"source":"iana"},"application/vnd.aristanetworks.swi":{"source":"iana","extensions":["swi"]},"application/vnd.artisan+json":{"source":"iana","compressible":true},"application/vnd.artsquare":{"source":"iana"},"application/vnd.astraea-software.iota":{"source":"iana","extensions":["iota"]},"application/vnd.audiograph":{"source":"iana","extensions":["aep"]},"application/vnd.autopackage":{"source":"iana"},"application/vnd.avalon+json":{"source":"iana","compressible":true},"application/vnd.avistar+xml":{"source":"iana","compressible":true},"application/vnd.balsamiq.bmml+xml":{"source":"iana","compressible":true,"extensions":["bmml"]},"application/vnd.balsamiq.bmpr":{"source":"iana"},"application/vnd.banana-accounting":{"source":"iana"},"application/vnd.bbf.usp.error":{"source":"iana"},"application/vnd.bbf.usp.msg":{"source":"iana"},"application/vnd.bbf.usp.msg+json":{"source":"iana","compressible":true},"application/vnd.bekitzur-stech+json":{"source":"iana","compressible":true},"application/vnd.bint.med-content":{"source":"iana"},"application/vnd.biopax.rdf+xml":{"source":"iana","compressible":true},"application/vnd.blink-idb-value-wrapper":{"source":"iana"},"application/vnd.blueice.multipass":{"source":"iana","extensions":["mpm"]},"application/vnd.bluetooth.ep.oob":{"source":"iana"},"application/vnd.bluetooth.le.oob":{"source":"iana"},"application/vnd.bmi":{"source":"iana","extensions":["bmi"]},"application/vnd.bpf":{"source":"iana"},"application/vnd.bpf3":{"source":"iana"},"application/vnd.businessobjects":{"source":"iana","extensions":["rep"]},"application/vnd.byu.uapi+json":{"source":"iana","compressible":true},"application/vnd.cab-jscript":{"source":"iana"},"application/vnd.canon-cpdl":{"source":"iana"},"application/vnd.canon-lips":{"source":"iana"},"application/vnd.capasystems-pg+json":{"source":"iana","compressible":true},"application/vnd.cendio.thinlinc.clientconf":{"source":"iana"},"application/vnd.century-systems.tcp_stream":{"source":"iana"},"application/vnd.chemdraw+xml":{"source":"iana","compressible":true,"extensions":["cdxml"]},"application/vnd.chess-pgn":{"source":"iana"},"application/vnd.chipnuts.karaoke-mmd":{"source":"iana","extensions":["mmd"]},"application/vnd.ciedi":{"source":"iana"},"application/vnd.cinderella":{"source":"iana","extensions":["cdy"]},"application/vnd.cirpack.isdn-ext":{"source":"iana"},"application/vnd.citationstyles.style+xml":{"source":"iana","compressible":true,"extensions":["csl"]},"application/vnd.claymore":{"source":"iana","extensions":["cla"]},"application/vnd.cloanto.rp9":{"source":"iana","extensions":["rp9"]},"application/vnd.clonk.c4group":{"source":"iana","extensions":["c4g","c4d","c4f","c4p","c4u"]},"application/vnd.cluetrust.cartomobile-config":{"source":"iana","extensions":["c11amc"]},"application/vnd.cluetrust.cartomobile-config-pkg":{"source":"iana","extensions":["c11amz"]},"application/vnd.coffeescript":{"source":"iana"},"application/vnd.collabio.xodocuments.document":{"source":"iana"},"application/vnd.collabio.xodocuments.document-template":{"source":"iana"},"application/vnd.collabio.xodocuments.presentation":{"source":"iana"},"application/vnd.collabio.xodocuments.presentation-template":{"source":"iana"},"application/vnd.collabio.xodocuments.spreadsheet":{"source":"iana"},"application/vnd.collabio.xodocuments.spreadsheet-template":{"source":"iana"},"application/vnd.collection+json":{"source":"iana","compressible":true},"application/vnd.collection.doc+json":{"source":"iana","compressible":true},"application/vnd.collection.next+json":{"source":"iana","compressible":true},"application/vnd.comicbook+zip":{"source":"iana","compressible":false},"application/vnd.comicbook-rar":{"source":"iana"},"application/vnd.commerce-battelle":{"source":"iana"},"application/vnd.commonspace":{"source":"iana","extensions":["csp"]},"application/vnd.contact.cmsg":{"source":"iana","extensions":["cdbcmsg"]},"application/vnd.coreos.ignition+json":{"source":"iana","compressible":true},"application/vnd.cosmocaller":{"source":"iana","extensions":["cmc"]},"application/vnd.crick.clicker":{"source":"iana","extensions":["clkx"]},"application/vnd.crick.clicker.keyboard":{"source":"iana","extensions":["clkk"]},"application/vnd.crick.clicker.palette":{"source":"iana","extensions":["clkp"]},"application/vnd.crick.clicker.template":{"source":"iana","extensions":["clkt"]},"application/vnd.crick.clicker.wordbank":{"source":"iana","extensions":["clkw"]},"application/vnd.criticaltools.wbs+xml":{"source":"iana","compressible":true,"extensions":["wbs"]},"application/vnd.cryptii.pipe+json":{"source":"iana","compressible":true},"application/vnd.crypto-shade-file":{"source":"iana"},"application/vnd.cryptomator.encrypted":{"source":"iana"},"application/vnd.cryptomator.vault":{"source":"iana"},"application/vnd.ctc-posml":{"source":"iana","extensions":["pml"]},"application/vnd.ctct.ws+xml":{"source":"iana","compressible":true},"application/vnd.cups-pdf":{"source":"iana"},"application/vnd.cups-postscript":{"source":"iana"},"application/vnd.cups-ppd":{"source":"iana","extensions":["ppd"]},"application/vnd.cups-raster":{"source":"iana"},"application/vnd.cups-raw":{"source":"iana"},"application/vnd.curl":{"source":"iana"},"application/vnd.curl.car":{"source":"apache","extensions":["car"]},"application/vnd.curl.pcurl":{"source":"apache","extensions":["pcurl"]},"application/vnd.cyan.dean.root+xml":{"source":"iana","compressible":true},"application/vnd.cybank":{"source":"iana"},"application/vnd.cyclonedx+json":{"source":"iana","compressible":true},"application/vnd.cyclonedx+xml":{"source":"iana","compressible":true},"application/vnd.d2l.coursepackage1p0+zip":{"source":"iana","compressible":false},"application/vnd.d3m-dataset":{"source":"iana"},"application/vnd.d3m-problem":{"source":"iana"},"application/vnd.dart":{"source":"iana","compressible":true,"extensions":["dart"]},"application/vnd.data-vision.rdz":{"source":"iana","extensions":["rdz"]},"application/vnd.datapackage+json":{"source":"iana","compressible":true},"application/vnd.dataresource+json":{"source":"iana","compressible":true},"application/vnd.dbf":{"source":"iana","extensions":["dbf"]},"application/vnd.debian.binary-package":{"source":"iana"},"application/vnd.dece.data":{"source":"iana","extensions":["uvf","uvvf","uvd","uvvd"]},"application/vnd.dece.ttml+xml":{"source":"iana","compressible":true,"extensions":["uvt","uvvt"]},"application/vnd.dece.unspecified":{"source":"iana","extensions":["uvx","uvvx"]},"application/vnd.dece.zip":{"source":"iana","extensions":["uvz","uvvz"]},"application/vnd.denovo.fcselayout-link":{"source":"iana","extensions":["fe_launch"]},"application/vnd.desmume.movie":{"source":"iana"},"application/vnd.dir-bi.plate-dl-nosuffix":{"source":"iana"},"application/vnd.dm.delegation+xml":{"source":"iana","compressible":true},"application/vnd.dna":{"source":"iana","extensions":["dna"]},"application/vnd.document+json":{"source":"iana","compressible":true},"application/vnd.dolby.mlp":{"source":"apache","extensions":["mlp"]},"application/vnd.dolby.mobile.1":{"source":"iana"},"application/vnd.dolby.mobile.2":{"source":"iana"},"application/vnd.doremir.scorecloud-binary-document":{"source":"iana"},"application/vnd.dpgraph":{"source":"iana","extensions":["dpg"]},"application/vnd.dreamfactory":{"source":"iana","extensions":["dfac"]},"application/vnd.drive+json":{"source":"iana","compressible":true},"application/vnd.ds-keypoint":{"source":"apache","extensions":["kpxx"]},"application/vnd.dtg.local":{"source":"iana"},"application/vnd.dtg.local.flash":{"source":"iana"},"application/vnd.dtg.local.html":{"source":"iana"},"application/vnd.dvb.ait":{"source":"iana","extensions":["ait"]},"application/vnd.dvb.dvbisl+xml":{"source":"iana","compressible":true},"application/vnd.dvb.dvbj":{"source":"iana"},"application/vnd.dvb.esgcontainer":{"source":"iana"},"application/vnd.dvb.ipdcdftnotifaccess":{"source":"iana"},"application/vnd.dvb.ipdcesgaccess":{"source":"iana"},"application/vnd.dvb.ipdcesgaccess2":{"source":"iana"},"application/vnd.dvb.ipdcesgpdd":{"source":"iana"},"application/vnd.dvb.ipdcroaming":{"source":"iana"},"application/vnd.dvb.iptv.alfec-base":{"source":"iana"},"application/vnd.dvb.iptv.alfec-enhancement":{"source":"iana"},"application/vnd.dvb.notif-aggregate-root+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-container+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-generic+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-ia-msglist+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-ia-registration-request+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-ia-registration-response+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-init+xml":{"source":"iana","compressible":true},"application/vnd.dvb.pfr":{"source":"iana"},"application/vnd.dvb.service":{"source":"iana","extensions":["svc"]},"application/vnd.dxr":{"source":"iana"},"application/vnd.dynageo":{"source":"iana","extensions":["geo"]},"application/vnd.dzr":{"source":"iana"},"application/vnd.easykaraoke.cdgdownload":{"source":"iana"},"application/vnd.ecdis-update":{"source":"iana"},"application/vnd.ecip.rlp":{"source":"iana"},"application/vnd.eclipse.ditto+json":{"source":"iana","compressible":true},"application/vnd.ecowin.chart":{"source":"iana","extensions":["mag"]},"application/vnd.ecowin.filerequest":{"source":"iana"},"application/vnd.ecowin.fileupdate":{"source":"iana"},"application/vnd.ecowin.series":{"source":"iana"},"application/vnd.ecowin.seriesrequest":{"source":"iana"},"application/vnd.ecowin.seriesupdate":{"source":"iana"},"application/vnd.efi.img":{"source":"iana"},"application/vnd.efi.iso":{"source":"iana"},"application/vnd.emclient.accessrequest+xml":{"source":"iana","compressible":true},"application/vnd.enliven":{"source":"iana","extensions":["nml"]},"application/vnd.enphase.envoy":{"source":"iana"},"application/vnd.eprints.data+xml":{"source":"iana","compressible":true},"application/vnd.epson.esf":{"source":"iana","extensions":["esf"]},"application/vnd.epson.msf":{"source":"iana","extensions":["msf"]},"application/vnd.epson.quickanime":{"source":"iana","extensions":["qam"]},"application/vnd.epson.salt":{"source":"iana","extensions":["slt"]},"application/vnd.epson.ssf":{"source":"iana","extensions":["ssf"]},"application/vnd.ericsson.quickcall":{"source":"iana"},"application/vnd.espass-espass+zip":{"source":"iana","compressible":false},"application/vnd.eszigno3+xml":{"source":"iana","compressible":true,"extensions":["es3","et3"]},"application/vnd.etsi.aoc+xml":{"source":"iana","compressible":true},"application/vnd.etsi.asic-e+zip":{"source":"iana","compressible":false},"application/vnd.etsi.asic-s+zip":{"source":"iana","compressible":false},"application/vnd.etsi.cug+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvcommand+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvdiscovery+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvprofile+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvsad-bc+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvsad-cod+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvsad-npvr+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvservice+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvsync+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvueprofile+xml":{"source":"iana","compressible":true},"application/vnd.etsi.mcid+xml":{"source":"iana","compressible":true},"application/vnd.etsi.mheg5":{"source":"iana"},"application/vnd.etsi.overload-control-policy-dataset+xml":{"source":"iana","compressible":true},"application/vnd.etsi.pstn+xml":{"source":"iana","compressible":true},"application/vnd.etsi.sci+xml":{"source":"iana","compressible":true},"application/vnd.etsi.simservs+xml":{"source":"iana","compressible":true},"application/vnd.etsi.timestamp-token":{"source":"iana"},"application/vnd.etsi.tsl+xml":{"source":"iana","compressible":true},"application/vnd.etsi.tsl.der":{"source":"iana"},"application/vnd.eu.kasparian.car+json":{"source":"iana","compressible":true},"application/vnd.eudora.data":{"source":"iana"},"application/vnd.evolv.ecig.profile":{"source":"iana"},"application/vnd.evolv.ecig.settings":{"source":"iana"},"application/vnd.evolv.ecig.theme":{"source":"iana"},"application/vnd.exstream-empower+zip":{"source":"iana","compressible":false},"application/vnd.exstream-package":{"source":"iana"},"application/vnd.ezpix-album":{"source":"iana","extensions":["ez2"]},"application/vnd.ezpix-package":{"source":"iana","extensions":["ez3"]},"application/vnd.f-secure.mobile":{"source":"iana"},"application/vnd.familysearch.gedcom+zip":{"source":"iana","compressible":false},"application/vnd.fastcopy-disk-image":{"source":"iana"},"application/vnd.fdf":{"source":"iana","extensions":["fdf"]},"application/vnd.fdsn.mseed":{"source":"iana","extensions":["mseed"]},"application/vnd.fdsn.seed":{"source":"iana","extensions":["seed","dataless"]},"application/vnd.ffsns":{"source":"iana"},"application/vnd.ficlab.flb+zip":{"source":"iana","compressible":false},"application/vnd.filmit.zfc":{"source":"iana"},"application/vnd.fints":{"source":"iana"},"application/vnd.firemonkeys.cloudcell":{"source":"iana"},"application/vnd.flographit":{"source":"iana","extensions":["gph"]},"application/vnd.fluxtime.clip":{"source":"iana","extensions":["ftc"]},"application/vnd.font-fontforge-sfd":{"source":"iana"},"application/vnd.framemaker":{"source":"iana","extensions":["fm","frame","maker","book"]},"application/vnd.frogans.fnc":{"source":"iana","extensions":["fnc"]},"application/vnd.frogans.ltf":{"source":"iana","extensions":["ltf"]},"application/vnd.fsc.weblaunch":{"source":"iana","extensions":["fsc"]},"application/vnd.fujifilm.fb.docuworks":{"source":"iana"},"application/vnd.fujifilm.fb.docuworks.binder":{"source":"iana"},"application/vnd.fujifilm.fb.docuworks.container":{"source":"iana"},"application/vnd.fujifilm.fb.jfi+xml":{"source":"iana","compressible":true},"application/vnd.fujitsu.oasys":{"source":"iana","extensions":["oas"]},"application/vnd.fujitsu.oasys2":{"source":"iana","extensions":["oa2"]},"application/vnd.fujitsu.oasys3":{"source":"iana","extensions":["oa3"]},"application/vnd.fujitsu.oasysgp":{"source":"iana","extensions":["fg5"]},"application/vnd.fujitsu.oasysprs":{"source":"iana","extensions":["bh2"]},"application/vnd.fujixerox.art-ex":{"source":"iana"},"application/vnd.fujixerox.art4":{"source":"iana"},"application/vnd.fujixerox.ddd":{"source":"iana","extensions":["ddd"]},"application/vnd.fujixerox.docuworks":{"source":"iana","extensions":["xdw"]},"application/vnd.fujixerox.docuworks.binder":{"source":"iana","extensions":["xbd"]},"application/vnd.fujixerox.docuworks.container":{"source":"iana"},"application/vnd.fujixerox.hbpl":{"source":"iana"},"application/vnd.fut-misnet":{"source":"iana"},"application/vnd.futoin+cbor":{"source":"iana"},"application/vnd.futoin+json":{"source":"iana","compressible":true},"application/vnd.fuzzysheet":{"source":"iana","extensions":["fzs"]},"application/vnd.genomatix.tuxedo":{"source":"iana","extensions":["txd"]},"application/vnd.gentics.grd+json":{"source":"iana","compressible":true},"application/vnd.geo+json":{"source":"iana","compressible":true},"application/vnd.geocube+xml":{"source":"iana","compressible":true},"application/vnd.geogebra.file":{"source":"iana","extensions":["ggb"]},"application/vnd.geogebra.slides":{"source":"iana"},"application/vnd.geogebra.tool":{"source":"iana","extensions":["ggt"]},"application/vnd.geometry-explorer":{"source":"iana","extensions":["gex","gre"]},"application/vnd.geonext":{"source":"iana","extensions":["gxt"]},"application/vnd.geoplan":{"source":"iana","extensions":["g2w"]},"application/vnd.geospace":{"source":"iana","extensions":["g3w"]},"application/vnd.gerber":{"source":"iana"},"application/vnd.globalplatform.card-content-mgt":{"source":"iana"},"application/vnd.globalplatform.card-content-mgt-response":{"source":"iana"},"application/vnd.gmx":{"source":"iana","extensions":["gmx"]},"application/vnd.google-apps.document":{"compressible":false,"extensions":["gdoc"]},"application/vnd.google-apps.presentation":{"compressible":false,"extensions":["gslides"]},"application/vnd.google-apps.spreadsheet":{"compressible":false,"extensions":["gsheet"]},"application/vnd.google-earth.kml+xml":{"source":"iana","compressible":true,"extensions":["kml"]},"application/vnd.google-earth.kmz":{"source":"iana","compressible":false,"extensions":["kmz"]},"application/vnd.gov.sk.e-form+xml":{"source":"iana","compressible":true},"application/vnd.gov.sk.e-form+zip":{"source":"iana","compressible":false},"application/vnd.gov.sk.xmldatacontainer+xml":{"source":"iana","compressible":true},"application/vnd.grafeq":{"source":"iana","extensions":["gqf","gqs"]},"application/vnd.gridmp":{"source":"iana"},"application/vnd.groove-account":{"source":"iana","extensions":["gac"]},"application/vnd.groove-help":{"source":"iana","extensions":["ghf"]},"application/vnd.groove-identity-message":{"source":"iana","extensions":["gim"]},"application/vnd.groove-injector":{"source":"iana","extensions":["grv"]},"application/vnd.groove-tool-message":{"source":"iana","extensions":["gtm"]},"application/vnd.groove-tool-template":{"source":"iana","extensions":["tpl"]},"application/vnd.groove-vcard":{"source":"iana","extensions":["vcg"]},"application/vnd.hal+json":{"source":"iana","compressible":true},"application/vnd.hal+xml":{"source":"iana","compressible":true,"extensions":["hal"]},"application/vnd.handheld-entertainment+xml":{"source":"iana","compressible":true,"extensions":["zmm"]},"application/vnd.hbci":{"source":"iana","extensions":["hbci"]},"application/vnd.hc+json":{"source":"iana","compressible":true},"application/vnd.hcl-bireports":{"source":"iana"},"application/vnd.hdt":{"source":"iana"},"application/vnd.heroku+json":{"source":"iana","compressible":true},"application/vnd.hhe.lesson-player":{"source":"iana","extensions":["les"]},"application/vnd.hl7cda+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.hl7v2+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.hp-hpgl":{"source":"iana","extensions":["hpgl"]},"application/vnd.hp-hpid":{"source":"iana","extensions":["hpid"]},"application/vnd.hp-hps":{"source":"iana","extensions":["hps"]},"application/vnd.hp-jlyt":{"source":"iana","extensions":["jlt"]},"application/vnd.hp-pcl":{"source":"iana","extensions":["pcl"]},"application/vnd.hp-pclxl":{"source":"iana","extensions":["pclxl"]},"application/vnd.httphone":{"source":"iana"},"application/vnd.hydrostatix.sof-data":{"source":"iana","extensions":["sfd-hdstx"]},"application/vnd.hyper+json":{"source":"iana","compressible":true},"application/vnd.hyper-item+json":{"source":"iana","compressible":true},"application/vnd.hyperdrive+json":{"source":"iana","compressible":true},"application/vnd.hzn-3d-crossword":{"source":"iana"},"application/vnd.ibm.afplinedata":{"source":"iana"},"application/vnd.ibm.electronic-media":{"source":"iana"},"application/vnd.ibm.minipay":{"source":"iana","extensions":["mpy"]},"application/vnd.ibm.modcap":{"source":"iana","extensions":["afp","listafp","list3820"]},"application/vnd.ibm.rights-management":{"source":"iana","extensions":["irm"]},"application/vnd.ibm.secure-container":{"source":"iana","extensions":["sc"]},"application/vnd.iccprofile":{"source":"iana","extensions":["icc","icm"]},"application/vnd.ieee.1905":{"source":"iana"},"application/vnd.igloader":{"source":"iana","extensions":["igl"]},"application/vnd.imagemeter.folder+zip":{"source":"iana","compressible":false},"application/vnd.imagemeter.image+zip":{"source":"iana","compressible":false},"application/vnd.immervision-ivp":{"source":"iana","extensions":["ivp"]},"application/vnd.immervision-ivu":{"source":"iana","extensions":["ivu"]},"application/vnd.ims.imsccv1p1":{"source":"iana"},"application/vnd.ims.imsccv1p2":{"source":"iana"},"application/vnd.ims.imsccv1p3":{"source":"iana"},"application/vnd.ims.lis.v2.result+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolconsumerprofile+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolproxy+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolproxy.id+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolsettings+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolsettings.simple+json":{"source":"iana","compressible":true},"application/vnd.informedcontrol.rms+xml":{"source":"iana","compressible":true},"application/vnd.informix-visionary":{"source":"iana"},"application/vnd.infotech.project":{"source":"iana"},"application/vnd.infotech.project+xml":{"source":"iana","compressible":true},"application/vnd.innopath.wamp.notification":{"source":"iana"},"application/vnd.insors.igm":{"source":"iana","extensions":["igm"]},"application/vnd.intercon.formnet":{"source":"iana","extensions":["xpw","xpx"]},"application/vnd.intergeo":{"source":"iana","extensions":["i2g"]},"application/vnd.intertrust.digibox":{"source":"iana"},"application/vnd.intertrust.nncp":{"source":"iana"},"application/vnd.intu.qbo":{"source":"iana","extensions":["qbo"]},"application/vnd.intu.qfx":{"source":"iana","extensions":["qfx"]},"application/vnd.iptc.g2.catalogitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.conceptitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.knowledgeitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.newsitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.newsmessage+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.packageitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.planningitem+xml":{"source":"iana","compressible":true},"application/vnd.ipunplugged.rcprofile":{"source":"iana","extensions":["rcprofile"]},"application/vnd.irepository.package+xml":{"source":"iana","compressible":true,"extensions":["irp"]},"application/vnd.is-xpr":{"source":"iana","extensions":["xpr"]},"application/vnd.isac.fcs":{"source":"iana","extensions":["fcs"]},"application/vnd.iso11783-10+zip":{"source":"iana","compressible":false},"application/vnd.jam":{"source":"iana","extensions":["jam"]},"application/vnd.japannet-directory-service":{"source":"iana"},"application/vnd.japannet-jpnstore-wakeup":{"source":"iana"},"application/vnd.japannet-payment-wakeup":{"source":"iana"},"application/vnd.japannet-registration":{"source":"iana"},"application/vnd.japannet-registration-wakeup":{"source":"iana"},"application/vnd.japannet-setstore-wakeup":{"source":"iana"},"application/vnd.japannet-verification":{"source":"iana"},"application/vnd.japannet-verification-wakeup":{"source":"iana"},"application/vnd.jcp.javame.midlet-rms":{"source":"iana","extensions":["rms"]},"application/vnd.jisp":{"source":"iana","extensions":["jisp"]},"application/vnd.joost.joda-archive":{"source":"iana","extensions":["joda"]},"application/vnd.jsk.isdn-ngn":{"source":"iana"},"application/vnd.kahootz":{"source":"iana","extensions":["ktz","ktr"]},"application/vnd.kde.karbon":{"source":"iana","extensions":["karbon"]},"application/vnd.kde.kchart":{"source":"iana","extensions":["chrt"]},"application/vnd.kde.kformula":{"source":"iana","extensions":["kfo"]},"application/vnd.kde.kivio":{"source":"iana","extensions":["flw"]},"application/vnd.kde.kontour":{"source":"iana","extensions":["kon"]},"application/vnd.kde.kpresenter":{"source":"iana","extensions":["kpr","kpt"]},"application/vnd.kde.kspread":{"source":"iana","extensions":["ksp"]},"application/vnd.kde.kword":{"source":"iana","extensions":["kwd","kwt"]},"application/vnd.kenameaapp":{"source":"iana","extensions":["htke"]},"application/vnd.kidspiration":{"source":"iana","extensions":["kia"]},"application/vnd.kinar":{"source":"iana","extensions":["kne","knp"]},"application/vnd.koan":{"source":"iana","extensions":["skp","skd","skt","skm"]},"application/vnd.kodak-descriptor":{"source":"iana","extensions":["sse"]},"application/vnd.las":{"source":"iana"},"application/vnd.las.las+json":{"source":"iana","compressible":true},"application/vnd.las.las+xml":{"source":"iana","compressible":true,"extensions":["lasxml"]},"application/vnd.laszip":{"source":"iana"},"application/vnd.leap+json":{"source":"iana","compressible":true},"application/vnd.liberty-request+xml":{"source":"iana","compressible":true},"application/vnd.llamagraphics.life-balance.desktop":{"source":"iana","extensions":["lbd"]},"application/vnd.llamagraphics.life-balance.exchange+xml":{"source":"iana","compressible":true,"extensions":["lbe"]},"application/vnd.logipipe.circuit+zip":{"source":"iana","compressible":false},"application/vnd.loom":{"source":"iana"},"application/vnd.lotus-1-2-3":{"source":"iana","extensions":["123"]},"application/vnd.lotus-approach":{"source":"iana","extensions":["apr"]},"application/vnd.lotus-freelance":{"source":"iana","extensions":["pre"]},"application/vnd.lotus-notes":{"source":"iana","extensions":["nsf"]},"application/vnd.lotus-organizer":{"source":"iana","extensions":["org"]},"application/vnd.lotus-screencam":{"source":"iana","extensions":["scm"]},"application/vnd.lotus-wordpro":{"source":"iana","extensions":["lwp"]},"application/vnd.macports.portpkg":{"source":"iana","extensions":["portpkg"]},"application/vnd.mapbox-vector-tile":{"source":"iana","extensions":["mvt"]},"application/vnd.marlin.drm.actiontoken+xml":{"source":"iana","compressible":true},"application/vnd.marlin.drm.conftoken+xml":{"source":"iana","compressible":true},"application/vnd.marlin.drm.license+xml":{"source":"iana","compressible":true},"application/vnd.marlin.drm.mdcf":{"source":"iana"},"application/vnd.mason+json":{"source":"iana","compressible":true},"application/vnd.maxar.archive.3tz+zip":{"source":"iana","compressible":false},"application/vnd.maxmind.maxmind-db":{"source":"iana"},"application/vnd.mcd":{"source":"iana","extensions":["mcd"]},"application/vnd.medcalcdata":{"source":"iana","extensions":["mc1"]},"application/vnd.mediastation.cdkey":{"source":"iana","extensions":["cdkey"]},"application/vnd.meridian-slingshot":{"source":"iana"},"application/vnd.mfer":{"source":"iana","extensions":["mwf"]},"application/vnd.mfmp":{"source":"iana","extensions":["mfm"]},"application/vnd.micro+json":{"source":"iana","compressible":true},"application/vnd.micrografx.flo":{"source":"iana","extensions":["flo"]},"application/vnd.micrografx.igx":{"source":"iana","extensions":["igx"]},"application/vnd.microsoft.portable-executable":{"source":"iana"},"application/vnd.microsoft.windows.thumbnail-cache":{"source":"iana"},"application/vnd.miele+json":{"source":"iana","compressible":true},"application/vnd.mif":{"source":"iana","extensions":["mif"]},"application/vnd.minisoft-hp3000-save":{"source":"iana"},"application/vnd.mitsubishi.misty-guard.trustweb":{"source":"iana"},"application/vnd.mobius.daf":{"source":"iana","extensions":["daf"]},"application/vnd.mobius.dis":{"source":"iana","extensions":["dis"]},"application/vnd.mobius.mbk":{"source":"iana","extensions":["mbk"]},"application/vnd.mobius.mqy":{"source":"iana","extensions":["mqy"]},"application/vnd.mobius.msl":{"source":"iana","extensions":["msl"]},"application/vnd.mobius.plc":{"source":"iana","extensions":["plc"]},"application/vnd.mobius.txf":{"source":"iana","extensions":["txf"]},"application/vnd.mophun.application":{"source":"iana","extensions":["mpn"]},"application/vnd.mophun.certificate":{"source":"iana","extensions":["mpc"]},"application/vnd.motorola.flexsuite":{"source":"iana"},"application/vnd.motorola.flexsuite.adsi":{"source":"iana"},"application/vnd.motorola.flexsuite.fis":{"source":"iana"},"application/vnd.motorola.flexsuite.gotap":{"source":"iana"},"application/vnd.motorola.flexsuite.kmr":{"source":"iana"},"application/vnd.motorola.flexsuite.ttc":{"source":"iana"},"application/vnd.motorola.flexsuite.wem":{"source":"iana"},"application/vnd.motorola.iprm":{"source":"iana"},"application/vnd.mozilla.xul+xml":{"source":"iana","compressible":true,"extensions":["xul"]},"application/vnd.ms-3mfdocument":{"source":"iana"},"application/vnd.ms-artgalry":{"source":"iana","extensions":["cil"]},"application/vnd.ms-asf":{"source":"iana"},"application/vnd.ms-cab-compressed":{"source":"iana","extensions":["cab"]},"application/vnd.ms-color.iccprofile":{"source":"apache"},"application/vnd.ms-excel":{"source":"iana","compressible":false,"extensions":["xls","xlm","xla","xlc","xlt","xlw"]},"application/vnd.ms-excel.addin.macroenabled.12":{"source":"iana","extensions":["xlam"]},"application/vnd.ms-excel.sheet.binary.macroenabled.12":{"source":"iana","extensions":["xlsb"]},"application/vnd.ms-excel.sheet.macroenabled.12":{"source":"iana","extensions":["xlsm"]},"application/vnd.ms-excel.template.macroenabled.12":{"source":"iana","extensions":["xltm"]},"application/vnd.ms-fontobject":{"source":"iana","compressible":true,"extensions":["eot"]},"application/vnd.ms-htmlhelp":{"source":"iana","extensions":["chm"]},"application/vnd.ms-ims":{"source":"iana","extensions":["ims"]},"application/vnd.ms-lrm":{"source":"iana","extensions":["lrm"]},"application/vnd.ms-office.activex+xml":{"source":"iana","compressible":true},"application/vnd.ms-officetheme":{"source":"iana","extensions":["thmx"]},"application/vnd.ms-opentype":{"source":"apache","compressible":true},"application/vnd.ms-outlook":{"compressible":false,"extensions":["msg"]},"application/vnd.ms-package.obfuscated-opentype":{"source":"apache"},"application/vnd.ms-pki.seccat":{"source":"apache","extensions":["cat"]},"application/vnd.ms-pki.stl":{"source":"apache","extensions":["stl"]},"application/vnd.ms-playready.initiator+xml":{"source":"iana","compressible":true},"application/vnd.ms-powerpoint":{"source":"iana","compressible":false,"extensions":["ppt","pps","pot"]},"application/vnd.ms-powerpoint.addin.macroenabled.12":{"source":"iana","extensions":["ppam"]},"application/vnd.ms-powerpoint.presentation.macroenabled.12":{"source":"iana","extensions":["pptm"]},"application/vnd.ms-powerpoint.slide.macroenabled.12":{"source":"iana","extensions":["sldm"]},"application/vnd.ms-powerpoint.slideshow.macroenabled.12":{"source":"iana","extensions":["ppsm"]},"application/vnd.ms-powerpoint.template.macroenabled.12":{"source":"iana","extensions":["potm"]},"application/vnd.ms-printdevicecapabilities+xml":{"source":"iana","compressible":true},"application/vnd.ms-printing.printticket+xml":{"source":"apache","compressible":true},"application/vnd.ms-printschematicket+xml":{"source":"iana","compressible":true},"application/vnd.ms-project":{"source":"iana","extensions":["mpp","mpt"]},"application/vnd.ms-tnef":{"source":"iana"},"application/vnd.ms-windows.devicepairing":{"source":"iana"},"application/vnd.ms-windows.nwprinting.oob":{"source":"iana"},"application/vnd.ms-windows.printerpairing":{"source":"iana"},"application/vnd.ms-windows.wsd.oob":{"source":"iana"},"application/vnd.ms-wmdrm.lic-chlg-req":{"source":"iana"},"application/vnd.ms-wmdrm.lic-resp":{"source":"iana"},"application/vnd.ms-wmdrm.meter-chlg-req":{"source":"iana"},"application/vnd.ms-wmdrm.meter-resp":{"source":"iana"},"application/vnd.ms-word.document.macroenabled.12":{"source":"iana","extensions":["docm"]},"application/vnd.ms-word.template.macroenabled.12":{"source":"iana","extensions":["dotm"]},"application/vnd.ms-works":{"source":"iana","extensions":["wps","wks","wcm","wdb"]},"application/vnd.ms-wpl":{"source":"iana","extensions":["wpl"]},"application/vnd.ms-xpsdocument":{"source":"iana","compressible":false,"extensions":["xps"]},"application/vnd.msa-disk-image":{"source":"iana"},"application/vnd.mseq":{"source":"iana","extensions":["mseq"]},"application/vnd.msign":{"source":"iana"},"application/vnd.multiad.creator":{"source":"iana"},"application/vnd.multiad.creator.cif":{"source":"iana"},"application/vnd.music-niff":{"source":"iana"},"application/vnd.musician":{"source":"iana","extensions":["mus"]},"application/vnd.muvee.style":{"source":"iana","extensions":["msty"]},"application/vnd.mynfc":{"source":"iana","extensions":["taglet"]},"application/vnd.nacamar.ybrid+json":{"source":"iana","compressible":true},"application/vnd.ncd.control":{"source":"iana"},"application/vnd.ncd.reference":{"source":"iana"},"application/vnd.nearst.inv+json":{"source":"iana","compressible":true},"application/vnd.nebumind.line":{"source":"iana"},"application/vnd.nervana":{"source":"iana"},"application/vnd.netfpx":{"source":"iana"},"application/vnd.neurolanguage.nlu":{"source":"iana","extensions":["nlu"]},"application/vnd.nimn":{"source":"iana"},"application/vnd.nintendo.nitro.rom":{"source":"iana"},"application/vnd.nintendo.snes.rom":{"source":"iana"},"application/vnd.nitf":{"source":"iana","extensions":["ntf","nitf"]},"application/vnd.noblenet-directory":{"source":"iana","extensions":["nnd"]},"application/vnd.noblenet-sealer":{"source":"iana","extensions":["nns"]},"application/vnd.noblenet-web":{"source":"iana","extensions":["nnw"]},"application/vnd.nokia.catalogs":{"source":"iana"},"application/vnd.nokia.conml+wbxml":{"source":"iana"},"application/vnd.nokia.conml+xml":{"source":"iana","compressible":true},"application/vnd.nokia.iptv.config+xml":{"source":"iana","compressible":true},"application/vnd.nokia.isds-radio-presets":{"source":"iana"},"application/vnd.nokia.landmark+wbxml":{"source":"iana"},"application/vnd.nokia.landmark+xml":{"source":"iana","compressible":true},"application/vnd.nokia.landmarkcollection+xml":{"source":"iana","compressible":true},"application/vnd.nokia.n-gage.ac+xml":{"source":"iana","compressible":true,"extensions":["ac"]},"application/vnd.nokia.n-gage.data":{"source":"iana","extensions":["ngdat"]},"application/vnd.nokia.n-gage.symbian.install":{"source":"iana","extensions":["n-gage"]},"application/vnd.nokia.ncd":{"source":"iana"},"application/vnd.nokia.pcd+wbxml":{"source":"iana"},"application/vnd.nokia.pcd+xml":{"source":"iana","compressible":true},"application/vnd.nokia.radio-preset":{"source":"iana","extensions":["rpst"]},"application/vnd.nokia.radio-presets":{"source":"iana","extensions":["rpss"]},"application/vnd.novadigm.edm":{"source":"iana","extensions":["edm"]},"application/vnd.novadigm.edx":{"source":"iana","extensions":["edx"]},"application/vnd.novadigm.ext":{"source":"iana","extensions":["ext"]},"application/vnd.ntt-local.content-share":{"source":"iana"},"application/vnd.ntt-local.file-transfer":{"source":"iana"},"application/vnd.ntt-local.ogw_remote-access":{"source":"iana"},"application/vnd.ntt-local.sip-ta_remote":{"source":"iana"},"application/vnd.ntt-local.sip-ta_tcp_stream":{"source":"iana"},"application/vnd.oasis.opendocument.chart":{"source":"iana","extensions":["odc"]},"application/vnd.oasis.opendocument.chart-template":{"source":"iana","extensions":["otc"]},"application/vnd.oasis.opendocument.database":{"source":"iana","extensions":["odb"]},"application/vnd.oasis.opendocument.formula":{"source":"iana","extensions":["odf"]},"application/vnd.oasis.opendocument.formula-template":{"source":"iana","extensions":["odft"]},"application/vnd.oasis.opendocument.graphics":{"source":"iana","compressible":false,"extensions":["odg"]},"application/vnd.oasis.opendocument.graphics-template":{"source":"iana","extensions":["otg"]},"application/vnd.oasis.opendocument.image":{"source":"iana","extensions":["odi"]},"application/vnd.oasis.opendocument.image-template":{"source":"iana","extensions":["oti"]},"application/vnd.oasis.opendocument.presentation":{"source":"iana","compressible":false,"extensions":["odp"]},"application/vnd.oasis.opendocument.presentation-template":{"source":"iana","extensions":["otp"]},"application/vnd.oasis.opendocument.spreadsheet":{"source":"iana","compressible":false,"extensions":["ods"]},"application/vnd.oasis.opendocument.spreadsheet-template":{"source":"iana","extensions":["ots"]},"application/vnd.oasis.opendocument.text":{"source":"iana","compressible":false,"extensions":["odt"]},"application/vnd.oasis.opendocument.text-master":{"source":"iana","extensions":["odm"]},"application/vnd.oasis.opendocument.text-template":{"source":"iana","extensions":["ott"]},"application/vnd.oasis.opendocument.text-web":{"source":"iana","extensions":["oth"]},"application/vnd.obn":{"source":"iana"},"application/vnd.ocf+cbor":{"source":"iana"},"application/vnd.oci.image.manifest.v1+json":{"source":"iana","compressible":true},"application/vnd.oftn.l10n+json":{"source":"iana","compressible":true},"application/vnd.oipf.contentaccessdownload+xml":{"source":"iana","compressible":true},"application/vnd.oipf.contentaccessstreaming+xml":{"source":"iana","compressible":true},"application/vnd.oipf.cspg-hexbinary":{"source":"iana"},"application/vnd.oipf.dae.svg+xml":{"source":"iana","compressible":true},"application/vnd.oipf.dae.xhtml+xml":{"source":"iana","compressible":true},"application/vnd.oipf.mippvcontrolmessage+xml":{"source":"iana","compressible":true},"application/vnd.oipf.pae.gem":{"source":"iana"},"application/vnd.oipf.spdiscovery+xml":{"source":"iana","compressible":true},"application/vnd.oipf.spdlist+xml":{"source":"iana","compressible":true},"application/vnd.oipf.ueprofile+xml":{"source":"iana","compressible":true},"application/vnd.oipf.userprofile+xml":{"source":"iana","compressible":true},"application/vnd.olpc-sugar":{"source":"iana","extensions":["xo"]},"application/vnd.oma-scws-config":{"source":"iana"},"application/vnd.oma-scws-http-request":{"source":"iana"},"application/vnd.oma-scws-http-response":{"source":"iana"},"application/vnd.oma.bcast.associated-procedure-parameter+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.drm-trigger+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.imd+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.ltkm":{"source":"iana"},"application/vnd.oma.bcast.notification+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.provisioningtrigger":{"source":"iana"},"application/vnd.oma.bcast.sgboot":{"source":"iana"},"application/vnd.oma.bcast.sgdd+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.sgdu":{"source":"iana"},"application/vnd.oma.bcast.simple-symbol-container":{"source":"iana"},"application/vnd.oma.bcast.smartcard-trigger+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.sprov+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.stkm":{"source":"iana"},"application/vnd.oma.cab-address-book+xml":{"source":"iana","compressible":true},"application/vnd.oma.cab-feature-handler+xml":{"source":"iana","compressible":true},"application/vnd.oma.cab-pcc+xml":{"source":"iana","compressible":true},"application/vnd.oma.cab-subs-invite+xml":{"source":"iana","compressible":true},"application/vnd.oma.cab-user-prefs+xml":{"source":"iana","compressible":true},"application/vnd.oma.dcd":{"source":"iana"},"application/vnd.oma.dcdc":{"source":"iana"},"application/vnd.oma.dd2+xml":{"source":"iana","compressible":true,"extensions":["dd2"]},"application/vnd.oma.drm.risd+xml":{"source":"iana","compressible":true},"application/vnd.oma.group-usage-list+xml":{"source":"iana","compressible":true},"application/vnd.oma.lwm2m+cbor":{"source":"iana"},"application/vnd.oma.lwm2m+json":{"source":"iana","compressible":true},"application/vnd.oma.lwm2m+tlv":{"source":"iana"},"application/vnd.oma.pal+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.detailed-progress-report+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.final-report+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.groups+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.invocation-descriptor+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.optimized-progress-report+xml":{"source":"iana","compressible":true},"application/vnd.oma.push":{"source":"iana"},"application/vnd.oma.scidm.messages+xml":{"source":"iana","compressible":true},"application/vnd.oma.xcap-directory+xml":{"source":"iana","compressible":true},"application/vnd.omads-email+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.omads-file+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.omads-folder+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.omaloc-supl-init":{"source":"iana"},"application/vnd.onepager":{"source":"iana"},"application/vnd.onepagertamp":{"source":"iana"},"application/vnd.onepagertamx":{"source":"iana"},"application/vnd.onepagertat":{"source":"iana"},"application/vnd.onepagertatp":{"source":"iana"},"application/vnd.onepagertatx":{"source":"iana"},"application/vnd.openblox.game+xml":{"source":"iana","compressible":true,"extensions":["obgx"]},"application/vnd.openblox.game-binary":{"source":"iana"},"application/vnd.openeye.oeb":{"source":"iana"},"application/vnd.openofficeorg.extension":{"source":"apache","extensions":["oxt"]},"application/vnd.openstreetmap.data+xml":{"source":"iana","compressible":true,"extensions":["osm"]},"application/vnd.opentimestamps.ots":{"source":"iana"},"application/vnd.openxmlformats-officedocument.custom-properties+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.customxmlproperties+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawing+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.chart+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.chartshapes+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.diagramcolors+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.diagramdata+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.diagramlayout+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.diagramstyle+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.extended-properties+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.commentauthors+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.comments+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.handoutmaster+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.notesmaster+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.notesslide+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.presentation":{"source":"iana","compressible":false,"extensions":["pptx"]},"application/vnd.openxmlformats-officedocument.presentationml.presentation.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.presprops+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slide":{"source":"iana","extensions":["sldx"]},"application/vnd.openxmlformats-officedocument.presentationml.slide+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slidelayout+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slidemaster+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slideshow":{"source":"iana","extensions":["ppsx"]},"application/vnd.openxmlformats-officedocument.presentationml.slideshow.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slideupdateinfo+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.tablestyles+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.tags+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.template":{"source":"iana","extensions":["potx"]},"application/vnd.openxmlformats-officedocument.presentationml.template.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.viewprops+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.calcchain+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.connections+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.externallink+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.pivotcachedefinition+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.pivotcacherecords+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.pivottable+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.querytable+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.revisionheaders+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.revisionlog+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.sharedstrings+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":{"source":"iana","compressible":false,"extensions":["xlsx"]},"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.sheetmetadata+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.table+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.tablesinglecells+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.template":{"source":"iana","extensions":["xltx"]},"application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.usernames+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.volatiledependencies+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.theme+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.themeoverride+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.vmldrawing":{"source":"iana"},"application/vnd.openxmlformats-officedocument.wordprocessingml.comments+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.document":{"source":"iana","compressible":false,"extensions":["docx"]},"application/vnd.openxmlformats-officedocument.wordprocessingml.document.glossary+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.document.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.endnotes+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.fonttable+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.footer+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.footnotes+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.numbering+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.settings+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.styles+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.template":{"source":"iana","extensions":["dotx"]},"application/vnd.openxmlformats-officedocument.wordprocessingml.template.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.websettings+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-package.core-properties+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-package.digital-signature-xmlsignature+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-package.relationships+xml":{"source":"iana","compressible":true},"application/vnd.oracle.resource+json":{"source":"iana","compressible":true},"application/vnd.orange.indata":{"source":"iana"},"application/vnd.osa.netdeploy":{"source":"iana"},"application/vnd.osgeo.mapguide.package":{"source":"iana","extensions":["mgp"]},"application/vnd.osgi.bundle":{"source":"iana"},"application/vnd.osgi.dp":{"source":"iana","extensions":["dp"]},"application/vnd.osgi.subsystem":{"source":"iana","extensions":["esa"]},"application/vnd.otps.ct-kip+xml":{"source":"iana","compressible":true},"application/vnd.oxli.countgraph":{"source":"iana"},"application/vnd.pagerduty+json":{"source":"iana","compressible":true},"application/vnd.palm":{"source":"iana","extensions":["pdb","pqa","oprc"]},"application/vnd.panoply":{"source":"iana"},"application/vnd.paos.xml":{"source":"iana"},"application/vnd.patentdive":{"source":"iana"},"application/vnd.patientecommsdoc":{"source":"iana"},"application/vnd.pawaafile":{"source":"iana","extensions":["paw"]},"application/vnd.pcos":{"source":"iana"},"application/vnd.pg.format":{"source":"iana","extensions":["str"]},"application/vnd.pg.osasli":{"source":"iana","extensions":["ei6"]},"application/vnd.piaccess.application-licence":{"source":"iana"},"application/vnd.picsel":{"source":"iana","extensions":["efif"]},"application/vnd.pmi.widget":{"source":"iana","extensions":["wg"]},"application/vnd.poc.group-advertisement+xml":{"source":"iana","compressible":true},"application/vnd.pocketlearn":{"source":"iana","extensions":["plf"]},"application/vnd.powerbuilder6":{"source":"iana","extensions":["pbd"]},"application/vnd.powerbuilder6-s":{"source":"iana"},"application/vnd.powerbuilder7":{"source":"iana"},"application/vnd.powerbuilder7-s":{"source":"iana"},"application/vnd.powerbuilder75":{"source":"iana"},"application/vnd.powerbuilder75-s":{"source":"iana"},"application/vnd.preminet":{"source":"iana"},"application/vnd.previewsystems.box":{"source":"iana","extensions":["box"]},"application/vnd.proteus.magazine":{"source":"iana","extensions":["mgz"]},"application/vnd.psfs":{"source":"iana"},"application/vnd.publishare-delta-tree":{"source":"iana","extensions":["qps"]},"application/vnd.pvi.ptid1":{"source":"iana","extensions":["ptid"]},"application/vnd.pwg-multiplexed":{"source":"iana"},"application/vnd.pwg-xhtml-print+xml":{"source":"iana","compressible":true},"application/vnd.qualcomm.brew-app-res":{"source":"iana"},"application/vnd.quarantainenet":{"source":"iana"},"application/vnd.quark.quarkxpress":{"source":"iana","extensions":["qxd","qxt","qwd","qwt","qxl","qxb"]},"application/vnd.quobject-quoxdocument":{"source":"iana"},"application/vnd.radisys.moml+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit-conf+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit-conn+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit-dialog+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit-stream+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-conf+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-base+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-fax-detect+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-fax-sendrecv+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-group+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-speech+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-transform+xml":{"source":"iana","compressible":true},"application/vnd.rainstor.data":{"source":"iana"},"application/vnd.rapid":{"source":"iana"},"application/vnd.rar":{"source":"iana","extensions":["rar"]},"application/vnd.realvnc.bed":{"source":"iana","extensions":["bed"]},"application/vnd.recordare.musicxml":{"source":"iana","extensions":["mxl"]},"application/vnd.recordare.musicxml+xml":{"source":"iana","compressible":true,"extensions":["musicxml"]},"application/vnd.renlearn.rlprint":{"source":"iana"},"application/vnd.resilient.logic":{"source":"iana"},"application/vnd.restful+json":{"source":"iana","compressible":true},"application/vnd.rig.cryptonote":{"source":"iana","extensions":["cryptonote"]},"application/vnd.rim.cod":{"source":"apache","extensions":["cod"]},"application/vnd.rn-realmedia":{"source":"apache","extensions":["rm"]},"application/vnd.rn-realmedia-vbr":{"source":"apache","extensions":["rmvb"]},"application/vnd.route66.link66+xml":{"source":"iana","compressible":true,"extensions":["link66"]},"application/vnd.rs-274x":{"source":"iana"},"application/vnd.ruckus.download":{"source":"iana"},"application/vnd.s3sms":{"source":"iana"},"application/vnd.sailingtracker.track":{"source":"iana","extensions":["st"]},"application/vnd.sar":{"source":"iana"},"application/vnd.sbm.cid":{"source":"iana"},"application/vnd.sbm.mid2":{"source":"iana"},"application/vnd.scribus":{"source":"iana"},"application/vnd.sealed.3df":{"source":"iana"},"application/vnd.sealed.csf":{"source":"iana"},"application/vnd.sealed.doc":{"source":"iana"},"application/vnd.sealed.eml":{"source":"iana"},"application/vnd.sealed.mht":{"source":"iana"},"application/vnd.sealed.net":{"source":"iana"},"application/vnd.sealed.ppt":{"source":"iana"},"application/vnd.sealed.tiff":{"source":"iana"},"application/vnd.sealed.xls":{"source":"iana"},"application/vnd.sealedmedia.softseal.html":{"source":"iana"},"application/vnd.sealedmedia.softseal.pdf":{"source":"iana"},"application/vnd.seemail":{"source":"iana","extensions":["see"]},"application/vnd.seis+json":{"source":"iana","compressible":true},"application/vnd.sema":{"source":"iana","extensions":["sema"]},"application/vnd.semd":{"source":"iana","extensions":["semd"]},"application/vnd.semf":{"source":"iana","extensions":["semf"]},"application/vnd.shade-save-file":{"source":"iana"},"application/vnd.shana.informed.formdata":{"source":"iana","extensions":["ifm"]},"application/vnd.shana.informed.formtemplate":{"source":"iana","extensions":["itp"]},"application/vnd.shana.informed.interchange":{"source":"iana","extensions":["iif"]},"application/vnd.shana.informed.package":{"source":"iana","extensions":["ipk"]},"application/vnd.shootproof+json":{"source":"iana","compressible":true},"application/vnd.shopkick+json":{"source":"iana","compressible":true},"application/vnd.shp":{"source":"iana"},"application/vnd.shx":{"source":"iana"},"application/vnd.sigrok.session":{"source":"iana"},"application/vnd.simtech-mindmapper":{"source":"iana","extensions":["twd","twds"]},"application/vnd.siren+json":{"source":"iana","compressible":true},"application/vnd.smaf":{"source":"iana","extensions":["mmf"]},"application/vnd.smart.notebook":{"source":"iana"},"application/vnd.smart.teacher":{"source":"iana","extensions":["teacher"]},"application/vnd.snesdev-page-table":{"source":"iana"},"application/vnd.software602.filler.form+xml":{"source":"iana","compressible":true,"extensions":["fo"]},"application/vnd.software602.filler.form-xml-zip":{"source":"iana"},"application/vnd.solent.sdkm+xml":{"source":"iana","compressible":true,"extensions":["sdkm","sdkd"]},"application/vnd.spotfire.dxp":{"source":"iana","extensions":["dxp"]},"application/vnd.spotfire.sfs":{"source":"iana","extensions":["sfs"]},"application/vnd.sqlite3":{"source":"iana"},"application/vnd.sss-cod":{"source":"iana"},"application/vnd.sss-dtf":{"source":"iana"},"application/vnd.sss-ntf":{"source":"iana"},"application/vnd.stardivision.calc":{"source":"apache","extensions":["sdc"]},"application/vnd.stardivision.draw":{"source":"apache","extensions":["sda"]},"application/vnd.stardivision.impress":{"source":"apache","extensions":["sdd"]},"application/vnd.stardivision.math":{"source":"apache","extensions":["smf"]},"application/vnd.stardivision.writer":{"source":"apache","extensions":["sdw","vor"]},"application/vnd.stardivision.writer-global":{"source":"apache","extensions":["sgl"]},"application/vnd.stepmania.package":{"source":"iana","extensions":["smzip"]},"application/vnd.stepmania.stepchart":{"source":"iana","extensions":["sm"]},"application/vnd.street-stream":{"source":"iana"},"application/vnd.sun.wadl+xml":{"source":"iana","compressible":true,"extensions":["wadl"]},"application/vnd.sun.xml.calc":{"source":"apache","extensions":["sxc"]},"application/vnd.sun.xml.calc.template":{"source":"apache","extensions":["stc"]},"application/vnd.sun.xml.draw":{"source":"apache","extensions":["sxd"]},"application/vnd.sun.xml.draw.template":{"source":"apache","extensions":["std"]},"application/vnd.sun.xml.impress":{"source":"apache","extensions":["sxi"]},"application/vnd.sun.xml.impress.template":{"source":"apache","extensions":["sti"]},"application/vnd.sun.xml.math":{"source":"apache","extensions":["sxm"]},"application/vnd.sun.xml.writer":{"source":"apache","extensions":["sxw"]},"application/vnd.sun.xml.writer.global":{"source":"apache","extensions":["sxg"]},"application/vnd.sun.xml.writer.template":{"source":"apache","extensions":["stw"]},"application/vnd.sus-calendar":{"source":"iana","extensions":["sus","susp"]},"application/vnd.svd":{"source":"iana","extensions":["svd"]},"application/vnd.swiftview-ics":{"source":"iana"},"application/vnd.sycle+xml":{"source":"iana","compressible":true},"application/vnd.syft+json":{"source":"iana","compressible":true},"application/vnd.symbian.install":{"source":"apache","extensions":["sis","sisx"]},"application/vnd.syncml+xml":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["xsm"]},"application/vnd.syncml.dm+wbxml":{"source":"iana","charset":"UTF-8","extensions":["bdm"]},"application/vnd.syncml.dm+xml":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["xdm"]},"application/vnd.syncml.dm.notification":{"source":"iana"},"application/vnd.syncml.dmddf+wbxml":{"source":"iana"},"application/vnd.syncml.dmddf+xml":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["ddf"]},"application/vnd.syncml.dmtnds+wbxml":{"source":"iana"},"application/vnd.syncml.dmtnds+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.syncml.ds.notification":{"source":"iana"},"application/vnd.tableschema+json":{"source":"iana","compressible":true},"application/vnd.tao.intent-module-archive":{"source":"iana","extensions":["tao"]},"application/vnd.tcpdump.pcap":{"source":"iana","extensions":["pcap","cap","dmp"]},"application/vnd.think-cell.ppttc+json":{"source":"iana","compressible":true},"application/vnd.tmd.mediaflex.api+xml":{"source":"iana","compressible":true},"application/vnd.tml":{"source":"iana"},"application/vnd.tmobile-livetv":{"source":"iana","extensions":["tmo"]},"application/vnd.tri.onesource":{"source":"iana"},"application/vnd.trid.tpt":{"source":"iana","extensions":["tpt"]},"application/vnd.triscape.mxs":{"source":"iana","extensions":["mxs"]},"application/vnd.trueapp":{"source":"iana","extensions":["tra"]},"application/vnd.truedoc":{"source":"iana"},"application/vnd.ubisoft.webplayer":{"source":"iana"},"application/vnd.ufdl":{"source":"iana","extensions":["ufd","ufdl"]},"application/vnd.uiq.theme":{"source":"iana","extensions":["utz"]},"application/vnd.umajin":{"source":"iana","extensions":["umj"]},"application/vnd.unity":{"source":"iana","extensions":["unityweb"]},"application/vnd.uoml+xml":{"source":"iana","compressible":true,"extensions":["uoml"]},"application/vnd.uplanet.alert":{"source":"iana"},"application/vnd.uplanet.alert-wbxml":{"source":"iana"},"application/vnd.uplanet.bearer-choice":{"source":"iana"},"application/vnd.uplanet.bearer-choice-wbxml":{"source":"iana"},"application/vnd.uplanet.cacheop":{"source":"iana"},"application/vnd.uplanet.cacheop-wbxml":{"source":"iana"},"application/vnd.uplanet.channel":{"source":"iana"},"application/vnd.uplanet.channel-wbxml":{"source":"iana"},"application/vnd.uplanet.list":{"source":"iana"},"application/vnd.uplanet.list-wbxml":{"source":"iana"},"application/vnd.uplanet.listcmd":{"source":"iana"},"application/vnd.uplanet.listcmd-wbxml":{"source":"iana"},"application/vnd.uplanet.signal":{"source":"iana"},"application/vnd.uri-map":{"source":"iana"},"application/vnd.valve.source.material":{"source":"iana"},"application/vnd.vcx":{"source":"iana","extensions":["vcx"]},"application/vnd.vd-study":{"source":"iana"},"application/vnd.vectorworks":{"source":"iana"},"application/vnd.vel+json":{"source":"iana","compressible":true},"application/vnd.verimatrix.vcas":{"source":"iana"},"application/vnd.veritone.aion+json":{"source":"iana","compressible":true},"application/vnd.veryant.thin":{"source":"iana"},"application/vnd.ves.encrypted":{"source":"iana"},"application/vnd.vidsoft.vidconference":{"source":"iana"},"application/vnd.visio":{"source":"iana","extensions":["vsd","vst","vss","vsw"]},"application/vnd.visionary":{"source":"iana","extensions":["vis"]},"application/vnd.vividence.scriptfile":{"source":"iana"},"application/vnd.vsf":{"source":"iana","extensions":["vsf"]},"application/vnd.wap.sic":{"source":"iana"},"application/vnd.wap.slc":{"source":"iana"},"application/vnd.wap.wbxml":{"source":"iana","charset":"UTF-8","extensions":["wbxml"]},"application/vnd.wap.wmlc":{"source":"iana","extensions":["wmlc"]},"application/vnd.wap.wmlscriptc":{"source":"iana","extensions":["wmlsc"]},"application/vnd.webturbo":{"source":"iana","extensions":["wtb"]},"application/vnd.wfa.dpp":{"source":"iana"},"application/vnd.wfa.p2p":{"source":"iana"},"application/vnd.wfa.wsc":{"source":"iana"},"application/vnd.windows.devicepairing":{"source":"iana"},"application/vnd.wmc":{"source":"iana"},"application/vnd.wmf.bootstrap":{"source":"iana"},"application/vnd.wolfram.mathematica":{"source":"iana"},"application/vnd.wolfram.mathematica.package":{"source":"iana"},"application/vnd.wolfram.player":{"source":"iana","extensions":["nbp"]},"application/vnd.wordperfect":{"source":"iana","extensions":["wpd"]},"application/vnd.wqd":{"source":"iana","extensions":["wqd"]},"application/vnd.wrq-hp3000-labelled":{"source":"iana"},"application/vnd.wt.stf":{"source":"iana","extensions":["stf"]},"application/vnd.wv.csp+wbxml":{"source":"iana"},"application/vnd.wv.csp+xml":{"source":"iana","compressible":true},"application/vnd.wv.ssp+xml":{"source":"iana","compressible":true},"application/vnd.xacml+json":{"source":"iana","compressible":true},"application/vnd.xara":{"source":"iana","extensions":["xar"]},"application/vnd.xfdl":{"source":"iana","extensions":["xfdl"]},"application/vnd.xfdl.webform":{"source":"iana"},"application/vnd.xmi+xml":{"source":"iana","compressible":true},"application/vnd.xmpie.cpkg":{"source":"iana"},"application/vnd.xmpie.dpkg":{"source":"iana"},"application/vnd.xmpie.plan":{"source":"iana"},"application/vnd.xmpie.ppkg":{"source":"iana"},"application/vnd.xmpie.xlim":{"source":"iana"},"application/vnd.yamaha.hv-dic":{"source":"iana","extensions":["hvd"]},"application/vnd.yamaha.hv-script":{"source":"iana","extensions":["hvs"]},"application/vnd.yamaha.hv-voice":{"source":"iana","extensions":["hvp"]},"application/vnd.yamaha.openscoreformat":{"source":"iana","extensions":["osf"]},"application/vnd.yamaha.openscoreformat.osfpvg+xml":{"source":"iana","compressible":true,"extensions":["osfpvg"]},"application/vnd.yamaha.remote-setup":{"source":"iana"},"application/vnd.yamaha.smaf-audio":{"source":"iana","extensions":["saf"]},"application/vnd.yamaha.smaf-phrase":{"source":"iana","extensions":["spf"]},"application/vnd.yamaha.through-ngn":{"source":"iana"},"application/vnd.yamaha.tunnel-udpencap":{"source":"iana"},"application/vnd.yaoweme":{"source":"iana"},"application/vnd.yellowriver-custom-menu":{"source":"iana","extensions":["cmp"]},"application/vnd.youtube.yt":{"source":"iana"},"application/vnd.zul":{"source":"iana","extensions":["zir","zirz"]},"application/vnd.zzazz.deck+xml":{"source":"iana","compressible":true,"extensions":["zaz"]},"application/voicexml+xml":{"source":"iana","compressible":true,"extensions":["vxml"]},"application/voucher-cms+json":{"source":"iana","compressible":true},"application/vq-rtcpxr":{"source":"iana"},"application/wasm":{"source":"iana","compressible":true,"extensions":["wasm"]},"application/watcherinfo+xml":{"source":"iana","compressible":true,"extensions":["wif"]},"application/webpush-options+json":{"source":"iana","compressible":true},"application/whoispp-query":{"source":"iana"},"application/whoispp-response":{"source":"iana"},"application/widget":{"source":"iana","extensions":["wgt"]},"application/winhlp":{"source":"apache","extensions":["hlp"]},"application/wita":{"source":"iana"},"application/wordperfect5.1":{"source":"iana"},"application/wsdl+xml":{"source":"iana","compressible":true,"extensions":["wsdl"]},"application/wspolicy+xml":{"source":"iana","compressible":true,"extensions":["wspolicy"]},"application/x-7z-compressed":{"source":"apache","compressible":false,"extensions":["7z"]},"application/x-abiword":{"source":"apache","extensions":["abw"]},"application/x-ace-compressed":{"source":"apache","extensions":["ace"]},"application/x-amf":{"source":"apache"},"application/x-apple-diskimage":{"source":"apache","extensions":["dmg"]},"application/x-arj":{"compressible":false,"extensions":["arj"]},"application/x-authorware-bin":{"source":"apache","extensions":["aab","x32","u32","vox"]},"application/x-authorware-map":{"source":"apache","extensions":["aam"]},"application/x-authorware-seg":{"source":"apache","extensions":["aas"]},"application/x-bcpio":{"source":"apache","extensions":["bcpio"]},"application/x-bdoc":{"compressible":false,"extensions":["bdoc"]},"application/x-bittorrent":{"source":"apache","extensions":["torrent"]},"application/x-blorb":{"source":"apache","extensions":["blb","blorb"]},"application/x-bzip":{"source":"apache","compressible":false,"extensions":["bz"]},"application/x-bzip2":{"source":"apache","compressible":false,"extensions":["bz2","boz"]},"application/x-cbr":{"source":"apache","extensions":["cbr","cba","cbt","cbz","cb7"]},"application/x-cdlink":{"source":"apache","extensions":["vcd"]},"application/x-cfs-compressed":{"source":"apache","extensions":["cfs"]},"application/x-chat":{"source":"apache","extensions":["chat"]},"application/x-chess-pgn":{"source":"apache","extensions":["pgn"]},"application/x-chrome-extension":{"extensions":["crx"]},"application/x-cocoa":{"source":"nginx","extensions":["cco"]},"application/x-compress":{"source":"apache"},"application/x-conference":{"source":"apache","extensions":["nsc"]},"application/x-cpio":{"source":"apache","extensions":["cpio"]},"application/x-csh":{"source":"apache","extensions":["csh"]},"application/x-deb":{"compressible":false},"application/x-debian-package":{"source":"apache","extensions":["deb","udeb"]},"application/x-dgc-compressed":{"source":"apache","extensions":["dgc"]},"application/x-director":{"source":"apache","extensions":["dir","dcr","dxr","cst","cct","cxt","w3d","fgd","swa"]},"application/x-doom":{"source":"apache","extensions":["wad"]},"application/x-dtbncx+xml":{"source":"apache","compressible":true,"extensions":["ncx"]},"application/x-dtbook+xml":{"source":"apache","compressible":true,"extensions":["dtb"]},"application/x-dtbresource+xml":{"source":"apache","compressible":true,"extensions":["res"]},"application/x-dvi":{"source":"apache","compressible":false,"extensions":["dvi"]},"application/x-envoy":{"source":"apache","extensions":["evy"]},"application/x-eva":{"source":"apache","extensions":["eva"]},"application/x-font-bdf":{"source":"apache","extensions":["bdf"]},"application/x-font-dos":{"source":"apache"},"application/x-font-framemaker":{"source":"apache"},"application/x-font-ghostscript":{"source":"apache","extensions":["gsf"]},"application/x-font-libgrx":{"source":"apache"},"application/x-font-linux-psf":{"source":"apache","extensions":["psf"]},"application/x-font-pcf":{"source":"apache","extensions":["pcf"]},"application/x-font-snf":{"source":"apache","extensions":["snf"]},"application/x-font-speedo":{"source":"apache"},"application/x-font-sunos-news":{"source":"apache"},"application/x-font-type1":{"source":"apache","extensions":["pfa","pfb","pfm","afm"]},"application/x-font-vfont":{"source":"apache"},"application/x-freearc":{"source":"apache","extensions":["arc"]},"application/x-futuresplash":{"source":"apache","extensions":["spl"]},"application/x-gca-compressed":{"source":"apache","extensions":["gca"]},"application/x-glulx":{"source":"apache","extensions":["ulx"]},"application/x-gnumeric":{"source":"apache","extensions":["gnumeric"]},"application/x-gramps-xml":{"source":"apache","extensions":["gramps"]},"application/x-gtar":{"source":"apache","extensions":["gtar"]},"application/x-gzip":{"source":"apache"},"application/x-hdf":{"source":"apache","extensions":["hdf"]},"application/x-httpd-php":{"compressible":true,"extensions":["php"]},"application/x-install-instructions":{"source":"apache","extensions":["install"]},"application/x-iso9660-image":{"source":"apache","extensions":["iso"]},"application/x-iwork-keynote-sffkey":{"extensions":["key"]},"application/x-iwork-numbers-sffnumbers":{"extensions":["numbers"]},"application/x-iwork-pages-sffpages":{"extensions":["pages"]},"application/x-java-archive-diff":{"source":"nginx","extensions":["jardiff"]},"application/x-java-jnlp-file":{"source":"apache","compressible":false,"extensions":["jnlp"]},"application/x-javascript":{"compressible":true},"application/x-keepass2":{"extensions":["kdbx"]},"application/x-latex":{"source":"apache","compressible":false,"extensions":["latex"]},"application/x-lua-bytecode":{"extensions":["luac"]},"application/x-lzh-compressed":{"source":"apache","extensions":["lzh","lha"]},"application/x-makeself":{"source":"nginx","extensions":["run"]},"application/x-mie":{"source":"apache","extensions":["mie"]},"application/x-mobipocket-ebook":{"source":"apache","extensions":["prc","mobi"]},"application/x-mpegurl":{"compressible":false},"application/x-ms-application":{"source":"apache","extensions":["application"]},"application/x-ms-shortcut":{"source":"apache","extensions":["lnk"]},"application/x-ms-wmd":{"source":"apache","extensions":["wmd"]},"application/x-ms-wmz":{"source":"apache","extensions":["wmz"]},"application/x-ms-xbap":{"source":"apache","extensions":["xbap"]},"application/x-msaccess":{"source":"apache","extensions":["mdb"]},"application/x-msbinder":{"source":"apache","extensions":["obd"]},"application/x-mscardfile":{"source":"apache","extensions":["crd"]},"application/x-msclip":{"source":"apache","extensions":["clp"]},"application/x-msdos-program":{"extensions":["exe"]},"application/x-msdownload":{"source":"apache","extensions":["exe","dll","com","bat","msi"]},"application/x-msmediaview":{"source":"apache","extensions":["mvb","m13","m14"]},"application/x-msmetafile":{"source":"apache","extensions":["wmf","wmz","emf","emz"]},"application/x-msmoney":{"source":"apache","extensions":["mny"]},"application/x-mspublisher":{"source":"apache","extensions":["pub"]},"application/x-msschedule":{"source":"apache","extensions":["scd"]},"application/x-msterminal":{"source":"apache","extensions":["trm"]},"application/x-mswrite":{"source":"apache","extensions":["wri"]},"application/x-netcdf":{"source":"apache","extensions":["nc","cdf"]},"application/x-ns-proxy-autoconfig":{"compressible":true,"extensions":["pac"]},"application/x-nzb":{"source":"apache","extensions":["nzb"]},"application/x-perl":{"source":"nginx","extensions":["pl","pm"]},"application/x-pilot":{"source":"nginx","extensions":["prc","pdb"]},"application/x-pkcs12":{"source":"apache","compressible":false,"extensions":["p12","pfx"]},"application/x-pkcs7-certificates":{"source":"apache","extensions":["p7b","spc"]},"application/x-pkcs7-certreqresp":{"source":"apache","extensions":["p7r"]},"application/x-pki-message":{"source":"iana"},"application/x-rar-compressed":{"source":"apache","compressible":false,"extensions":["rar"]},"application/x-redhat-package-manager":{"source":"nginx","extensions":["rpm"]},"application/x-research-info-systems":{"source":"apache","extensions":["ris"]},"application/x-sea":{"source":"nginx","extensions":["sea"]},"application/x-sh":{"source":"apache","compressible":true,"extensions":["sh"]},"application/x-shar":{"source":"apache","extensions":["shar"]},"application/x-shockwave-flash":{"source":"apache","compressible":false,"extensions":["swf"]},"application/x-silverlight-app":{"source":"apache","extensions":["xap"]},"application/x-sql":{"source":"apache","extensions":["sql"]},"application/x-stuffit":{"source":"apache","compressible":false,"extensions":["sit"]},"application/x-stuffitx":{"source":"apache","extensions":["sitx"]},"application/x-subrip":{"source":"apache","extensions":["srt"]},"application/x-sv4cpio":{"source":"apache","extensions":["sv4cpio"]},"application/x-sv4crc":{"source":"apache","extensions":["sv4crc"]},"application/x-t3vm-image":{"source":"apache","extensions":["t3"]},"application/x-tads":{"source":"apache","extensions":["gam"]},"application/x-tar":{"source":"apache","compressible":true,"extensions":["tar"]},"application/x-tcl":{"source":"apache","extensions":["tcl","tk"]},"application/x-tex":{"source":"apache","extensions":["tex"]},"application/x-tex-tfm":{"source":"apache","extensions":["tfm"]},"application/x-texinfo":{"source":"apache","extensions":["texinfo","texi"]},"application/x-tgif":{"source":"apache","extensions":["obj"]},"application/x-ustar":{"source":"apache","extensions":["ustar"]},"application/x-virtualbox-hdd":{"compressible":true,"extensions":["hdd"]},"application/x-virtualbox-ova":{"compressible":true,"extensions":["ova"]},"application/x-virtualbox-ovf":{"compressible":true,"extensions":["ovf"]},"application/x-virtualbox-vbox":{"compressible":true,"extensions":["vbox"]},"application/x-virtualbox-vbox-extpack":{"compressible":false,"extensions":["vbox-extpack"]},"application/x-virtualbox-vdi":{"compressible":true,"extensions":["vdi"]},"application/x-virtualbox-vhd":{"compressible":true,"extensions":["vhd"]},"application/x-virtualbox-vmdk":{"compressible":true,"extensions":["vmdk"]},"application/x-wais-source":{"source":"apache","extensions":["src"]},"application/x-web-app-manifest+json":{"compressible":true,"extensions":["webapp"]},"application/x-www-form-urlencoded":{"source":"iana","compressible":true},"application/x-x509-ca-cert":{"source":"iana","extensions":["der","crt","pem"]},"application/x-x509-ca-ra-cert":{"source":"iana"},"application/x-x509-next-ca-cert":{"source":"iana"},"application/x-xfig":{"source":"apache","extensions":["fig"]},"application/x-xliff+xml":{"source":"apache","compressible":true,"extensions":["xlf"]},"application/x-xpinstall":{"source":"apache","compressible":false,"extensions":["xpi"]},"application/x-xz":{"source":"apache","extensions":["xz"]},"application/x-zmachine":{"source":"apache","extensions":["z1","z2","z3","z4","z5","z6","z7","z8"]},"application/x400-bp":{"source":"iana"},"application/xacml+xml":{"source":"iana","compressible":true},"application/xaml+xml":{"source":"apache","compressible":true,"extensions":["xaml"]},"application/xcap-att+xml":{"source":"iana","compressible":true,"extensions":["xav"]},"application/xcap-caps+xml":{"source":"iana","compressible":true,"extensions":["xca"]},"application/xcap-diff+xml":{"source":"iana","compressible":true,"extensions":["xdf"]},"application/xcap-el+xml":{"source":"iana","compressible":true,"extensions":["xel"]},"application/xcap-error+xml":{"source":"iana","compressible":true},"application/xcap-ns+xml":{"source":"iana","compressible":true,"extensions":["xns"]},"application/xcon-conference-info+xml":{"source":"iana","compressible":true},"application/xcon-conference-info-diff+xml":{"source":"iana","compressible":true},"application/xenc+xml":{"source":"iana","compressible":true,"extensions":["xenc"]},"application/xhtml+xml":{"source":"iana","compressible":true,"extensions":["xhtml","xht"]},"application/xhtml-voice+xml":{"source":"apache","compressible":true},"application/xliff+xml":{"source":"iana","compressible":true,"extensions":["xlf"]},"application/xml":{"source":"iana","compressible":true,"extensions":["xml","xsl","xsd","rng"]},"application/xml-dtd":{"source":"iana","compressible":true,"extensions":["dtd"]},"application/xml-external-parsed-entity":{"source":"iana"},"application/xml-patch+xml":{"source":"iana","compressible":true},"application/xmpp+xml":{"source":"iana","compressible":true},"application/xop+xml":{"source":"iana","compressible":true,"extensions":["xop"]},"application/xproc+xml":{"source":"apache","compressible":true,"extensions":["xpl"]},"application/xslt+xml":{"source":"iana","compressible":true,"extensions":["xsl","xslt"]},"application/xspf+xml":{"source":"apache","compressible":true,"extensions":["xspf"]},"application/xv+xml":{"source":"iana","compressible":true,"extensions":["mxml","xhvml","xvml","xvm"]},"application/yang":{"source":"iana","extensions":["yang"]},"application/yang-data+json":{"source":"iana","compressible":true},"application/yang-data+xml":{"source":"iana","compressible":true},"application/yang-patch+json":{"source":"iana","compressible":true},"application/yang-patch+xml":{"source":"iana","compressible":true},"application/yin+xml":{"source":"iana","compressible":true,"extensions":["yin"]},"application/zip":{"source":"iana","compressible":false,"extensions":["zip"]},"application/zlib":{"source":"iana"},"application/zstd":{"source":"iana"},"audio/1d-interleaved-parityfec":{"source":"iana"},"audio/32kadpcm":{"source":"iana"},"audio/3gpp":{"source":"iana","compressible":false,"extensions":["3gpp"]},"audio/3gpp2":{"source":"iana"},"audio/aac":{"source":"iana"},"audio/ac3":{"source":"iana"},"audio/adpcm":{"source":"apache","extensions":["adp"]},"audio/amr":{"source":"iana","extensions":["amr"]},"audio/amr-wb":{"source":"iana"},"audio/amr-wb+":{"source":"iana"},"audio/aptx":{"source":"iana"},"audio/asc":{"source":"iana"},"audio/atrac-advanced-lossless":{"source":"iana"},"audio/atrac-x":{"source":"iana"},"audio/atrac3":{"source":"iana"},"audio/basic":{"source":"iana","compressible":false,"extensions":["au","snd"]},"audio/bv16":{"source":"iana"},"audio/bv32":{"source":"iana"},"audio/clearmode":{"source":"iana"},"audio/cn":{"source":"iana"},"audio/dat12":{"source":"iana"},"audio/dls":{"source":"iana"},"audio/dsr-es201108":{"source":"iana"},"audio/dsr-es202050":{"source":"iana"},"audio/dsr-es202211":{"source":"iana"},"audio/dsr-es202212":{"source":"iana"},"audio/dv":{"source":"iana"},"audio/dvi4":{"source":"iana"},"audio/eac3":{"source":"iana"},"audio/encaprtp":{"source":"iana"},"audio/evrc":{"source":"iana"},"audio/evrc-qcp":{"source":"iana"},"audio/evrc0":{"source":"iana"},"audio/evrc1":{"source":"iana"},"audio/evrcb":{"source":"iana"},"audio/evrcb0":{"source":"iana"},"audio/evrcb1":{"source":"iana"},"audio/evrcnw":{"source":"iana"},"audio/evrcnw0":{"source":"iana"},"audio/evrcnw1":{"source":"iana"},"audio/evrcwb":{"source":"iana"},"audio/evrcwb0":{"source":"iana"},"audio/evrcwb1":{"source":"iana"},"audio/evs":{"source":"iana"},"audio/flexfec":{"source":"iana"},"audio/fwdred":{"source":"iana"},"audio/g711-0":{"source":"iana"},"audio/g719":{"source":"iana"},"audio/g722":{"source":"iana"},"audio/g7221":{"source":"iana"},"audio/g723":{"source":"iana"},"audio/g726-16":{"source":"iana"},"audio/g726-24":{"source":"iana"},"audio/g726-32":{"source":"iana"},"audio/g726-40":{"source":"iana"},"audio/g728":{"source":"iana"},"audio/g729":{"source":"iana"},"audio/g7291":{"source":"iana"},"audio/g729d":{"source":"iana"},"audio/g729e":{"source":"iana"},"audio/gsm":{"source":"iana"},"audio/gsm-efr":{"source":"iana"},"audio/gsm-hr-08":{"source":"iana"},"audio/ilbc":{"source":"iana"},"audio/ip-mr_v2.5":{"source":"iana"},"audio/isac":{"source":"apache"},"audio/l16":{"source":"iana"},"audio/l20":{"source":"iana"},"audio/l24":{"source":"iana","compressible":false},"audio/l8":{"source":"iana"},"audio/lpc":{"source":"iana"},"audio/melp":{"source":"iana"},"audio/melp1200":{"source":"iana"},"audio/melp2400":{"source":"iana"},"audio/melp600":{"source":"iana"},"audio/mhas":{"source":"iana"},"audio/midi":{"source":"apache","extensions":["mid","midi","kar","rmi"]},"audio/mobile-xmf":{"source":"iana","extensions":["mxmf"]},"audio/mp3":{"compressible":false,"extensions":["mp3"]},"audio/mp4":{"source":"iana","compressible":false,"extensions":["m4a","mp4a"]},"audio/mp4a-latm":{"source":"iana"},"audio/mpa":{"source":"iana"},"audio/mpa-robust":{"source":"iana"},"audio/mpeg":{"source":"iana","compressible":false,"extensions":["mpga","mp2","mp2a","mp3","m2a","m3a"]},"audio/mpeg4-generic":{"source":"iana"},"audio/musepack":{"source":"apache"},"audio/ogg":{"source":"iana","compressible":false,"extensions":["oga","ogg","spx","opus"]},"audio/opus":{"source":"iana"},"audio/parityfec":{"source":"iana"},"audio/pcma":{"source":"iana"},"audio/pcma-wb":{"source":"iana"},"audio/pcmu":{"source":"iana"},"audio/pcmu-wb":{"source":"iana"},"audio/prs.sid":{"source":"iana"},"audio/qcelp":{"source":"iana"},"audio/raptorfec":{"source":"iana"},"audio/red":{"source":"iana"},"audio/rtp-enc-aescm128":{"source":"iana"},"audio/rtp-midi":{"source":"iana"},"audio/rtploopback":{"source":"iana"},"audio/rtx":{"source":"iana"},"audio/s3m":{"source":"apache","extensions":["s3m"]},"audio/scip":{"source":"iana"},"audio/silk":{"source":"apache","extensions":["sil"]},"audio/smv":{"source":"iana"},"audio/smv-qcp":{"source":"iana"},"audio/smv0":{"source":"iana"},"audio/sofa":{"source":"iana"},"audio/sp-midi":{"source":"iana"},"audio/speex":{"source":"iana"},"audio/t140c":{"source":"iana"},"audio/t38":{"source":"iana"},"audio/telephone-event":{"source":"iana"},"audio/tetra_acelp":{"source":"iana"},"audio/tetra_acelp_bb":{"source":"iana"},"audio/tone":{"source":"iana"},"audio/tsvcis":{"source":"iana"},"audio/uemclip":{"source":"iana"},"audio/ulpfec":{"source":"iana"},"audio/usac":{"source":"iana"},"audio/vdvi":{"source":"iana"},"audio/vmr-wb":{"source":"iana"},"audio/vnd.3gpp.iufp":{"source":"iana"},"audio/vnd.4sb":{"source":"iana"},"audio/vnd.audiokoz":{"source":"iana"},"audio/vnd.celp":{"source":"iana"},"audio/vnd.cisco.nse":{"source":"iana"},"audio/vnd.cmles.radio-events":{"source":"iana"},"audio/vnd.cns.anp1":{"source":"iana"},"audio/vnd.cns.inf1":{"source":"iana"},"audio/vnd.dece.audio":{"source":"iana","extensions":["uva","uvva"]},"audio/vnd.digital-winds":{"source":"iana","extensions":["eol"]},"audio/vnd.dlna.adts":{"source":"iana"},"audio/vnd.dolby.heaac.1":{"source":"iana"},"audio/vnd.dolby.heaac.2":{"source":"iana"},"audio/vnd.dolby.mlp":{"source":"iana"},"audio/vnd.dolby.mps":{"source":"iana"},"audio/vnd.dolby.pl2":{"source":"iana"},"audio/vnd.dolby.pl2x":{"source":"iana"},"audio/vnd.dolby.pl2z":{"source":"iana"},"audio/vnd.dolby.pulse.1":{"source":"iana"},"audio/vnd.dra":{"source":"iana","extensions":["dra"]},"audio/vnd.dts":{"source":"iana","extensions":["dts"]},"audio/vnd.dts.hd":{"source":"iana","extensions":["dtshd"]},"audio/vnd.dts.uhd":{"source":"iana"},"audio/vnd.dvb.file":{"source":"iana"},"audio/vnd.everad.plj":{"source":"iana"},"audio/vnd.hns.audio":{"source":"iana"},"audio/vnd.lucent.voice":{"source":"iana","extensions":["lvp"]},"audio/vnd.ms-playready.media.pya":{"source":"iana","extensions":["pya"]},"audio/vnd.nokia.mobile-xmf":{"source":"iana"},"audio/vnd.nortel.vbk":{"source":"iana"},"audio/vnd.nuera.ecelp4800":{"source":"iana","extensions":["ecelp4800"]},"audio/vnd.nuera.ecelp7470":{"source":"iana","extensions":["ecelp7470"]},"audio/vnd.nuera.ecelp9600":{"source":"iana","extensions":["ecelp9600"]},"audio/vnd.octel.sbc":{"source":"iana"},"audio/vnd.presonus.multitrack":{"source":"iana"},"audio/vnd.qcelp":{"source":"iana"},"audio/vnd.rhetorex.32kadpcm":{"source":"iana"},"audio/vnd.rip":{"source":"iana","extensions":["rip"]},"audio/vnd.rn-realaudio":{"compressible":false},"audio/vnd.sealedmedia.softseal.mpeg":{"source":"iana"},"audio/vnd.vmx.cvsd":{"source":"iana"},"audio/vnd.wave":{"compressible":false},"audio/vorbis":{"source":"iana","compressible":false},"audio/vorbis-config":{"source":"iana"},"audio/wav":{"compressible":false,"extensions":["wav"]},"audio/wave":{"compressible":false,"extensions":["wav"]},"audio/webm":{"source":"apache","compressible":false,"extensions":["weba"]},"audio/x-aac":{"source":"apache","compressible":false,"extensions":["aac"]},"audio/x-aiff":{"source":"apache","extensions":["aif","aiff","aifc"]},"audio/x-caf":{"source":"apache","compressible":false,"extensions":["caf"]},"audio/x-flac":{"source":"apache","extensions":["flac"]},"audio/x-m4a":{"source":"nginx","extensions":["m4a"]},"audio/x-matroska":{"source":"apache","extensions":["mka"]},"audio/x-mpegurl":{"source":"apache","extensions":["m3u"]},"audio/x-ms-wax":{"source":"apache","extensions":["wax"]},"audio/x-ms-wma":{"source":"apache","extensions":["wma"]},"audio/x-pn-realaudio":{"source":"apache","extensions":["ram","ra"]},"audio/x-pn-realaudio-plugin":{"source":"apache","extensions":["rmp"]},"audio/x-realaudio":{"source":"nginx","extensions":["ra"]},"audio/x-tta":{"source":"apache"},"audio/x-wav":{"source":"apache","extensions":["wav"]},"audio/xm":{"source":"apache","extensions":["xm"]},"chemical/x-cdx":{"source":"apache","extensions":["cdx"]},"chemical/x-cif":{"source":"apache","extensions":["cif"]},"chemical/x-cmdf":{"source":"apache","extensions":["cmdf"]},"chemical/x-cml":{"source":"apache","extensions":["cml"]},"chemical/x-csml":{"source":"apache","extensions":["csml"]},"chemical/x-pdb":{"source":"apache"},"chemical/x-xyz":{"source":"apache","extensions":["xyz"]},"font/collection":{"source":"iana","extensions":["ttc"]},"font/otf":{"source":"iana","compressible":true,"extensions":["otf"]},"font/sfnt":{"source":"iana"},"font/ttf":{"source":"iana","compressible":true,"extensions":["ttf"]},"font/woff":{"source":"iana","extensions":["woff"]},"font/woff2":{"source":"iana","extensions":["woff2"]},"image/aces":{"source":"iana","extensions":["exr"]},"image/apng":{"compressible":false,"extensions":["apng"]},"image/avci":{"source":"iana","extensions":["avci"]},"image/avcs":{"source":"iana","extensions":["avcs"]},"image/avif":{"source":"iana","compressible":false,"extensions":["avif"]},"image/bmp":{"source":"iana","compressible":true,"extensions":["bmp"]},"image/cgm":{"source":"iana","extensions":["cgm"]},"image/dicom-rle":{"source":"iana","extensions":["drle"]},"image/emf":{"source":"iana","extensions":["emf"]},"image/fits":{"source":"iana","extensions":["fits"]},"image/g3fax":{"source":"iana","extensions":["g3"]},"image/gif":{"source":"iana","compressible":false,"extensions":["gif"]},"image/heic":{"source":"iana","extensions":["heic"]},"image/heic-sequence":{"source":"iana","extensions":["heics"]},"image/heif":{"source":"iana","extensions":["heif"]},"image/heif-sequence":{"source":"iana","extensions":["heifs"]},"image/hej2k":{"source":"iana","extensions":["hej2"]},"image/hsj2":{"source":"iana","extensions":["hsj2"]},"image/ief":{"source":"iana","extensions":["ief"]},"image/jls":{"source":"iana","extensions":["jls"]},"image/jp2":{"source":"iana","compressible":false,"extensions":["jp2","jpg2"]},"image/jpeg":{"source":"iana","compressible":false,"extensions":["jpeg","jpg","jpe"]},"image/jph":{"source":"iana","extensions":["jph"]},"image/jphc":{"source":"iana","extensions":["jhc"]},"image/jpm":{"source":"iana","compressible":false,"extensions":["jpm"]},"image/jpx":{"source":"iana","compressible":false,"extensions":["jpx","jpf"]},"image/jxr":{"source":"iana","extensions":["jxr"]},"image/jxra":{"source":"iana","extensions":["jxra"]},"image/jxrs":{"source":"iana","extensions":["jxrs"]},"image/jxs":{"source":"iana","extensions":["jxs"]},"image/jxsc":{"source":"iana","extensions":["jxsc"]},"image/jxsi":{"source":"iana","extensions":["jxsi"]},"image/jxss":{"source":"iana","extensions":["jxss"]},"image/ktx":{"source":"iana","extensions":["ktx"]},"image/ktx2":{"source":"iana","extensions":["ktx2"]},"image/naplps":{"source":"iana"},"image/pjpeg":{"compressible":false},"image/png":{"source":"iana","compressible":false,"extensions":["png"]},"image/prs.btif":{"source":"iana","extensions":["btif"]},"image/prs.pti":{"source":"iana","extensions":["pti"]},"image/pwg-raster":{"source":"iana"},"image/sgi":{"source":"apache","extensions":["sgi"]},"image/svg+xml":{"source":"iana","compressible":true,"extensions":["svg","svgz"]},"image/t38":{"source":"iana","extensions":["t38"]},"image/tiff":{"source":"iana","compressible":false,"extensions":["tif","tiff"]},"image/tiff-fx":{"source":"iana","extensions":["tfx"]},"image/vnd.adobe.photoshop":{"source":"iana","compressible":true,"extensions":["psd"]},"image/vnd.airzip.accelerator.azv":{"source":"iana","extensions":["azv"]},"image/vnd.cns.inf2":{"source":"iana"},"image/vnd.dece.graphic":{"source":"iana","extensions":["uvi","uvvi","uvg","uvvg"]},"image/vnd.djvu":{"source":"iana","extensions":["djvu","djv"]},"image/vnd.dvb.subtitle":{"source":"iana","extensions":["sub"]},"image/vnd.dwg":{"source":"iana","extensions":["dwg"]},"image/vnd.dxf":{"source":"iana","extensions":["dxf"]},"image/vnd.fastbidsheet":{"source":"iana","extensions":["fbs"]},"image/vnd.fpx":{"source":"iana","extensions":["fpx"]},"image/vnd.fst":{"source":"iana","extensions":["fst"]},"image/vnd.fujixerox.edmics-mmr":{"source":"iana","extensions":["mmr"]},"image/vnd.fujixerox.edmics-rlc":{"source":"iana","extensions":["rlc"]},"image/vnd.globalgraphics.pgb":{"source":"iana"},"image/vnd.microsoft.icon":{"source":"iana","compressible":true,"extensions":["ico"]},"image/vnd.mix":{"source":"iana"},"image/vnd.mozilla.apng":{"source":"iana"},"image/vnd.ms-dds":{"compressible":true,"extensions":["dds"]},"image/vnd.ms-modi":{"source":"iana","extensions":["mdi"]},"image/vnd.ms-photo":{"source":"apache","extensions":["wdp"]},"image/vnd.net-fpx":{"source":"iana","extensions":["npx"]},"image/vnd.pco.b16":{"source":"iana","extensions":["b16"]},"image/vnd.radiance":{"source":"iana"},"image/vnd.sealed.png":{"source":"iana"},"image/vnd.sealedmedia.softseal.gif":{"source":"iana"},"image/vnd.sealedmedia.softseal.jpg":{"source":"iana"},"image/vnd.svf":{"source":"iana"},"image/vnd.tencent.tap":{"source":"iana","extensions":["tap"]},"image/vnd.valve.source.texture":{"source":"iana","extensions":["vtf"]},"image/vnd.wap.wbmp":{"source":"iana","extensions":["wbmp"]},"image/vnd.xiff":{"source":"iana","extensions":["xif"]},"image/vnd.zbrush.pcx":{"source":"iana","extensions":["pcx"]},"image/webp":{"source":"apache","extensions":["webp"]},"image/wmf":{"source":"iana","extensions":["wmf"]},"image/x-3ds":{"source":"apache","extensions":["3ds"]},"image/x-cmu-raster":{"source":"apache","extensions":["ras"]},"image/x-cmx":{"source":"apache","extensions":["cmx"]},"image/x-freehand":{"source":"apache","extensions":["fh","fhc","fh4","fh5","fh7"]},"image/x-icon":{"source":"apache","compressible":true,"extensions":["ico"]},"image/x-jng":{"source":"nginx","extensions":["jng"]},"image/x-mrsid-image":{"source":"apache","extensions":["sid"]},"image/x-ms-bmp":{"source":"nginx","compressible":true,"extensions":["bmp"]},"image/x-pcx":{"source":"apache","extensions":["pcx"]},"image/x-pict":{"source":"apache","extensions":["pic","pct"]},"image/x-portable-anymap":{"source":"apache","extensions":["pnm"]},"image/x-portable-bitmap":{"source":"apache","extensions":["pbm"]},"image/x-portable-graymap":{"source":"apache","extensions":["pgm"]},"image/x-portable-pixmap":{"source":"apache","extensions":["ppm"]},"image/x-rgb":{"source":"apache","extensions":["rgb"]},"image/x-tga":{"source":"apache","extensions":["tga"]},"image/x-xbitmap":{"source":"apache","extensions":["xbm"]},"image/x-xcf":{"compressible":false},"image/x-xpixmap":{"source":"apache","extensions":["xpm"]},"image/x-xwindowdump":{"source":"apache","extensions":["xwd"]},"message/cpim":{"source":"iana"},"message/delivery-status":{"source":"iana"},"message/disposition-notification":{"source":"iana","extensions":["disposition-notification"]},"message/external-body":{"source":"iana"},"message/feedback-report":{"source":"iana"},"message/global":{"source":"iana","extensions":["u8msg"]},"message/global-delivery-status":{"source":"iana","extensions":["u8dsn"]},"message/global-disposition-notification":{"source":"iana","extensions":["u8mdn"]},"message/global-headers":{"source":"iana","extensions":["u8hdr"]},"message/http":{"source":"iana","compressible":false},"message/imdn+xml":{"source":"iana","compressible":true},"message/news":{"source":"iana"},"message/partial":{"source":"iana","compressible":false},"message/rfc822":{"source":"iana","compressible":true,"extensions":["eml","mime"]},"message/s-http":{"source":"iana"},"message/sip":{"source":"iana"},"message/sipfrag":{"source":"iana"},"message/tracking-status":{"source":"iana"},"message/vnd.si.simp":{"source":"iana"},"message/vnd.wfa.wsc":{"source":"iana","extensions":["wsc"]},"model/3mf":{"source":"iana","extensions":["3mf"]},"model/e57":{"source":"iana"},"model/gltf+json":{"source":"iana","compressible":true,"extensions":["gltf"]},"model/gltf-binary":{"source":"iana","compressible":true,"extensions":["glb"]},"model/iges":{"source":"iana","compressible":false,"extensions":["igs","iges"]},"model/mesh":{"source":"iana","compressible":false,"extensions":["msh","mesh","silo"]},"model/mtl":{"source":"iana","extensions":["mtl"]},"model/obj":{"source":"iana","extensions":["obj"]},"model/step":{"source":"iana"},"model/step+xml":{"source":"iana","compressible":true,"extensions":["stpx"]},"model/step+zip":{"source":"iana","compressible":false,"extensions":["stpz"]},"model/step-xml+zip":{"source":"iana","compressible":false,"extensions":["stpxz"]},"model/stl":{"source":"iana","extensions":["stl"]},"model/vnd.collada+xml":{"source":"iana","compressible":true,"extensions":["dae"]},"model/vnd.dwf":{"source":"iana","extensions":["dwf"]},"model/vnd.flatland.3dml":{"source":"iana"},"model/vnd.gdl":{"source":"iana","extensions":["gdl"]},"model/vnd.gs-gdl":{"source":"apache"},"model/vnd.gs.gdl":{"source":"iana"},"model/vnd.gtw":{"source":"iana","extensions":["gtw"]},"model/vnd.moml+xml":{"source":"iana","compressible":true},"model/vnd.mts":{"source":"iana","extensions":["mts"]},"model/vnd.opengex":{"source":"iana","extensions":["ogex"]},"model/vnd.parasolid.transmit.binary":{"source":"iana","extensions":["x_b"]},"model/vnd.parasolid.transmit.text":{"source":"iana","extensions":["x_t"]},"model/vnd.pytha.pyox":{"source":"iana"},"model/vnd.rosette.annotated-data-model":{"source":"iana"},"model/vnd.sap.vds":{"source":"iana","extensions":["vds"]},"model/vnd.usdz+zip":{"source":"iana","compressible":false,"extensions":["usdz"]},"model/vnd.valve.source.compiled-map":{"source":"iana","extensions":["bsp"]},"model/vnd.vtu":{"source":"iana","extensions":["vtu"]},"model/vrml":{"source":"iana","compressible":false,"extensions":["wrl","vrml"]},"model/x3d+binary":{"source":"apache","compressible":false,"extensions":["x3db","x3dbz"]},"model/x3d+fastinfoset":{"source":"iana","extensions":["x3db"]},"model/x3d+vrml":{"source":"apache","compressible":false,"extensions":["x3dv","x3dvz"]},"model/x3d+xml":{"source":"iana","compressible":true,"extensions":["x3d","x3dz"]},"model/x3d-vrml":{"source":"iana","extensions":["x3dv"]},"multipart/alternative":{"source":"iana","compressible":false},"multipart/appledouble":{"source":"iana"},"multipart/byteranges":{"source":"iana"},"multipart/digest":{"source":"iana"},"multipart/encrypted":{"source":"iana","compressible":false},"multipart/form-data":{"source":"iana","compressible":false},"multipart/header-set":{"source":"iana"},"multipart/mixed":{"source":"iana"},"multipart/multilingual":{"source":"iana"},"multipart/parallel":{"source":"iana"},"multipart/related":{"source":"iana","compressible":false},"multipart/report":{"source":"iana"},"multipart/signed":{"source":"iana","compressible":false},"multipart/vnd.bint.med-plus":{"source":"iana"},"multipart/voice-message":{"source":"iana"},"multipart/x-mixed-replace":{"source":"iana"},"text/1d-interleaved-parityfec":{"source":"iana"},"text/cache-manifest":{"source":"iana","compressible":true,"extensions":["appcache","manifest"]},"text/calendar":{"source":"iana","extensions":["ics","ifb"]},"text/calender":{"compressible":true},"text/cmd":{"compressible":true},"text/coffeescript":{"extensions":["coffee","litcoffee"]},"text/cql":{"source":"iana"},"text/cql-expression":{"source":"iana"},"text/cql-identifier":{"source":"iana"},"text/css":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["css"]},"text/csv":{"source":"iana","compressible":true,"extensions":["csv"]},"text/csv-schema":{"source":"iana"},"text/directory":{"source":"iana"},"text/dns":{"source":"iana"},"text/ecmascript":{"source":"iana"},"text/encaprtp":{"source":"iana"},"text/enriched":{"source":"iana"},"text/fhirpath":{"source":"iana"},"text/flexfec":{"source":"iana"},"text/fwdred":{"source":"iana"},"text/gff3":{"source":"iana"},"text/grammar-ref-list":{"source":"iana"},"text/html":{"source":"iana","compressible":true,"extensions":["html","htm","shtml"]},"text/jade":{"extensions":["jade"]},"text/javascript":{"source":"iana","compressible":true},"text/jcr-cnd":{"source":"iana"},"text/jsx":{"compressible":true,"extensions":["jsx"]},"text/less":{"compressible":true,"extensions":["less"]},"text/markdown":{"source":"iana","compressible":true,"extensions":["markdown","md"]},"text/mathml":{"source":"nginx","extensions":["mml"]},"text/mdx":{"compressible":true,"extensions":["mdx"]},"text/mizar":{"source":"iana"},"text/n3":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["n3"]},"text/parameters":{"source":"iana","charset":"UTF-8"},"text/parityfec":{"source":"iana"},"text/plain":{"source":"iana","compressible":true,"extensions":["txt","text","conf","def","list","log","in","ini"]},"text/provenance-notation":{"source":"iana","charset":"UTF-8"},"text/prs.fallenstein.rst":{"source":"iana"},"text/prs.lines.tag":{"source":"iana","extensions":["dsc"]},"text/prs.prop.logic":{"source":"iana"},"text/raptorfec":{"source":"iana"},"text/red":{"source":"iana"},"text/rfc822-headers":{"source":"iana"},"text/richtext":{"source":"iana","compressible":true,"extensions":["rtx"]},"text/rtf":{"source":"iana","compressible":true,"extensions":["rtf"]},"text/rtp-enc-aescm128":{"source":"iana"},"text/rtploopback":{"source":"iana"},"text/rtx":{"source":"iana"},"text/sgml":{"source":"iana","extensions":["sgml","sgm"]},"text/shaclc":{"source":"iana"},"text/shex":{"source":"iana","extensions":["shex"]},"text/slim":{"extensions":["slim","slm"]},"text/spdx":{"source":"iana","extensions":["spdx"]},"text/strings":{"source":"iana"},"text/stylus":{"extensions":["stylus","styl"]},"text/t140":{"source":"iana"},"text/tab-separated-values":{"source":"iana","compressible":true,"extensions":["tsv"]},"text/troff":{"source":"iana","extensions":["t","tr","roff","man","me","ms"]},"text/turtle":{"source":"iana","charset":"UTF-8","extensions":["ttl"]},"text/ulpfec":{"source":"iana"},"text/uri-list":{"source":"iana","compressible":true,"extensions":["uri","uris","urls"]},"text/vcard":{"source":"iana","compressible":true,"extensions":["vcard"]},"text/vnd.a":{"source":"iana"},"text/vnd.abc":{"source":"iana"},"text/vnd.ascii-art":{"source":"iana"},"text/vnd.curl":{"source":"iana","extensions":["curl"]},"text/vnd.curl.dcurl":{"source":"apache","extensions":["dcurl"]},"text/vnd.curl.mcurl":{"source":"apache","extensions":["mcurl"]},"text/vnd.curl.scurl":{"source":"apache","extensions":["scurl"]},"text/vnd.debian.copyright":{"source":"iana","charset":"UTF-8"},"text/vnd.dmclientscript":{"source":"iana"},"text/vnd.dvb.subtitle":{"source":"iana","extensions":["sub"]},"text/vnd.esmertec.theme-descriptor":{"source":"iana","charset":"UTF-8"},"text/vnd.familysearch.gedcom":{"source":"iana","extensions":["ged"]},"text/vnd.ficlab.flt":{"source":"iana"},"text/vnd.fly":{"source":"iana","extensions":["fly"]},"text/vnd.fmi.flexstor":{"source":"iana","extensions":["flx"]},"text/vnd.gml":{"source":"iana"},"text/vnd.graphviz":{"source":"iana","extensions":["gv"]},"text/vnd.hans":{"source":"iana"},"text/vnd.hgl":{"source":"iana"},"text/vnd.in3d.3dml":{"source":"iana","extensions":["3dml"]},"text/vnd.in3d.spot":{"source":"iana","extensions":["spot"]},"text/vnd.iptc.newsml":{"source":"iana"},"text/vnd.iptc.nitf":{"source":"iana"},"text/vnd.latex-z":{"source":"iana"},"text/vnd.motorola.reflex":{"source":"iana"},"text/vnd.ms-mediapackage":{"source":"iana"},"text/vnd.net2phone.commcenter.command":{"source":"iana"},"text/vnd.radisys.msml-basic-layout":{"source":"iana"},"text/vnd.senx.warpscript":{"source":"iana"},"text/vnd.si.uricatalogue":{"source":"iana"},"text/vnd.sosi":{"source":"iana"},"text/vnd.sun.j2me.app-descriptor":{"source":"iana","charset":"UTF-8","extensions":["jad"]},"text/vnd.trolltech.linguist":{"source":"iana","charset":"UTF-8"},"text/vnd.wap.si":{"source":"iana"},"text/vnd.wap.sl":{"source":"iana"},"text/vnd.wap.wml":{"source":"iana","extensions":["wml"]},"text/vnd.wap.wmlscript":{"source":"iana","extensions":["wmls"]},"text/vtt":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["vtt"]},"text/x-asm":{"source":"apache","extensions":["s","asm"]},"text/x-c":{"source":"apache","extensions":["c","cc","cxx","cpp","h","hh","dic"]},"text/x-component":{"source":"nginx","extensions":["htc"]},"text/x-fortran":{"source":"apache","extensions":["f","for","f77","f90"]},"text/x-gwt-rpc":{"compressible":true},"text/x-handlebars-template":{"extensions":["hbs"]},"text/x-java-source":{"source":"apache","extensions":["java"]},"text/x-jquery-tmpl":{"compressible":true},"text/x-lua":{"extensions":["lua"]},"text/x-markdown":{"compressible":true,"extensions":["mkd"]},"text/x-nfo":{"source":"apache","extensions":["nfo"]},"text/x-opml":{"source":"apache","extensions":["opml"]},"text/x-org":{"compressible":true,"extensions":["org"]},"text/x-pascal":{"source":"apache","extensions":["p","pas"]},"text/x-processing":{"compressible":true,"extensions":["pde"]},"text/x-sass":{"extensions":["sass"]},"text/x-scss":{"extensions":["scss"]},"text/x-setext":{"source":"apache","extensions":["etx"]},"text/x-sfv":{"source":"apache","extensions":["sfv"]},"text/x-suse-ymp":{"compressible":true,"extensions":["ymp"]},"text/x-uuencode":{"source":"apache","extensions":["uu"]},"text/x-vcalendar":{"source":"apache","extensions":["vcs"]},"text/x-vcard":{"source":"apache","extensions":["vcf"]},"text/xml":{"source":"iana","compressible":true,"extensions":["xml"]},"text/xml-external-parsed-entity":{"source":"iana"},"text/yaml":{"compressible":true,"extensions":["yaml","yml"]},"video/1d-interleaved-parityfec":{"source":"iana"},"video/3gpp":{"source":"iana","extensions":["3gp","3gpp"]},"video/3gpp-tt":{"source":"iana"},"video/3gpp2":{"source":"iana","extensions":["3g2"]},"video/av1":{"source":"iana"},"video/bmpeg":{"source":"iana"},"video/bt656":{"source":"iana"},"video/celb":{"source":"iana"},"video/dv":{"source":"iana"},"video/encaprtp":{"source":"iana"},"video/ffv1":{"source":"iana"},"video/flexfec":{"source":"iana"},"video/h261":{"source":"iana","extensions":["h261"]},"video/h263":{"source":"iana","extensions":["h263"]},"video/h263-1998":{"source":"iana"},"video/h263-2000":{"source":"iana"},"video/h264":{"source":"iana","extensions":["h264"]},"video/h264-rcdo":{"source":"iana"},"video/h264-svc":{"source":"iana"},"video/h265":{"source":"iana"},"video/iso.segment":{"source":"iana","extensions":["m4s"]},"video/jpeg":{"source":"iana","extensions":["jpgv"]},"video/jpeg2000":{"source":"iana"},"video/jpm":{"source":"apache","extensions":["jpm","jpgm"]},"video/jxsv":{"source":"iana"},"video/mj2":{"source":"iana","extensions":["mj2","mjp2"]},"video/mp1s":{"source":"iana"},"video/mp2p":{"source":"iana"},"video/mp2t":{"source":"iana","extensions":["ts"]},"video/mp4":{"source":"iana","compressible":false,"extensions":["mp4","mp4v","mpg4"]},"video/mp4v-es":{"source":"iana"},"video/mpeg":{"source":"iana","compressible":false,"extensions":["mpeg","mpg","mpe","m1v","m2v"]},"video/mpeg4-generic":{"source":"iana"},"video/mpv":{"source":"iana"},"video/nv":{"source":"iana"},"video/ogg":{"source":"iana","compressible":false,"extensions":["ogv"]},"video/parityfec":{"source":"iana"},"video/pointer":{"source":"iana"},"video/quicktime":{"source":"iana","compressible":false,"extensions":["qt","mov"]},"video/raptorfec":{"source":"iana"},"video/raw":{"source":"iana"},"video/rtp-enc-aescm128":{"source":"iana"},"video/rtploopback":{"source":"iana"},"video/rtx":{"source":"iana"},"video/scip":{"source":"iana"},"video/smpte291":{"source":"iana"},"video/smpte292m":{"source":"iana"},"video/ulpfec":{"source":"iana"},"video/vc1":{"source":"iana"},"video/vc2":{"source":"iana"},"video/vnd.cctv":{"source":"iana"},"video/vnd.dece.hd":{"source":"iana","extensions":["uvh","uvvh"]},"video/vnd.dece.mobile":{"source":"iana","extensions":["uvm","uvvm"]},"video/vnd.dece.mp4":{"source":"iana"},"video/vnd.dece.pd":{"source":"iana","extensions":["uvp","uvvp"]},"video/vnd.dece.sd":{"source":"iana","extensions":["uvs","uvvs"]},"video/vnd.dece.video":{"source":"iana","extensions":["uvv","uvvv"]},"video/vnd.directv.mpeg":{"source":"iana"},"video/vnd.directv.mpeg-tts":{"source":"iana"},"video/vnd.dlna.mpeg-tts":{"source":"iana"},"video/vnd.dvb.file":{"source":"iana","extensions":["dvb"]},"video/vnd.fvt":{"source":"iana","extensions":["fvt"]},"video/vnd.hns.video":{"source":"iana"},"video/vnd.iptvforum.1dparityfec-1010":{"source":"iana"},"video/vnd.iptvforum.1dparityfec-2005":{"source":"iana"},"video/vnd.iptvforum.2dparityfec-1010":{"source":"iana"},"video/vnd.iptvforum.2dparityfec-2005":{"source":"iana"},"video/vnd.iptvforum.ttsavc":{"source":"iana"},"video/vnd.iptvforum.ttsmpeg2":{"source":"iana"},"video/vnd.motorola.video":{"source":"iana"},"video/vnd.motorola.videop":{"source":"iana"},"video/vnd.mpegurl":{"source":"iana","extensions":["mxu","m4u"]},"video/vnd.ms-playready.media.pyv":{"source":"iana","extensions":["pyv"]},"video/vnd.nokia.interleaved-multimedia":{"source":"iana"},"video/vnd.nokia.mp4vr":{"source":"iana"},"video/vnd.nokia.videovoip":{"source":"iana"},"video/vnd.objectvideo":{"source":"iana"},"video/vnd.radgamettools.bink":{"source":"iana"},"video/vnd.radgamettools.smacker":{"source":"iana"},"video/vnd.sealed.mpeg1":{"source":"iana"},"video/vnd.sealed.mpeg4":{"source":"iana"},"video/vnd.sealed.swf":{"source":"iana"},"video/vnd.sealedmedia.softseal.mov":{"source":"iana"},"video/vnd.uvvu.mp4":{"source":"iana","extensions":["uvu","uvvu"]},"video/vnd.vivo":{"source":"iana","extensions":["viv"]},"video/vnd.youtube.yt":{"source":"iana"},"video/vp8":{"source":"iana"},"video/vp9":{"source":"iana"},"video/webm":{"source":"apache","compressible":false,"extensions":["webm"]},"video/x-f4v":{"source":"apache","extensions":["f4v"]},"video/x-fli":{"source":"apache","extensions":["fli"]},"video/x-flv":{"source":"apache","compressible":false,"extensions":["flv"]},"video/x-m4v":{"source":"apache","extensions":["m4v"]},"video/x-matroska":{"source":"apache","compressible":false,"extensions":["mkv","mk3d","mks"]},"video/x-mng":{"source":"apache","extensions":["mng"]},"video/x-ms-asf":{"source":"apache","extensions":["asf","asx"]},"video/x-ms-vob":{"source":"apache","extensions":["vob"]},"video/x-ms-wm":{"source":"apache","extensions":["wm"]},"video/x-ms-wmv":{"source":"apache","compressible":false,"extensions":["wmv"]},"video/x-ms-wmx":{"source":"apache","extensions":["wmx"]},"video/x-ms-wvx":{"source":"apache","extensions":["wvx"]},"video/x-msvideo":{"source":"apache","extensions":["avi"]},"video/x-sgi-movie":{"source":"apache","extensions":["movie"]},"video/x-smv":{"source":"apache","extensions":["smv"]},"x-conference/x-cooltalk":{"source":"apache","extensions":["ice"]},"x-shader/x-fragment":{"compressible":true},"x-shader/x-vertex":{"compressible":true}}')},49863:e=>{"use strict";e.exports={i8:"4.8.1"}}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),i=t.X(0,[638],()=>s(36093));module.exports=i})();