"use strict";(()=>{var e={};e.id=2,e.ids=[2],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},6113:e=>{e.exports=require("crypto")},40868:(e,r,a)=>{a.r(r),a.d(r,{headerHooks:()=>f,originalPathname:()=>v,patchFetch:()=>E,requestAsyncStorage:()=>w,routeModule:()=>p,serverHooks:()=>h,staticGenerationAsyncStorage:()=>g,staticGenerationBailout:()=>y});var t={};a.r(t),a.d(t,{POST:()=>m});var s=a(95419),i=a(69108),n=a(99678),o=a(78070),l=a(88302),d=a(68505),c=a(20075);let u=l.z.object({name:l.z.string().min(1,"Name is required").max(100),email:l.z.string().email("Invalid email address"),password:l.z.string().min(6,"Password must be at least 6 characters")});async function m(e){try{let r=await e.json(),{name:a,email:t,password:s}=u.parse(r);if(await d._B.user.findUnique({where:{email:t}}))return o.Z.json({error:"User with this email already exists"},{status:409});let i=await (0,c.c_)(s),n=await d._B.user.create({data:{name:a,email:t,password:i},select:{id:!0,name:!0,email:!0,createdAt:!0}});return o.Z.json({message:"User created successfully",user:n},{status:201})}catch(e){if(e instanceof l.z.ZodError)return o.Z.json({error:"Validation failed",details:e.errors},{status:400});return console.error("Registration error:",e),o.Z.json({error:"Internal server error"},{status:500})}}let p=new s.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/auth/register/route",pathname:"/api/auth/register",filename:"route",bundlePath:"app/api/auth/register/route"},resolvedPagePath:"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/app/api/auth/register/route.ts",nextConfigOutput:"",userland:t}),{requestAsyncStorage:w,staticGenerationAsyncStorage:g,serverHooks:h,headerHooks:f,staticGenerationBailout:y}=p,v="/api/auth/register/route";function E(){return(0,n.patchFetch)({serverHooks:h,staticGenerationAsyncStorage:g})}},20075:(e,r,a)=>{a.d(r,{Lz:()=>c,c_:()=>u});var t=a(54896),s=a(10375),i=a(50694),n=a(86485),o=a(6521),l=a.n(o),d=a(68505);let c={adapter:(0,t.N)(d._B),providers:[(0,s.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,i.Z)({clientId:process.env.GITHUB_ID,clientSecret:process.env.GITHUB_SECRET}),(0,n.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;let r=await d._B.user.findUnique({where:{email:e.email}});return r&&r.password&&await l().compare(e.password,r.password)?{id:r.id,email:r.email,name:r.name,image:r.image}:null}})],session:{strategy:"jwt"},pages:{signIn:"/auth/signin"},callbacks:{jwt:async({token:e,user:r})=>(r&&(e.id=r.id),e),session:async({session:e,token:r})=>(r&&(e.user.id=r.id),e),async signIn({user:e,account:r,profile:a}){if(r?.provider!=="credentials"&&e.email)try{await d._B.user.findUnique({where:{email:e.email}})||await d._B.user.create({data:{email:e.email,name:e.name||"",image:e.image,emailVerified:new Date}})}catch(e){console.error("Error creating user profile:",e)}return!0}},events:{async createUser({user:e}){console.log("New user created:",e.email)},async signIn({user:e,account:r,isNewUser:a}){console.log("User signed in:",e.email,"Provider:",r?.provider)}},debug:!1};async function u(e){return l().hash(e,12)}},68505:(e,r,a)=>{a.d(r,{Lb:()=>i,_B:()=>s});let t=require("@prisma/client"),s=globalThis.prisma??new t.PrismaClient({log:["query","error","warn"],errorFormat:"pretty"});function i(e){if(console.error("Database error:",e),"P2002"===e.code)throw Error("A record with this information already exists");if("P2025"===e.code)throw Error("Record not found");if("P2003"===e.code)throw Error("Foreign key constraint failed");throw Error("Database operation failed")}}};var r=require("../../../../webpack-runtime.js");r.C(e);var a=e=>r(r.s=e),t=r.X(0,[638,543,793],()=>a(40868));module.exports=t})();