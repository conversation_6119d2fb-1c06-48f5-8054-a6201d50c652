"use strict";(()=>{var e={};e.id=817,e.ids=[817],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},35900:e=>{e.exports=require("pg")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},41808:e=>{e.exports=require("net")},77282:e=>{e.exports=require("process")},12781:e=>{e.exports=require("stream")},71576:e=>{e.exports=require("string_decoder")},39512:e=>{e.exports=require("timers")},24404:e=>{e.exports=require("tls")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},77142:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>x,originalPathname:()=>b,patchFetch:()=>q,requestAsyncStorage:()=>y,routeModule:()=>g,serverHooks:()=>f,staticGenerationAsyncStorage:()=>C,staticGenerationBailout:()=>I});var n={};r.r(n),r.d(n,{GET:()=>w,POST:()=>p});var a=r(95419),s=r(69108),o=r(99678),i=r(78070),c=r(98480),u=r(55949);class d{static{this.DEFAULT_MAX_ROWS=1e3}static{this.DEFAULT_TIMEOUT=3e4}static{this.MAX_QUERY_LENGTH=5e4}static async executeQuery(e){let t=Date.now(),r=this.generateQueryHash(e.sql);try{let n=await this.validateQuery(e);if(!n.isValid)return{success:!1,error:`Query validation failed: ${n.errors.join(", ")}`,metadata:{connectionId:e.connectionId,executedAt:new Date,executionTime:Date.now()-t,rowsReturned:0,queryHash:r}};let a=await u.ConnectionService.getConnectionById(e.connectionId);if(!a)return{success:!1,error:"Database connection not found",metadata:{connectionId:e.connectionId,executedAt:new Date,executionTime:Date.now()-t,rowsReturned:0,queryHash:r}};let s=c.ConnectorFactory.createConnector(a.databaseType,await this.getConnectionConfig(e.connectionId),e.connectionId),o=e.sql,i=e.maxRows||this.DEFAULT_MAX_ROWS;"SELECT"!==n.queryType||this.hasLimitClause(e.sql)||(o=this.addLimitClause(e.sql,i,a.databaseType));let d=await this.executeWithTimeout(()=>s.executeQuery(o,e.params),e.timeout||this.DEFAULT_TIMEOUT),l=Date.now()-t,h={success:!0,data:{rows:d.rows,fields:d.fields,rowCount:d.rowCount,executionTime:d.executionTime,affectedRows:"SELECT"!==n.queryType?d.rowCount:void 0},warnings:n.warnings.length>0?n.warnings:void 0,metadata:{connectionId:e.connectionId,executedAt:new Date,executionTime:l,rowsReturned:d.rows.length,queryHash:r}};return await this.logQueryExecution(e,h),h}catch(s){let n=Date.now()-t,a={success:!1,error:s instanceof Error?s.message:"Unknown error occurred",metadata:{connectionId:e.connectionId,executedAt:new Date,executionTime:n,rowsReturned:0,queryHash:r}};return await this.logQueryExecution(e,a),a}}static async validateQuery(e){let t=[],r=[];e.sql&&0!==e.sql.trim().length||t.push("Query cannot be empty"),e.sql.length>this.MAX_QUERY_LENGTH&&t.push(`Query too long. Maximum length is ${this.MAX_QUERY_LENGTH} characters`);let n=this.detectQueryType(e.sql);for(let r of[/drop\s+database/gi,/drop\s+schema/gi,/truncate\s+table/gi,/delete\s+from\s+\w+\s*;?\s*$/gi,/update\s+\w+\s+set\s+.*\s*;?\s*$/gi])if(r.test(e.sql)){t.push("Query contains potentially dangerous operations");break}let a=this.estimateQueryComplexity(e.sql);return"HIGH"===a&&r.push("This query appears to be complex and may take longer to execute"),e.sql.split(";").filter(e=>e.trim().length>0).length>1&&t.push("Multiple statements are not allowed. Please execute one statement at a time"),{isValid:0===t.length,errors:t,warnings:r,queryType:n,isReadOnly:"SELECT"===n,estimatedComplexity:a}}static async getConnectionConfig(e){let t=await u.ConnectionService.getConnectionString(e);if(!t)throw Error("Connection string not found");return this.parseConnectionString(t)}static parseConnectionString(e){if(e.startsWith("postgresql://")||e.startsWith("postgres://")){let t=new URL(e);return{host:t.hostname,port:parseInt(t.port)||5432,database:t.pathname.substring(1),username:t.username,password:t.password,ssl:"require"===t.searchParams.get("sslmode")}}if(e.startsWith("mysql://")){let t=new URL(e);return{host:t.hostname,port:parseInt(t.port)||3306,database:t.pathname.substring(1),username:t.username,password:t.password,ssl:"true"===t.searchParams.get("ssl")}}{let t=new Map;return e.split(";").forEach(e=>{let[r,n]=e.split("=");r&&n&&t.set(r.trim().toLowerCase(),n.trim())}),{host:t.get("host")||"localhost",port:parseInt(t.get("port")||"5432"),database:t.get("database")||"",username:t.get("username")||t.get("user")||"",password:t.get("password")||"",ssl:"true"===t.get("ssl")}}}static detectQueryType(e){let t=e.trim().toLowerCase();return t.startsWith("select")?"SELECT":t.startsWith("insert")?"INSERT":t.startsWith("update")?"UPDATE":t.startsWith("delete")?"DELETE":t.startsWith("create")?"CREATE":t.startsWith("alter")?"ALTER":t.startsWith("drop")?"DROP":"UNKNOWN"}static estimateQueryComplexity(e){let t=e.toLowerCase(),r=0;t.includes("join")&&(r+=1),(t.includes("subquery")||t.includes("exists"))&&(r+=2),t.includes("group by")&&(r+=1),t.includes("order by")&&(r+=1),t.includes("having")&&(r+=1),t.includes("union")&&(r+=2),(t.includes("window")||t.includes("over("))&&(r+=2);let n=t.match(/from\s+\w+|join\s+\w+/g);return(n&&n.length>2&&(r+=1),r>=4)?"HIGH":r>=2?"MEDIUM":"LOW"}static hasLimitClause(e){return/\blimit\s+\d+/gi.test(e)}static addLimitClause(e,t,r){let n=e.trim();return"MYSQL"===r||"POSTGRESQL"===r?`${n} LIMIT ${t}`:n}static generateQueryHash(e){let t=0;for(let r=0;r<e.length;r++)t=(t<<5)-t+e.charCodeAt(r),t&=t;return Math.abs(t).toString(16)}static async executeWithTimeout(e,t){let r=new Promise((e,r)=>setTimeout(()=>r(Error(`Query execution timeout after ${t}ms`)),t));return Promise.race([e(),r])}static async logQueryExecution(e,t){try{console.log("Query execution logged:",{connectionId:e.connectionId,userId:e.userId,success:t.success,executionTime:t.metadata.executionTime,rowsReturned:t.metadata.rowsReturned,queryHash:t.metadata.queryHash})}catch(e){console.error("Failed to log query execution:",e)}}}var l=r(88302);let h=l.z.object({connectionId:l.z.string().cuid(),sql:l.z.string().min(1).max(5e4),params:l.z.array(l.z.any()).optional(),maxRows:l.z.number().int().min(1).max(1e4).optional(),timeout:l.z.number().int().min(1e3).max(3e5).optional(),userId:l.z.string().cuid().optional(),saveToHistory:l.z.boolean().optional().default(!0)});async function p(e){try{let t=await e.json(),r=h.parse(t),n=await d.executeQuery({connectionId:r.connectionId,sql:r.sql,params:r.params,maxRows:r.maxRows,timeout:r.timeout,userId:r.userId});if(n.success&&r.saveToHistory&&r.userId)try{await m({userId:r.userId,connectionId:r.connectionId,sql:r.sql,result:n})}catch(e){console.error("Failed to save query to history:",e)}return i.Z.json(n)}catch(e){if(e instanceof l.z.ZodError)return i.Z.json({success:!1,error:"Validation failed",details:e.errors},{status:400});return console.error("POST /api/queries/execute error:",e),i.Z.json({success:!1,error:e instanceof Error?e.message:"Internal server error",metadata:{connectionId:"",executedAt:new Date,executionTime:0,rowsReturned:0,queryHash:""}},{status:500})}}async function m(e){let{QueryService:t}=await r.e(133).then(r.bind(r,67133)),{ConnectionService:n}=await Promise.resolve().then(r.bind(r,55949));try{let r;let a=await n.getConnectionById(e.connectionId);if(!a)throw Error("Connection not found");try{r=(await t.getUserSessions(e.userId,1)).find(t=>t.databaseConnectionId===e.connectionId&&t.isActive)||await t.createSession({userId:e.userId,databaseConnectionId:e.connectionId,name:`Query Session - ${new Date().toLocaleString()}`})}catch(n){r=await t.createSession({userId:e.userId,databaseConnectionId:e.connectionId,name:`Query Session - ${new Date().toLocaleString()}`})}await t.createGeneratedQuery({sessionId:r.id,userId:e.userId,userInput:`Executed: ${e.sql.substring(0,100)}${e.sql.length>100?"...":""}`,generatedSQL:e.sql,explanation:e.result.success?`Query executed successfully. Returned ${e.result.data?.rowCount||0} rows in ${e.result.metadata.executionTime}ms.`:`Query failed: ${e.result.error}`,databaseType:a.databaseType,executionTime:e.result.metadata.executionTime,rowsAffected:e.result.data?.affectedRows,performanceData:{executionTime:e.result.metadata.executionTime,rowsReturned:e.result.metadata.rowsReturned,queryHash:e.result.metadata.queryHash,success:e.result.success}})}catch(e){throw console.error("Failed to save query to history:",e),e}}async function w(e){try{let{searchParams:t}=new URL(e.url),r=t.get("connectionId"),n=t.get("sql");if(!r||!n)return i.Z.json({error:"connectionId and sql parameters are required"},{status:400});let a=await d.validateQuery({connectionId:r,sql:decodeURIComponent(n)});return i.Z.json({validation:a,timestamp:new Date().toISOString()})}catch(e){return console.error("GET /api/queries/execute/validate error:",e),i.Z.json({error:"Internal server error"},{status:500})}}let g=new a.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/queries/execute/route",pathname:"/api/queries/execute",filename:"route",bundlePath:"app/api/queries/execute/route"},resolvedPagePath:"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/app/api/queries/execute/route.ts",nextConfigOutput:"",userland:n}),{requestAsyncStorage:y,staticGenerationAsyncStorage:C,serverHooks:f,headerHooks:x,staticGenerationBailout:I}=g,b="/api/queries/execute/route";function q(){return(0,o.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:C})}},68505:(e,t,r)=>{r.d(t,{Lb:()=>s,_B:()=>a});let n=require("@prisma/client"),a=globalThis.prisma??new n.PrismaClient({log:["query","error","warn"],errorFormat:"pretty"});function s(e){if(console.error("Database error:",e),"P2002"===e.code)throw Error("A record with this information already exists");if("P2025"===e.code)throw Error("Record not found");if("P2003"===e.code)throw Error("Foreign key constraint failed");throw Error("Database operation failed")}},55949:(e,t,r)=>{r.d(t,{ConnectionService:()=>s});var n=r(68505);function a(e){try{let t=Buffer.from(e).toString("base64");return"enc:"+t}catch(e){throw console.error("Encryption failed:",e),Error("Failed to encrypt data")}}r(6113);class s{static async createConnection(e){try{let t=a(e.connectionString),{connectionString:r,...s}=e;return await n._B.databaseConnection.create({data:{...s,connectionStringHash:t}})}catch(e){(0,n.Lb)(e)}}static async getConnectionById(e){try{return await n._B.databaseConnection.findUnique({where:{id:e},include:{user:{select:{id:!0,name:!0,email:!0}},team:{select:{id:!0,name:!0}}}})}catch(e){(0,n.Lb)(e)}}static async getUserConnections(e){try{return await n._B.databaseConnection.findMany({where:{userId:e,isActive:!0},orderBy:{lastConnectedAt:"desc"}})}catch(e){(0,n.Lb)(e)}}static async getTeamConnections(e){try{return await n._B.databaseConnection.findMany({where:{teamId:e,isActive:!0},include:{user:{select:{id:!0,name:!0,email:!0}}},orderBy:{lastConnectedAt:"desc"}})}catch(e){(0,n.Lb)(e)}}static async updateConnection(e,t){try{let r={...t};return t.connectionString&&(r.connectionStringHash=a(t.connectionString),delete r.connectionString),await n._B.databaseConnection.update({where:{id:e},data:r})}catch(e){(0,n.Lb)(e)}}static async updateLastConnected(e){try{await n._B.databaseConnection.update({where:{id:e},data:{lastConnectedAt:new Date}})}catch(e){(0,n.Lb)(e)}}static async deleteConnection(e){try{await n._B.databaseConnection.delete({where:{id:e}})}catch(e){(0,n.Lb)(e)}}static async getConnectionString(e){try{let t=await n._B.databaseConnection.findUnique({where:{id:e},select:{connectionStringHash:!0}});if(!t)return null;return function(e){try{if(!e.startsWith("enc:"))throw Error("Invalid encrypted data format");let t=e.substring(4);return Buffer.from(t,"base64").toString("utf8")}catch(e){throw console.error("Decryption failed:",e),Error("Failed to decrypt data")}}(t.connectionStringHash)}catch(e){return console.error("Failed to decrypt connection string:",e),null}}static async testConnection(e){try{let t=await this.getConnectionById(e);if(!t)return!1;let n=await this.getConnectionString(e);if(!n)return!1;let a=this.parseConnectionString(n),{ConnectorFactory:s}=await Promise.all([r.e(69),r.e(168)]).then(r.bind(r,98480)),o=s.createConnector(t.databaseType,a),i=await o.testConnection();return await o.disconnect(),i&&await this.updateLastConnected(e),i}catch(e){return console.error("Connection test failed:",e),!1}}static async getConnectionStats(e){try{let[t,r,a]=await Promise.all([n._B.generatedQuery.count({where:{session:{databaseConnectionId:e}}}),n._B.querySession.count({where:{databaseConnectionId:e}}),n._B.querySession.count({where:{databaseConnectionId:e,createdAt:{gte:new Date(Date.now()-6048e5)}}})]);return{totalQueries:t,totalSessions:r,recentActivity:a}}catch(e){(0,n.Lb)(e)}}static parseConnectionString(e){try{if(e.startsWith("postgresql://")||e.startsWith("postgres://"))return this.parsePostgreSQLConnectionString(e);if(e.startsWith("mysql://"))return this.parseMySQLConnectionString(e);return this.parseGenericConnectionString(e)}catch(e){throw Error(`Invalid connection string format: ${e}`)}}static parsePostgreSQLConnectionString(e){let t=new URL(e);return{host:t.hostname,port:parseInt(t.port)||5432,database:t.pathname.substring(1),username:t.username,password:t.password,ssl:"require"===t.searchParams.get("sslmode")}}static parseMySQLConnectionString(e){let t=new URL(e);return{host:t.hostname,port:parseInt(t.port)||3306,database:t.pathname.substring(1),username:t.username,password:t.password,ssl:"true"===t.searchParams.get("ssl")}}static parseGenericConnectionString(e){let t=new Map;return e.split(";").forEach(e=>{let[r,n]=e.split("=");r&&n&&t.set(r.trim().toLowerCase(),n.trim())}),{host:t.get("host")||"localhost",port:parseInt(t.get("port")||"5432"),database:t.get("database")||"",username:t.get("username")||t.get("user")||"",password:t.get("password")||"",ssl:"true"===t.get("ssl")}}static async getConnectionSchema(e){try{let t=await this.getConnectionById(e);if(!t)throw Error("Connection not found");let n=await this.getConnectionString(e);if(!n)throw Error("Connection string not found");let a=this.parseConnectionString(n),{ConnectorFactory:s}=await Promise.all([r.e(69),r.e(168)]).then(r.bind(r,98480)),o=s.createConnector(t.databaseType,a,e),i=await o.getSchema();return await this.updateConnection(e,{schemaMetadata:i}),i}catch(e){(0,n.Lb)(e)}}}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[638,543,69,168],()=>r(77142));module.exports=n})();