"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/queries/route";
exports.ids = ["app/api/queries/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fqueries%2Froute&page=%2Fapi%2Fqueries%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fqueries%2Froute.ts&appDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fquery-craft-studio-2%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fquery-craft-studio-2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fqueries%2Froute&page=%2Fapi%2Fqueries%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fqueries%2Froute.ts&appDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fquery-craft-studio-2%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fquery-craft-studio-2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_alexandresimasmaciel_Documents_ia_sistemas_query_craft_studio_2_src_app_api_queries_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/queries/route.ts */ \"(rsc)/./src/app/api/queries/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/queries/route\",\n        pathname: \"/api/queries\",\n        filename: \"route\",\n        bundlePath: \"app/api/queries/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/app/api/queries/route.ts\",\n    nextConfigOutput,\n    userland: _Users_alexandresimasmaciel_Documents_ia_sistemas_query_craft_studio_2_src_app_api_queries_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/queries/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fqueries%2Froute&page=%2Fapi%2Fqueries%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fqueries%2Froute.ts&appDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fquery-craft-studio-2%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fquery-craft-studio-2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/queries/route.ts":
/*!**************************************!*\
  !*** ./src/app/api/queries/route.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _lib_database_services_query_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/database/services/query-service */ \"(rsc)/./src/lib/database/services/query-service.ts\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/dist/esm/index.js\");\n\n\n\n// Validation schemas\nconst createQuerySchema = zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n    sessionId: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().cuid(),\n    userId: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().cuid(),\n    userInput: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().min(1),\n    generatedSQL: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().min(1),\n    explanation: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().optional(),\n    databaseType: zod__WEBPACK_IMPORTED_MODULE_2__.z[\"enum\"]([\n        \"MYSQL\",\n        \"POSTGRESQL\"\n    ]),\n    executionTime: zod__WEBPACK_IMPORTED_MODULE_2__.z.number().int().min(0).optional(),\n    rowsAffected: zod__WEBPACK_IMPORTED_MODULE_2__.z.number().int().min(0).optional(),\n    performanceData: zod__WEBPACK_IMPORTED_MODULE_2__.z.any().optional(),\n    optimizationTips: zod__WEBPACK_IMPORTED_MODULE_2__.z.any().optional()\n});\nconst updateQuerySchema = zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n    status: zod__WEBPACK_IMPORTED_MODULE_2__.z[\"enum\"]([\n        \"GENERATED\",\n        \"EXECUTED\",\n        \"FAILED\",\n        \"OPTIMIZED\"\n    ]).optional(),\n    executionTime: zod__WEBPACK_IMPORTED_MODULE_2__.z.number().int().min(0).optional(),\n    rowsAffected: zod__WEBPACK_IMPORTED_MODULE_2__.z.number().int().min(0).optional(),\n    errorMessage: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().optional(),\n    performanceData: zod__WEBPACK_IMPORTED_MODULE_2__.z.any().optional(),\n    userFeedback: zod__WEBPACK_IMPORTED_MODULE_2__.z.number().int().min(1).max(5).optional()\n});\nconst createSessionSchema = zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n    userId: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().cuid(),\n    databaseConnectionId: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().cuid(),\n    name: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().optional(),\n    sessionData: zod__WEBPACK_IMPORTED_MODULE_2__.z.any().optional()\n});\n// GET /api/queries - Get queries or sessions\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const userId = searchParams.get(\"userId\");\n        const sessionId = searchParams.get(\"sessionId\");\n        const type = searchParams.get(\"type\"); // 'queries' or 'sessions'\n        const limit = parseInt(searchParams.get(\"limit\") || \"20\");\n        const databaseType = searchParams.get(\"databaseType\");\n        if (type === \"sessions\" && userId) {\n            const sessions = await _lib_database_services_query_service__WEBPACK_IMPORTED_MODULE_1__.QueryService.getUserSessions(userId, limit);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(sessions);\n        }\n        if (type === \"history\" && userId) {\n            const queries = await _lib_database_services_query_service__WEBPACK_IMPORTED_MODULE_1__.QueryService.getUserQueryHistory(userId, limit, databaseType || undefined);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(queries);\n        }\n        if (sessionId) {\n            const session = await _lib_database_services_query_service__WEBPACK_IMPORTED_MODULE_1__.QueryService.getSessionById(sessionId);\n            if (!session) {\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    error: \"Session not found\"\n                }, {\n                    status: 404\n                });\n            }\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(session);\n        }\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Invalid parameters. Use type=sessions&userId=X or type=history&userId=X or sessionId=X\"\n        }, {\n            status: 400\n        });\n    } catch (error) {\n        console.error(\"GET /api/queries error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/queries - Create a new query or session\nasync function POST(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const type = searchParams.get(\"type\"); // 'query' or 'session'\n        const body = await request.json();\n        if (type === \"session\") {\n            const validatedData = createSessionSchema.parse(body);\n            const session = await _lib_database_services_query_service__WEBPACK_IMPORTED_MODULE_1__.QueryService.createSession(validatedData);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(session, {\n                status: 201\n            });\n        }\n        if (type === \"query\") {\n            const validatedData = createQuerySchema.parse(body);\n            const query = await _lib_database_services_query_service__WEBPACK_IMPORTED_MODULE_1__.QueryService.createGeneratedQuery(validatedData);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(query, {\n                status: 201\n            });\n        }\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Type parameter required (query or session)\"\n        }, {\n            status: 400\n        });\n    } catch (error) {\n        if (error instanceof zod__WEBPACK_IMPORTED_MODULE_2__.z.ZodError) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Validation failed\",\n                details: error.errors\n            }, {\n                status: 400\n            });\n        }\n        console.error(\"POST /api/queries error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n// PUT /api/queries - Update query or session\nasync function PUT(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const id = searchParams.get(\"id\");\n        const type = searchParams.get(\"type\"); // 'query' or 'session'\n        if (!id) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"ID required\"\n            }, {\n                status: 400\n            });\n        }\n        const body = await request.json();\n        if (type === \"query\") {\n            const validatedData = updateQuerySchema.parse(body);\n            if (validatedData.userFeedback) {\n                const query = await _lib_database_services_query_service__WEBPACK_IMPORTED_MODULE_1__.QueryService.addQueryFeedback(id, validatedData.userFeedback);\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(query);\n            } else if (validatedData.status) {\n                // Only call updateQueryExecution if status is provided\n                const executionData = {\n                    status: validatedData.status,\n                    executionTime: validatedData.executionTime,\n                    rowsAffected: validatedData.rowsAffected,\n                    errorMessage: validatedData.errorMessage,\n                    performanceData: validatedData.performanceData\n                };\n                const query = await _lib_database_services_query_service__WEBPACK_IMPORTED_MODULE_1__.QueryService.updateQueryExecution(id, executionData);\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(query);\n            } else {\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    error: \"Either userFeedback or status is required for query updates\"\n                }, {\n                    status: 400\n                });\n            }\n        }\n        if (type === \"session\") {\n            const sessionData = zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n                name: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().optional(),\n                sessionData: zod__WEBPACK_IMPORTED_MODULE_2__.z.any().optional(),\n                isActive: zod__WEBPACK_IMPORTED_MODULE_2__.z.boolean().optional()\n            }).parse(body);\n            const session = await _lib_database_services_query_service__WEBPACK_IMPORTED_MODULE_1__.QueryService.updateSession(id, sessionData);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(session);\n        }\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Type parameter required (query or session)\"\n        }, {\n            status: 400\n        });\n    } catch (error) {\n        if (error instanceof zod__WEBPACK_IMPORTED_MODULE_2__.z.ZodError) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Validation failed\",\n                details: error.errors\n            }, {\n                status: 400\n            });\n        }\n        console.error(\"PUT /api/queries error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n// DELETE /api/queries - Delete session\nasync function DELETE(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const id = searchParams.get(\"id\");\n        if (!id) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Session ID required\"\n            }, {\n                status: 400\n            });\n        }\n        await _lib_database_services_query_service__WEBPACK_IMPORTED_MODULE_1__.QueryService.deleteSession(id);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true\n        });\n    } catch (error) {\n        console.error(\"DELETE /api/queries error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/queries/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database/prisma.ts":
/*!************************************!*\
  !*** ./src/lib/database/prisma.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   disconnectDatabase: () => (/* binding */ disconnectDatabase),\n/* harmony export */   handleDatabaseError: () => (/* binding */ handleDatabaseError),\n/* harmony export */   prisma: () => (/* binding */ prisma),\n/* harmony export */   testDatabaseConnection: () => (/* binding */ testDatabaseConnection)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n    log: [\n        \"query\",\n        \"error\",\n        \"warn\"\n    ],\n    errorFormat: \"pretty\"\n});\nif (true) globalForPrisma.prisma = prisma;\n// Helper function to handle database errors\nfunction handleDatabaseError(error) {\n    console.error(\"Database error:\", error);\n    if (error.code === \"P2002\") {\n        throw new Error(\"A record with this information already exists\");\n    }\n    if (error.code === \"P2025\") {\n        throw new Error(\"Record not found\");\n    }\n    if (error.code === \"P2003\") {\n        throw new Error(\"Foreign key constraint failed\");\n    }\n    throw new Error(\"Database operation failed\");\n}\n// Connection test function\nasync function testDatabaseConnection() {\n    try {\n        await prisma.$connect();\n        await prisma.$queryRaw`SELECT 1`;\n        return true;\n    } catch (error) {\n        console.error(\"Database connection failed:\", error);\n        return false;\n    }\n}\n// Graceful shutdown\nasync function disconnectDatabase() {\n    await prisma.$disconnect();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database/prisma.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database/services/query-service.ts":
/*!****************************************************!*\
  !*** ./src/lib/database/services/query-service.ts ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryService: () => (/* binding */ QueryService)\n/* harmony export */ });\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../prisma */ \"(rsc)/./src/lib/database/prisma.ts\");\n\nclass QueryService {\n    // Create a new query session\n    static async createSession(data) {\n        try {\n            return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.querySession.create({\n                data: {\n                    ...data,\n                    sessionData: data.sessionData || {}\n                }\n            });\n        } catch (error) {\n            (0,_prisma__WEBPACK_IMPORTED_MODULE_0__.handleDatabaseError)(error);\n        }\n    }\n    // Get session by ID\n    static async getSessionById(id) {\n        try {\n            return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.querySession.findUnique({\n                where: {\n                    id\n                },\n                include: {\n                    user: {\n                        select: {\n                            id: true,\n                            name: true,\n                            email: true\n                        }\n                    },\n                    databaseConnection: {\n                        select: {\n                            id: true,\n                            name: true,\n                            databaseType: true\n                        }\n                    },\n                    generatedQueries: {\n                        orderBy: {\n                            createdAt: \"desc\"\n                        },\n                        take: 10\n                    },\n                    chatMessages: {\n                        orderBy: {\n                            createdAt: \"asc\"\n                        }\n                    }\n                }\n            });\n        } catch (error) {\n            (0,_prisma__WEBPACK_IMPORTED_MODULE_0__.handleDatabaseError)(error);\n        }\n    }\n    // Get user sessions\n    static async getUserSessions(userId, limit = 20) {\n        try {\n            return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.querySession.findMany({\n                where: {\n                    userId,\n                    isActive: true\n                },\n                include: {\n                    databaseConnection: {\n                        select: {\n                            id: true,\n                            name: true,\n                            databaseType: true\n                        }\n                    },\n                    _count: {\n                        select: {\n                            generatedQueries: true,\n                            chatMessages: true\n                        }\n                    }\n                },\n                orderBy: {\n                    updatedAt: \"desc\"\n                },\n                take: limit\n            });\n        } catch (error) {\n            (0,_prisma__WEBPACK_IMPORTED_MODULE_0__.handleDatabaseError)(error);\n        }\n    }\n    // Create a generated query\n    static async createGeneratedQuery(data) {\n        try {\n            return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.generatedQuery.create({\n                data\n            });\n        } catch (error) {\n            (0,_prisma__WEBPACK_IMPORTED_MODULE_0__.handleDatabaseError)(error);\n        }\n    }\n    // Update query execution results\n    static async updateQueryExecution(id, data) {\n        try {\n            return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.generatedQuery.update({\n                where: {\n                    id\n                },\n                data\n            });\n        } catch (error) {\n            (0,_prisma__WEBPACK_IMPORTED_MODULE_0__.handleDatabaseError)(error);\n        }\n    }\n    // Add user feedback to query\n    static async addQueryFeedback(id, feedback) {\n        try {\n            return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.generatedQuery.update({\n                where: {\n                    id\n                },\n                data: {\n                    userFeedback: feedback\n                }\n            });\n        } catch (error) {\n            (0,_prisma__WEBPACK_IMPORTED_MODULE_0__.handleDatabaseError)(error);\n        }\n    }\n    // Get user query history\n    static async getUserQueryHistory(userId, limit = 50, databaseType) {\n        try {\n            return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.generatedQuery.findMany({\n                where: {\n                    userId,\n                    ...databaseType && {\n                        databaseType\n                    }\n                },\n                include: {\n                    session: {\n                        select: {\n                            id: true,\n                            name: true,\n                            databaseConnection: {\n                                select: {\n                                    id: true,\n                                    name: true\n                                }\n                            }\n                        }\n                    }\n                },\n                orderBy: {\n                    createdAt: \"desc\"\n                },\n                take: limit\n            });\n        } catch (error) {\n            (0,_prisma__WEBPACK_IMPORTED_MODULE_0__.handleDatabaseError)(error);\n        }\n    }\n    // Add chat message\n    static async addChatMessage(data) {\n        try {\n            return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.chatMessage.create({\n                data\n            });\n        } catch (error) {\n            (0,_prisma__WEBPACK_IMPORTED_MODULE_0__.handleDatabaseError)(error);\n        }\n    }\n    // Get session chat messages\n    static async getSessionMessages(sessionId) {\n        try {\n            return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.chatMessage.findMany({\n                where: {\n                    sessionId\n                },\n                orderBy: {\n                    createdAt: \"asc\"\n                }\n            });\n        } catch (error) {\n            (0,_prisma__WEBPACK_IMPORTED_MODULE_0__.handleDatabaseError)(error);\n        }\n    }\n    // Update session\n    static async updateSession(id, data) {\n        try {\n            return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.querySession.update({\n                where: {\n                    id\n                },\n                data\n            });\n        } catch (error) {\n            (0,_prisma__WEBPACK_IMPORTED_MODULE_0__.handleDatabaseError)(error);\n        }\n    }\n    // Delete session\n    static async deleteSession(id) {\n        try {\n            await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.querySession.delete({\n                where: {\n                    id\n                }\n            });\n        } catch (error) {\n            (0,_prisma__WEBPACK_IMPORTED_MODULE_0__.handleDatabaseError)(error);\n        }\n    }\n    // Get query analytics\n    static async getQueryAnalytics(userId, days = 30) {\n        try {\n            const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);\n            const [totalQueries, successfulQueries, averageExecutionTime, topDatabases, dailyActivity] = await Promise.all([\n                _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.generatedQuery.count({\n                    where: {\n                        userId,\n                        createdAt: {\n                            gte: startDate\n                        }\n                    }\n                }),\n                _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.generatedQuery.count({\n                    where: {\n                        userId,\n                        status: \"EXECUTED\",\n                        createdAt: {\n                            gte: startDate\n                        }\n                    }\n                }),\n                _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.generatedQuery.aggregate({\n                    where: {\n                        userId,\n                        executionTime: {\n                            not: null\n                        },\n                        createdAt: {\n                            gte: startDate\n                        }\n                    },\n                    _avg: {\n                        executionTime: true\n                    }\n                }),\n                _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.generatedQuery.groupBy({\n                    by: [\n                        \"databaseType\"\n                    ],\n                    where: {\n                        userId,\n                        createdAt: {\n                            gte: startDate\n                        }\n                    },\n                    _count: {\n                        id: true\n                    },\n                    orderBy: {\n                        _count: {\n                            id: \"desc\"\n                        }\n                    }\n                }),\n                _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.$queryRaw`\n          SELECT \n            DATE(created_at) as date,\n            COUNT(*) as count\n          FROM generated_queries \n          WHERE user_id = ${userId} \n            AND created_at >= ${startDate}\n          GROUP BY DATE(created_at)\n          ORDER BY date DESC\n        `\n            ]);\n            return {\n                totalQueries,\n                successfulQueries,\n                successRate: totalQueries > 0 ? successfulQueries / totalQueries * 100 : 0,\n                averageExecutionTime: averageExecutionTime._avg.executionTime || 0,\n                topDatabases,\n                dailyActivity\n            };\n        } catch (error) {\n            (0,_prisma__WEBPACK_IMPORTED_MODULE_0__.handleDatabaseError)(error);\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database/services/query-service.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/zod"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fqueries%2Froute&page=%2Fapi%2Fqueries%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fqueries%2Froute.ts&appDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fquery-craft-studio-2%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fquery-craft-studio-2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();