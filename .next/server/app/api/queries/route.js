"use strict";(()=>{var e={};e.id=267,e.ids=[267,133],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},9416:(e,r,t)=>{t.r(r),t.d(r,{headerHooks:()=>S,originalPathname:()=>q,patchFetch:()=>Q,requestAsyncStorage:()=>h,routeModule:()=>f,serverHooks:()=>E,staticGenerationAsyncStorage:()=>b,staticGenerationBailout:()=>T});var a={};t.r(a),t.d(a,{DELETE:()=>w,GET:()=>p,POST:()=>g,PUT:()=>m});var s=t(95419),n=t(69108),i=t(99678),o=t(78070),u=t(67133),c=t(88302);let d=c.z.object({sessionId:c.z.string().cuid(),userId:c.z.string().cuid(),userInput:c.z.string().min(1),generatedSQL:c.z.string().min(1),explanation:c.z.string().optional(),databaseType:c.z.enum(["MYSQL","POSTGRESQL"]),executionTime:c.z.number().int().min(0).optional(),rowsAffected:c.z.number().int().min(0).optional(),performanceData:c.z.any().optional(),optimizationTips:c.z.any().optional()}),y=c.z.object({status:c.z.enum(["GENERATED","EXECUTED","FAILED","OPTIMIZED"]).optional(),executionTime:c.z.number().int().min(0).optional(),rowsAffected:c.z.number().int().min(0).optional(),errorMessage:c.z.string().optional(),performanceData:c.z.any().optional(),userFeedback:c.z.number().int().min(1).max(5).optional()}),l=c.z.object({userId:c.z.string().cuid(),databaseConnectionId:c.z.string().cuid(),name:c.z.string().optional(),sessionData:c.z.any().optional()});async function p(e){try{let{searchParams:r}=new URL(e.url),t=r.get("userId"),a=r.get("sessionId"),s=r.get("type"),n=parseInt(r.get("limit")||"20"),i=r.get("databaseType");if("sessions"===s&&t){let e=await u.QueryService.getUserSessions(t,n);return o.Z.json(e)}if("history"===s&&t){let e=await u.QueryService.getUserQueryHistory(t,n,i||void 0);return o.Z.json(e)}if(a){let e=await u.QueryService.getSessionById(a);if(!e)return o.Z.json({error:"Session not found"},{status:404});return o.Z.json(e)}return o.Z.json({error:"Invalid parameters. Use type=sessions&userId=X or type=history&userId=X or sessionId=X"},{status:400})}catch(e){return console.error("GET /api/queries error:",e),o.Z.json({error:"Internal server error"},{status:500})}}async function g(e){try{let{searchParams:r}=new URL(e.url),t=r.get("type"),a=await e.json();if("session"===t){let e=l.parse(a),r=await u.QueryService.createSession(e);return o.Z.json(r,{status:201})}if("query"===t){let e=d.parse(a),r=await u.QueryService.createGeneratedQuery(e);return o.Z.json(r,{status:201})}return o.Z.json({error:"Type parameter required (query or session)"},{status:400})}catch(e){if(e instanceof c.z.ZodError)return o.Z.json({error:"Validation failed",details:e.errors},{status:400});return console.error("POST /api/queries error:",e),o.Z.json({error:"Internal server error"},{status:500})}}async function m(e){try{let{searchParams:r}=new URL(e.url),t=r.get("id"),a=r.get("type");if(!t)return o.Z.json({error:"ID required"},{status:400});let s=await e.json();if("query"===a){let e=y.parse(s);if(e.userFeedback){let r=await u.QueryService.addQueryFeedback(t,e.userFeedback);return o.Z.json(r)}if(!e.status)return o.Z.json({error:"Either userFeedback or status is required for query updates"},{status:400});{let r={status:e.status,executionTime:e.executionTime,rowsAffected:e.rowsAffected,errorMessage:e.errorMessage,performanceData:e.performanceData},a=await u.QueryService.updateQueryExecution(t,r);return o.Z.json(a)}}if("session"===a){let e=c.z.object({name:c.z.string().optional(),sessionData:c.z.any().optional(),isActive:c.z.boolean().optional()}).parse(s),r=await u.QueryService.updateSession(t,e);return o.Z.json(r)}return o.Z.json({error:"Type parameter required (query or session)"},{status:400})}catch(e){if(e instanceof c.z.ZodError)return o.Z.json({error:"Validation failed",details:e.errors},{status:400});return console.error("PUT /api/queries error:",e),o.Z.json({error:"Internal server error"},{status:500})}}async function w(e){try{let{searchParams:r}=new URL(e.url),t=r.get("id");if(!t)return o.Z.json({error:"Session ID required"},{status:400});return await u.QueryService.deleteSession(t),o.Z.json({success:!0})}catch(e){return console.error("DELETE /api/queries error:",e),o.Z.json({error:"Internal server error"},{status:500})}}let f=new s.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/queries/route",pathname:"/api/queries",filename:"route",bundlePath:"app/api/queries/route"},resolvedPagePath:"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/app/api/queries/route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:h,staticGenerationAsyncStorage:b,serverHooks:E,headerHooks:S,staticGenerationBailout:T}=f,q="/api/queries/route";function Q(){return(0,i.patchFetch)({serverHooks:E,staticGenerationAsyncStorage:b})}},68505:(e,r,t)=>{t.d(r,{Lb:()=>n,_B:()=>s});let a=require("@prisma/client"),s=globalThis.prisma??new a.PrismaClient({log:["query","error","warn"],errorFormat:"pretty"});function n(e){if(console.error("Database error:",e),"P2002"===e.code)throw Error("A record with this information already exists");if("P2025"===e.code)throw Error("Record not found");if("P2003"===e.code)throw Error("Foreign key constraint failed");throw Error("Database operation failed")}},67133:(e,r,t)=>{t.d(r,{QueryService:()=>s});var a=t(68505);class s{static async createSession(e){try{return await a._B.querySession.create({data:{...e,sessionData:e.sessionData||{}}})}catch(e){(0,a.Lb)(e)}}static async getSessionById(e){try{return await a._B.querySession.findUnique({where:{id:e},include:{user:{select:{id:!0,name:!0,email:!0}},databaseConnection:{select:{id:!0,name:!0,databaseType:!0}},generatedQueries:{orderBy:{createdAt:"desc"},take:10},chatMessages:{orderBy:{createdAt:"asc"}}}})}catch(e){(0,a.Lb)(e)}}static async getUserSessions(e,r=20){try{return await a._B.querySession.findMany({where:{userId:e,isActive:!0},include:{databaseConnection:{select:{id:!0,name:!0,databaseType:!0}},_count:{select:{generatedQueries:!0,chatMessages:!0}}},orderBy:{updatedAt:"desc"},take:r})}catch(e){(0,a.Lb)(e)}}static async createGeneratedQuery(e){try{return await a._B.generatedQuery.create({data:e})}catch(e){(0,a.Lb)(e)}}static async updateQueryExecution(e,r){try{return await a._B.generatedQuery.update({where:{id:e},data:r})}catch(e){(0,a.Lb)(e)}}static async addQueryFeedback(e,r){try{return await a._B.generatedQuery.update({where:{id:e},data:{userFeedback:r}})}catch(e){(0,a.Lb)(e)}}static async getUserQueryHistory(e,r=50,t){try{return await a._B.generatedQuery.findMany({where:{userId:e,...t&&{databaseType:t}},include:{session:{select:{id:!0,name:!0,databaseConnection:{select:{id:!0,name:!0}}}}},orderBy:{createdAt:"desc"},take:r})}catch(e){(0,a.Lb)(e)}}static async addChatMessage(e){try{return await a._B.chatMessage.create({data:e})}catch(e){(0,a.Lb)(e)}}static async getSessionMessages(e){try{return await a._B.chatMessage.findMany({where:{sessionId:e},orderBy:{createdAt:"asc"}})}catch(e){(0,a.Lb)(e)}}static async updateSession(e,r){try{return await a._B.querySession.update({where:{id:e},data:r})}catch(e){(0,a.Lb)(e)}}static async deleteSession(e){try{await a._B.querySession.delete({where:{id:e}})}catch(e){(0,a.Lb)(e)}}static async getQueryAnalytics(e,r=30){try{let t=new Date(Date.now()-864e5*r),[s,n,i,o,u]=await Promise.all([a._B.generatedQuery.count({where:{userId:e,createdAt:{gte:t}}}),a._B.generatedQuery.count({where:{userId:e,status:"EXECUTED",createdAt:{gte:t}}}),a._B.generatedQuery.aggregate({where:{userId:e,executionTime:{not:null},createdAt:{gte:t}},_avg:{executionTime:!0}}),a._B.generatedQuery.groupBy({by:["databaseType"],where:{userId:e,createdAt:{gte:t}},_count:{id:!0},orderBy:{_count:{id:"desc"}}}),a._B.$queryRaw`
          SELECT 
            DATE(created_at) as date,
            COUNT(*) as count
          FROM generated_queries 
          WHERE user_id = ${e} 
            AND created_at >= ${t}
          GROUP BY DATE(created_at)
          ORDER BY date DESC
        `]);return{totalQueries:s,successfulQueries:n,successRate:s>0?n/s*100:0,averageExecutionTime:i._avg.executionTime||0,topDatabases:o,dailyActivity:u}}catch(e){(0,a.Lb)(e)}}}}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[638,543],()=>t(9416));module.exports=a})();