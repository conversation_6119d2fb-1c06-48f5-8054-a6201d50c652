"use strict";(()=>{var t={};t.id=737,t.ids=[737],t.modules={30517:t=>{t.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},35900:t=>{t.exports=require("pg")},14300:t=>{t.exports=require("buffer")},6113:t=>{t.exports=require("crypto")},82361:t=>{t.exports=require("events")},41808:t=>{t.exports=require("net")},77282:t=>{t.exports=require("process")},12781:t=>{t.exports=require("stream")},71576:t=>{t.exports=require("string_decoder")},39512:t=>{t.exports=require("timers")},24404:t=>{t.exports=require("tls")},57310:t=>{t.exports=require("url")},73837:t=>{t.exports=require("util")},59796:t=>{t.exports=require("zlib")},92068:(t,e,n)=>{n.r(e),n.d(e,{headerHooks:()=>w,originalPathname:()=>f,patchFetch:()=>b,requestAsyncStorage:()=>h,routeModule:()=>p,serverHooks:()=>m,staticGenerationAsyncStorage:()=>g,staticGenerationBailout:()=>y});var r={};n.r(r),n.d(r,{POST:()=>l});var a=n(95419),o=n(69108),s=n(99678),i=n(78070),c=n(55949),d=n(88302);let u=d.z.object({connectionId:d.z.string().cuid().optional(),databaseType:d.z.enum(["MYSQL","POSTGRESQL"]).optional(),connectionString:d.z.string().optional(),host:d.z.string().optional(),port:d.z.number().int().min(1).max(65535).optional(),database:d.z.string().optional(),username:d.z.string().optional(),password:d.z.string().optional(),ssl:d.z.boolean().optional()});async function l(t){try{let e=await t.json(),r=u.parse(e),a=!1,o=null;if(r.connectionId)try{a=await c.ConnectionService.testConnection(r.connectionId)}catch(t){o=t instanceof Error?t.message:"Connection test failed"}else if(!r.databaseType||!r.connectionString)return i.Z.json({error:"Either connectionId or connection details required"},{status:400});else try{let t;let{ConnectorFactory:e}=await Promise.all([n.e(69),n.e(168)]).then(n.bind(n,98480));t=r.connectionString?function(t){try{if(t.startsWith("postgresql://")||t.startsWith("postgres://")){let e=new URL(t);return{host:e.hostname,port:parseInt(e.port)||5432,database:e.pathname.substring(1),username:e.username,password:e.password,ssl:"require"===e.searchParams.get("sslmode")}}if(t.startsWith("mysql://")){let e=new URL(t);return{host:e.hostname,port:parseInt(e.port)||3306,database:e.pathname.substring(1),username:e.username,password:e.password,ssl:"true"===e.searchParams.get("ssl")}}{let e=new Map;return t.split(";").forEach(t=>{let[n,r]=t.split("=");n&&r&&e.set(n.trim().toLowerCase(),r.trim())}),{host:e.get("host")||"localhost",port:parseInt(e.get("port")||"5432"),database:e.get("database")||"",username:e.get("username")||e.get("user")||"",password:e.get("password")||"",ssl:"true"===e.get("ssl")}}}catch(t){throw Error(`Invalid connection string format: ${t}`)}}(r.connectionString):{host:r.host||"localhost",port:r.port||("MYSQL"===r.databaseType?3306:5432),database:r.database||"",username:r.username||"",password:r.password||"",ssl:r.ssl||!1};let o=e.createConnector(r.databaseType,t);a=await o.testConnection(),await o.disconnect()}catch(t){o=t instanceof Error?t.message:"Connection test failed"}return i.Z.json({success:a,connected:a,error:o,timestamp:new Date().toISOString()})}catch(t){if(t instanceof d.z.ZodError)return i.Z.json({error:"Validation failed",details:t.errors},{status:400});return console.error("POST /api/connections/test error:",t),i.Z.json({success:!1,connected:!1,error:t instanceof Error?t.message:"Internal server error",timestamp:new Date().toISOString()},{status:500})}}let p=new a.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/connections/test/route",pathname:"/api/connections/test",filename:"route",bundlePath:"app/api/connections/test/route"},resolvedPagePath:"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/app/api/connections/test/route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:h,staticGenerationAsyncStorage:g,serverHooks:m,headerHooks:w,staticGenerationBailout:y}=p,f="/api/connections/test/route";function b(){return(0,s.patchFetch)({serverHooks:m,staticGenerationAsyncStorage:g})}},68505:(t,e,n)=>{n.d(e,{Lb:()=>o,_B:()=>a});let r=require("@prisma/client"),a=globalThis.prisma??new r.PrismaClient({log:["query","error","warn"],errorFormat:"pretty"});function o(t){if(console.error("Database error:",t),"P2002"===t.code)throw Error("A record with this information already exists");if("P2025"===t.code)throw Error("Record not found");if("P2003"===t.code)throw Error("Foreign key constraint failed");throw Error("Database operation failed")}},55949:(t,e,n)=>{n.d(e,{ConnectionService:()=>o});var r=n(68505);function a(t){try{let e=Buffer.from(t).toString("base64");return"enc:"+e}catch(t){throw console.error("Encryption failed:",t),Error("Failed to encrypt data")}}n(6113);class o{static async createConnection(t){try{let e=a(t.connectionString),{connectionString:n,...o}=t;return await r._B.databaseConnection.create({data:{...o,connectionStringHash:e}})}catch(t){(0,r.Lb)(t)}}static async getConnectionById(t){try{return await r._B.databaseConnection.findUnique({where:{id:t},include:{user:{select:{id:!0,name:!0,email:!0}},team:{select:{id:!0,name:!0}}}})}catch(t){(0,r.Lb)(t)}}static async getUserConnections(t){try{return await r._B.databaseConnection.findMany({where:{userId:t,isActive:!0},orderBy:{lastConnectedAt:"desc"}})}catch(t){(0,r.Lb)(t)}}static async getTeamConnections(t){try{return await r._B.databaseConnection.findMany({where:{teamId:t,isActive:!0},include:{user:{select:{id:!0,name:!0,email:!0}}},orderBy:{lastConnectedAt:"desc"}})}catch(t){(0,r.Lb)(t)}}static async updateConnection(t,e){try{let n={...e};return e.connectionString&&(n.connectionStringHash=a(e.connectionString),delete n.connectionString),await r._B.databaseConnection.update({where:{id:t},data:n})}catch(t){(0,r.Lb)(t)}}static async updateLastConnected(t){try{await r._B.databaseConnection.update({where:{id:t},data:{lastConnectedAt:new Date}})}catch(t){(0,r.Lb)(t)}}static async deleteConnection(t){try{await r._B.databaseConnection.delete({where:{id:t}})}catch(t){(0,r.Lb)(t)}}static async getConnectionString(t){try{let e=await r._B.databaseConnection.findUnique({where:{id:t},select:{connectionStringHash:!0}});if(!e)return null;return function(t){try{if(!t.startsWith("enc:"))throw Error("Invalid encrypted data format");let e=t.substring(4);return Buffer.from(e,"base64").toString("utf8")}catch(t){throw console.error("Decryption failed:",t),Error("Failed to decrypt data")}}(e.connectionStringHash)}catch(t){return console.error("Failed to decrypt connection string:",t),null}}static async testConnection(t){try{let e=await this.getConnectionById(t);if(!e)return!1;let r=await this.getConnectionString(t);if(!r)return!1;let a=this.parseConnectionString(r),{ConnectorFactory:o}=await Promise.all([n.e(69),n.e(168)]).then(n.bind(n,98480)),s=o.createConnector(e.databaseType,a),i=await s.testConnection();return await s.disconnect(),i&&await this.updateLastConnected(t),i}catch(t){return console.error("Connection test failed:",t),!1}}static async getConnectionStats(t){try{let[e,n,a]=await Promise.all([r._B.generatedQuery.count({where:{session:{databaseConnectionId:t}}}),r._B.querySession.count({where:{databaseConnectionId:t}}),r._B.querySession.count({where:{databaseConnectionId:t,createdAt:{gte:new Date(Date.now()-6048e5)}}})]);return{totalQueries:e,totalSessions:n,recentActivity:a}}catch(t){(0,r.Lb)(t)}}static parseConnectionString(t){try{if(t.startsWith("postgresql://")||t.startsWith("postgres://"))return this.parsePostgreSQLConnectionString(t);if(t.startsWith("mysql://"))return this.parseMySQLConnectionString(t);return this.parseGenericConnectionString(t)}catch(t){throw Error(`Invalid connection string format: ${t}`)}}static parsePostgreSQLConnectionString(t){let e=new URL(t);return{host:e.hostname,port:parseInt(e.port)||5432,database:e.pathname.substring(1),username:e.username,password:e.password,ssl:"require"===e.searchParams.get("sslmode")}}static parseMySQLConnectionString(t){let e=new URL(t);return{host:e.hostname,port:parseInt(e.port)||3306,database:e.pathname.substring(1),username:e.username,password:e.password,ssl:"true"===e.searchParams.get("ssl")}}static parseGenericConnectionString(t){let e=new Map;return t.split(";").forEach(t=>{let[n,r]=t.split("=");n&&r&&e.set(n.trim().toLowerCase(),r.trim())}),{host:e.get("host")||"localhost",port:parseInt(e.get("port")||"5432"),database:e.get("database")||"",username:e.get("username")||e.get("user")||"",password:e.get("password")||"",ssl:"true"===e.get("ssl")}}static async getConnectionSchema(t){try{let e=await this.getConnectionById(t);if(!e)throw Error("Connection not found");let r=await this.getConnectionString(t);if(!r)throw Error("Connection string not found");let a=this.parseConnectionString(r),{ConnectorFactory:o}=await Promise.all([n.e(69),n.e(168)]).then(n.bind(n,98480)),s=o.createConnector(e.databaseType,a,t),i=await s.getSchema();return await this.updateConnection(t,{schemaMetadata:i}),i}catch(t){(0,r.Lb)(t)}}}}};var e=require("../../../../webpack-runtime.js");e.C(t);var n=t=>e(e.s=t),r=e.X(0,[638,543],()=>n(92068));module.exports=r})();