"use strict";(()=>{var e={};e.id=348,e.ids=[348],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},35900:e=>{e.exports=require("pg")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},41808:e=>{e.exports=require("net")},77282:e=>{e.exports=require("process")},12781:e=>{e.exports=require("stream")},71576:e=>{e.exports=require("string_decoder")},39512:e=>{e.exports=require("timers")},24404:e=>{e.exports=require("tls")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},2410:(e,t,n)=>{n.r(t),n.d(t,{headerHooks:()=>b,originalPathname:()=>q,patchFetch:()=>v,requestAsyncStorage:()=>C,routeModule:()=>w,serverHooks:()=>f,staticGenerationAsyncStorage:()=>y,staticGenerationBailout:()=>S});var r={};n.r(r),n.d(r,{DELETE:()=>m,GET:()=>p,POST:()=>h,PUT:()=>g});var o=n(95419),a=n(69108),i=n(99678),s=n(78070),c=n(55949),u=n(88302);let d=u.z.object({userId:u.z.string().cuid(),teamId:u.z.string().cuid().optional(),name:u.z.string().min(1).max(255),databaseType:u.z.enum(["MYSQL","POSTGRESQL"]),connectionString:u.z.string().min(1),host:u.z.string().optional(),port:u.z.number().int().min(1).max(65535).optional(),database:u.z.string().optional(),username:u.z.string().optional()}),l=u.z.object({name:u.z.string().min(1).max(255).optional(),connectionString:u.z.string().min(1).optional(),host:u.z.string().optional(),port:u.z.number().int().min(1).max(65535).optional(),database:u.z.string().optional(),username:u.z.string().optional(),schemaMetadata:u.z.any().optional(),isActive:u.z.boolean().optional()});async function p(e){try{let{searchParams:t}=new URL(e.url),n=t.get("userId"),r=t.get("teamId"),o=t.get("id");if(o){let e=await c.ConnectionService.getConnectionById(o);if(!e)return s.Z.json({error:"Connection not found"},{status:404});return s.Z.json(e)}if(n){let e=await c.ConnectionService.getUserConnections(n);return s.Z.json(e)}if(r){let e=await c.ConnectionService.getTeamConnections(r);return s.Z.json(e)}return s.Z.json({error:"userId, teamId, or id parameter required"},{status:400})}catch(e){return console.error("GET /api/connections error:",e),s.Z.json({error:"Internal server error"},{status:500})}}async function h(e){try{let t=await e.json(),n=d.parse(t),r=await c.ConnectionService.createConnection(n);return s.Z.json(r,{status:201})}catch(e){if(e instanceof u.z.ZodError)return s.Z.json({error:"Validation failed",details:e.errors},{status:400});return console.error("POST /api/connections error:",e),s.Z.json({error:"Internal server error"},{status:500})}}async function g(e){try{let{searchParams:t}=new URL(e.url),n=t.get("id");if(!n)return s.Z.json({error:"Connection ID required"},{status:400});let r=await e.json(),o=l.parse(r),a=await c.ConnectionService.updateConnection(n,o);return s.Z.json(a)}catch(e){if(e instanceof u.z.ZodError)return s.Z.json({error:"Validation failed",details:e.errors},{status:400});return console.error("PUT /api/connections error:",e),s.Z.json({error:"Internal server error"},{status:500})}}async function m(e){try{let{searchParams:t}=new URL(e.url),n=t.get("id");if(!n)return s.Z.json({error:"Connection ID required"},{status:400});return await c.ConnectionService.deleteConnection(n),s.Z.json({success:!0})}catch(e){return console.error("DELETE /api/connections error:",e),s.Z.json({error:"Internal server error"},{status:500})}}let w=new o.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/connections/route",pathname:"/api/connections",filename:"route",bundlePath:"app/api/connections/route"},resolvedPagePath:"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/app/api/connections/route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:C,staticGenerationAsyncStorage:y,serverHooks:f,headerHooks:b,staticGenerationBailout:S}=w,q="/api/connections/route";function v(){return(0,i.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:y})}},68505:(e,t,n)=>{n.d(t,{Lb:()=>a,_B:()=>o});let r=require("@prisma/client"),o=globalThis.prisma??new r.PrismaClient({log:["query","error","warn"],errorFormat:"pretty"});function a(e){if(console.error("Database error:",e),"P2002"===e.code)throw Error("A record with this information already exists");if("P2025"===e.code)throw Error("Record not found");if("P2003"===e.code)throw Error("Foreign key constraint failed");throw Error("Database operation failed")}},55949:(e,t,n)=>{n.d(t,{ConnectionService:()=>a});var r=n(68505);function o(e){try{let t=Buffer.from(e).toString("base64");return"enc:"+t}catch(e){throw console.error("Encryption failed:",e),Error("Failed to encrypt data")}}n(6113);class a{static async createConnection(e){try{let t=o(e.connectionString),{connectionString:n,...a}=e;return await r._B.databaseConnection.create({data:{...a,connectionStringHash:t}})}catch(e){(0,r.Lb)(e)}}static async getConnectionById(e){try{return await r._B.databaseConnection.findUnique({where:{id:e},include:{user:{select:{id:!0,name:!0,email:!0}},team:{select:{id:!0,name:!0}}}})}catch(e){(0,r.Lb)(e)}}static async getUserConnections(e){try{return await r._B.databaseConnection.findMany({where:{userId:e,isActive:!0},orderBy:{lastConnectedAt:"desc"}})}catch(e){(0,r.Lb)(e)}}static async getTeamConnections(e){try{return await r._B.databaseConnection.findMany({where:{teamId:e,isActive:!0},include:{user:{select:{id:!0,name:!0,email:!0}}},orderBy:{lastConnectedAt:"desc"}})}catch(e){(0,r.Lb)(e)}}static async updateConnection(e,t){try{let n={...t};return t.connectionString&&(n.connectionStringHash=o(t.connectionString),delete n.connectionString),await r._B.databaseConnection.update({where:{id:e},data:n})}catch(e){(0,r.Lb)(e)}}static async updateLastConnected(e){try{await r._B.databaseConnection.update({where:{id:e},data:{lastConnectedAt:new Date}})}catch(e){(0,r.Lb)(e)}}static async deleteConnection(e){try{await r._B.databaseConnection.delete({where:{id:e}})}catch(e){(0,r.Lb)(e)}}static async getConnectionString(e){try{let t=await r._B.databaseConnection.findUnique({where:{id:e},select:{connectionStringHash:!0}});if(!t)return null;return function(e){try{if(!e.startsWith("enc:"))throw Error("Invalid encrypted data format");let t=e.substring(4);return Buffer.from(t,"base64").toString("utf8")}catch(e){throw console.error("Decryption failed:",e),Error("Failed to decrypt data")}}(t.connectionStringHash)}catch(e){return console.error("Failed to decrypt connection string:",e),null}}static async testConnection(e){try{let t=await this.getConnectionById(e);if(!t)return!1;let r=await this.getConnectionString(e);if(!r)return!1;let o=this.parseConnectionString(r),{ConnectorFactory:a}=await Promise.all([n.e(69),n.e(168)]).then(n.bind(n,98480)),i=a.createConnector(t.databaseType,o),s=await i.testConnection();return await i.disconnect(),s&&await this.updateLastConnected(e),s}catch(e){return console.error("Connection test failed:",e),!1}}static async getConnectionStats(e){try{let[t,n,o]=await Promise.all([r._B.generatedQuery.count({where:{session:{databaseConnectionId:e}}}),r._B.querySession.count({where:{databaseConnectionId:e}}),r._B.querySession.count({where:{databaseConnectionId:e,createdAt:{gte:new Date(Date.now()-6048e5)}}})]);return{totalQueries:t,totalSessions:n,recentActivity:o}}catch(e){(0,r.Lb)(e)}}static parseConnectionString(e){try{if(e.startsWith("postgresql://")||e.startsWith("postgres://"))return this.parsePostgreSQLConnectionString(e);if(e.startsWith("mysql://"))return this.parseMySQLConnectionString(e);return this.parseGenericConnectionString(e)}catch(e){throw Error(`Invalid connection string format: ${e}`)}}static parsePostgreSQLConnectionString(e){let t=new URL(e);return{host:t.hostname,port:parseInt(t.port)||5432,database:t.pathname.substring(1),username:t.username,password:t.password,ssl:"require"===t.searchParams.get("sslmode")}}static parseMySQLConnectionString(e){let t=new URL(e);return{host:t.hostname,port:parseInt(t.port)||3306,database:t.pathname.substring(1),username:t.username,password:t.password,ssl:"true"===t.searchParams.get("ssl")}}static parseGenericConnectionString(e){let t=new Map;return e.split(";").forEach(e=>{let[n,r]=e.split("=");n&&r&&t.set(n.trim().toLowerCase(),r.trim())}),{host:t.get("host")||"localhost",port:parseInt(t.get("port")||"5432"),database:t.get("database")||"",username:t.get("username")||t.get("user")||"",password:t.get("password")||"",ssl:"true"===t.get("ssl")}}static async getConnectionSchema(e){try{let t=await this.getConnectionById(e);if(!t)throw Error("Connection not found");let r=await this.getConnectionString(e);if(!r)throw Error("Connection string not found");let o=this.parseConnectionString(r),{ConnectorFactory:a}=await Promise.all([n.e(69),n.e(168)]).then(n.bind(n,98480)),i=a.createConnector(t.databaseType,o,e),s=await i.getSchema();return await this.updateConnection(e,{schemaMetadata:s}),s}catch(e){(0,r.Lb)(e)}}}}};var t=require("../../../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),r=t.X(0,[638,543],()=>n(2410));module.exports=r})();