"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/connections/route";
exports.ids = ["app/api/connections/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "pg":
/*!*********************!*\
  !*** external "pg" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("pg");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("net");

/***/ }),

/***/ "process":
/*!**************************!*\
  !*** external "process" ***!
  \**************************/
/***/ ((module) => {

module.exports = require("process");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "string_decoder":
/*!*********************************!*\
  !*** external "string_decoder" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("string_decoder");

/***/ }),

/***/ "timers":
/*!*************************!*\
  !*** external "timers" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("timers");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fconnections%2Froute&page=%2Fapi%2Fconnections%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fconnections%2Froute.ts&appDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fquery-craft-studio-2%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fquery-craft-studio-2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fconnections%2Froute&page=%2Fapi%2Fconnections%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fconnections%2Froute.ts&appDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fquery-craft-studio-2%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fquery-craft-studio-2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_alexandresimasmaciel_Documents_ia_sistemas_query_craft_studio_2_src_app_api_connections_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/connections/route.ts */ \"(rsc)/./src/app/api/connections/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/connections/route\",\n        pathname: \"/api/connections\",\n        filename: \"route\",\n        bundlePath: \"app/api/connections/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/app/api/connections/route.ts\",\n    nextConfigOutput,\n    userland: _Users_alexandresimasmaciel_Documents_ia_sistemas_query_craft_studio_2_src_app_api_connections_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/connections/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fconnections%2Froute&page=%2Fapi%2Fconnections%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fconnections%2Froute.ts&appDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fquery-craft-studio-2%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fquery-craft-studio-2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/connections/route.ts":
/*!******************************************!*\
  !*** ./src/app/api/connections/route.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _lib_database_services_connection_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/database/services/connection-service */ \"(rsc)/./src/lib/database/services/connection-service.ts\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/dist/esm/index.js\");\n\n\n\n// Validation schemas\nconst createConnectionSchema = zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n    userId: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().cuid(),\n    teamId: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().cuid().optional(),\n    name: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().min(1).max(255),\n    databaseType: zod__WEBPACK_IMPORTED_MODULE_2__.z[\"enum\"]([\n        \"MYSQL\",\n        \"POSTGRESQL\"\n    ]),\n    connectionString: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().min(1),\n    host: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().optional(),\n    port: zod__WEBPACK_IMPORTED_MODULE_2__.z.number().int().min(1).max(65535).optional(),\n    database: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().optional(),\n    username: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().optional()\n});\nconst updateConnectionSchema = zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().min(1).max(255).optional(),\n    connectionString: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().min(1).optional(),\n    host: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().optional(),\n    port: zod__WEBPACK_IMPORTED_MODULE_2__.z.number().int().min(1).max(65535).optional(),\n    database: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().optional(),\n    username: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().optional(),\n    schemaMetadata: zod__WEBPACK_IMPORTED_MODULE_2__.z.any().optional(),\n    isActive: zod__WEBPACK_IMPORTED_MODULE_2__.z.boolean().optional()\n});\n// GET /api/connections - Get user connections\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const userId = searchParams.get(\"userId\");\n        const teamId = searchParams.get(\"teamId\");\n        const id = searchParams.get(\"id\");\n        if (id) {\n            const connection = await _lib_database_services_connection_service__WEBPACK_IMPORTED_MODULE_1__.ConnectionService.getConnectionById(id);\n            if (!connection) {\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    error: \"Connection not found\"\n                }, {\n                    status: 404\n                });\n            }\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(connection);\n        }\n        if (userId) {\n            const connections = await _lib_database_services_connection_service__WEBPACK_IMPORTED_MODULE_1__.ConnectionService.getUserConnections(userId);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(connections);\n        }\n        if (teamId) {\n            const connections = await _lib_database_services_connection_service__WEBPACK_IMPORTED_MODULE_1__.ConnectionService.getTeamConnections(teamId);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(connections);\n        }\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"userId, teamId, or id parameter required\"\n        }, {\n            status: 400\n        });\n    } catch (error) {\n        console.error(\"GET /api/connections error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/connections - Create a new connection\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const validatedData = createConnectionSchema.parse(body);\n        const connection = await _lib_database_services_connection_service__WEBPACK_IMPORTED_MODULE_1__.ConnectionService.createConnection(validatedData);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(connection, {\n            status: 201\n        });\n    } catch (error) {\n        if (error instanceof zod__WEBPACK_IMPORTED_MODULE_2__.z.ZodError) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Validation failed\",\n                details: error.errors\n            }, {\n                status: 400\n            });\n        }\n        console.error(\"POST /api/connections error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n// PUT /api/connections - Update connection\nasync function PUT(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const id = searchParams.get(\"id\");\n        if (!id) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Connection ID required\"\n            }, {\n                status: 400\n            });\n        }\n        const body = await request.json();\n        const validatedData = updateConnectionSchema.parse(body);\n        const connection = await _lib_database_services_connection_service__WEBPACK_IMPORTED_MODULE_1__.ConnectionService.updateConnection(id, validatedData);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(connection);\n    } catch (error) {\n        if (error instanceof zod__WEBPACK_IMPORTED_MODULE_2__.z.ZodError) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Validation failed\",\n                details: error.errors\n            }, {\n                status: 400\n            });\n        }\n        console.error(\"PUT /api/connections error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n// DELETE /api/connections - Delete connection\nasync function DELETE(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const id = searchParams.get(\"id\");\n        if (!id) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Connection ID required\"\n            }, {\n                status: 400\n            });\n        }\n        await _lib_database_services_connection_service__WEBPACK_IMPORTED_MODULE_1__.ConnectionService.deleteConnection(id);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true\n        });\n    } catch (error) {\n        console.error(\"DELETE /api/connections error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/connections/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database/prisma.ts":
/*!************************************!*\
  !*** ./src/lib/database/prisma.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   disconnectDatabase: () => (/* binding */ disconnectDatabase),\n/* harmony export */   handleDatabaseError: () => (/* binding */ handleDatabaseError),\n/* harmony export */   prisma: () => (/* binding */ prisma),\n/* harmony export */   testDatabaseConnection: () => (/* binding */ testDatabaseConnection)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n    log: [\n        \"query\",\n        \"error\",\n        \"warn\"\n    ],\n    errorFormat: \"pretty\"\n});\nif (true) globalForPrisma.prisma = prisma;\n// Helper function to handle database errors\nfunction handleDatabaseError(error) {\n    console.error(\"Database error:\", error);\n    if (error.code === \"P2002\") {\n        throw new Error(\"A record with this information already exists\");\n    }\n    if (error.code === \"P2025\") {\n        throw new Error(\"Record not found\");\n    }\n    if (error.code === \"P2003\") {\n        throw new Error(\"Foreign key constraint failed\");\n    }\n    throw new Error(\"Database operation failed\");\n}\n// Connection test function\nasync function testDatabaseConnection() {\n    try {\n        await prisma.$connect();\n        await prisma.$queryRaw`SELECT 1`;\n        return true;\n    } catch (error) {\n        console.error(\"Database connection failed:\", error);\n        return false;\n    }\n}\n// Graceful shutdown\nasync function disconnectDatabase() {\n    await prisma.$disconnect();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database/prisma.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database/services/connection-service.ts":
/*!*********************************************************!*\
  !*** ./src/lib/database/services/connection-service.ts ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConnectionService: () => (/* binding */ ConnectionService)\n/* harmony export */ });\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../prisma */ \"(rsc)/./src/lib/database/prisma.ts\");\n/* harmony import */ var _lib_utils_encryption__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils/encryption */ \"(rsc)/./src/lib/utils/encryption.ts\");\n\n\nclass ConnectionService {\n    // Create a new database connection\n    static async createConnection(data) {\n        try {\n            const connectionStringHash = (0,_lib_utils_encryption__WEBPACK_IMPORTED_MODULE_1__.encrypt)(data.connectionString);\n            const { connectionString, ...createData } = data;\n            return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.databaseConnection.create({\n                data: {\n                    ...createData,\n                    connectionStringHash\n                }\n            });\n        } catch (error) {\n            (0,_prisma__WEBPACK_IMPORTED_MODULE_0__.handleDatabaseError)(error);\n        }\n    }\n    // Get connection by ID\n    static async getConnectionById(id) {\n        try {\n            return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.databaseConnection.findUnique({\n                where: {\n                    id\n                },\n                include: {\n                    user: {\n                        select: {\n                            id: true,\n                            name: true,\n                            email: true\n                        }\n                    },\n                    team: {\n                        select: {\n                            id: true,\n                            name: true\n                        }\n                    }\n                }\n            });\n        } catch (error) {\n            (0,_prisma__WEBPACK_IMPORTED_MODULE_0__.handleDatabaseError)(error);\n        }\n    }\n    // Get user connections\n    static async getUserConnections(userId) {\n        try {\n            return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.databaseConnection.findMany({\n                where: {\n                    userId,\n                    isActive: true\n                },\n                orderBy: {\n                    lastConnectedAt: \"desc\"\n                }\n            });\n        } catch (error) {\n            (0,_prisma__WEBPACK_IMPORTED_MODULE_0__.handleDatabaseError)(error);\n        }\n    }\n    // Get team connections\n    static async getTeamConnections(teamId) {\n        try {\n            return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.databaseConnection.findMany({\n                where: {\n                    teamId,\n                    isActive: true\n                },\n                include: {\n                    user: {\n                        select: {\n                            id: true,\n                            name: true,\n                            email: true\n                        }\n                    }\n                },\n                orderBy: {\n                    lastConnectedAt: \"desc\"\n                }\n            });\n        } catch (error) {\n            (0,_prisma__WEBPACK_IMPORTED_MODULE_0__.handleDatabaseError)(error);\n        }\n    }\n    // Update connection\n    static async updateConnection(id, data) {\n        try {\n            const updateData = {\n                ...data\n            };\n            // Encrypt connection string if provided\n            if (data.connectionString) {\n                updateData.connectionStringHash = (0,_lib_utils_encryption__WEBPACK_IMPORTED_MODULE_1__.encrypt)(data.connectionString);\n                delete updateData.connectionString;\n            }\n            return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.databaseConnection.update({\n                where: {\n                    id\n                },\n                data: updateData\n            });\n        } catch (error) {\n            (0,_prisma__WEBPACK_IMPORTED_MODULE_0__.handleDatabaseError)(error);\n        }\n    }\n    // Update last connected timestamp\n    static async updateLastConnected(id) {\n        try {\n            await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.databaseConnection.update({\n                where: {\n                    id\n                },\n                data: {\n                    lastConnectedAt: new Date()\n                }\n            });\n        } catch (error) {\n            (0,_prisma__WEBPACK_IMPORTED_MODULE_0__.handleDatabaseError)(error);\n        }\n    }\n    // Delete connection\n    static async deleteConnection(id) {\n        try {\n            await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.databaseConnection.delete({\n                where: {\n                    id\n                }\n            });\n        } catch (error) {\n            (0,_prisma__WEBPACK_IMPORTED_MODULE_0__.handleDatabaseError)(error);\n        }\n    }\n    // Get decrypted connection string\n    static async getConnectionString(id) {\n        try {\n            const connection = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.databaseConnection.findUnique({\n                where: {\n                    id\n                },\n                select: {\n                    connectionStringHash: true\n                }\n            });\n            if (!connection) return null;\n            return (0,_lib_utils_encryption__WEBPACK_IMPORTED_MODULE_1__.decrypt)(connection.connectionStringHash);\n        } catch (error) {\n            console.error(\"Failed to decrypt connection string:\", error);\n            return null;\n        }\n    }\n    // Test connection\n    static async testConnection(id) {\n        try {\n            const connection = await this.getConnectionById(id);\n            if (!connection) return false;\n            const connectionString = await this.getConnectionString(id);\n            if (!connectionString) return false;\n            // Parse connection string to get connection details\n            const config = this.parseConnectionString(connectionString);\n            // Import connector factory\n            const { ConnectorFactory } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/mysql2\"), __webpack_require__.e(\"vendor-chunks/iconv-lite\"), __webpack_require__.e(\"vendor-chunks/aws-ssl-profiles\"), __webpack_require__.e(\"vendor-chunks/sqlstring\"), __webpack_require__.e(\"vendor-chunks/seq-queue\"), __webpack_require__.e(\"vendor-chunks/named-placeholders\"), __webpack_require__.e(\"vendor-chunks/long\"), __webpack_require__.e(\"vendor-chunks/safer-buffer\"), __webpack_require__.e(\"vendor-chunks/lru.min\"), __webpack_require__.e(\"vendor-chunks/is-property\"), __webpack_require__.e(\"vendor-chunks/generate-function\"), __webpack_require__.e(\"vendor-chunks/denque\"), __webpack_require__.e(\"_rsc_node_modules_mysql2_lib_sync_recursive_cardinal_-_rsc_src_lib_database_connectors_connec-e905c9\")]).then(__webpack_require__.bind(__webpack_require__, /*! ../connectors/connector-factory */ \"(rsc)/./src/lib/database/connectors/connector-factory.ts\"));\n            // Create connector and test connection\n            const connector = ConnectorFactory.createConnector(connection.databaseType, config);\n            const isConnected = await connector.testConnection();\n            // Clean up connector\n            await connector.disconnect();\n            if (isConnected) {\n                // Update last connected timestamp if successful\n                await this.updateLastConnected(id);\n            }\n            return isConnected;\n        } catch (error) {\n            console.error(\"Connection test failed:\", error);\n            return false;\n        }\n    }\n    // Get connection statistics\n    static async getConnectionStats(id) {\n        try {\n            const [totalQueries, totalSessions, recentActivity] = await Promise.all([\n                _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.generatedQuery.count({\n                    where: {\n                        session: {\n                            databaseConnectionId: id\n                        }\n                    }\n                }),\n                _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.querySession.count({\n                    where: {\n                        databaseConnectionId: id\n                    }\n                }),\n                _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.querySession.count({\n                    where: {\n                        databaseConnectionId: id,\n                        createdAt: {\n                            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)\n                        }\n                    }\n                })\n            ]);\n            return {\n                totalQueries,\n                totalSessions,\n                recentActivity\n            };\n        } catch (error) {\n            (0,_prisma__WEBPACK_IMPORTED_MODULE_0__.handleDatabaseError)(error);\n        }\n    }\n    // Parse connection string to extract connection details\n    static parseConnectionString(connectionString) {\n        try {\n            // Handle different connection string formats\n            if (connectionString.startsWith(\"postgresql://\") || connectionString.startsWith(\"postgres://\")) {\n                return this.parsePostgreSQLConnectionString(connectionString);\n            } else if (connectionString.startsWith(\"mysql://\")) {\n                return this.parseMySQLConnectionString(connectionString);\n            } else {\n                // Try to parse as a generic format\n                return this.parseGenericConnectionString(connectionString);\n            }\n        } catch (error) {\n            throw new Error(`Invalid connection string format: ${error}`);\n        }\n    }\n    static parsePostgreSQLConnectionString(connectionString) {\n        const url = new URL(connectionString);\n        return {\n            host: url.hostname,\n            port: parseInt(url.port) || 5432,\n            database: url.pathname.substring(1),\n            username: url.username,\n            password: url.password,\n            ssl: url.searchParams.get(\"sslmode\") === \"require\"\n        };\n    }\n    static parseMySQLConnectionString(connectionString) {\n        const url = new URL(connectionString);\n        return {\n            host: url.hostname,\n            port: parseInt(url.port) || 3306,\n            database: url.pathname.substring(1),\n            username: url.username,\n            password: url.password,\n            ssl: url.searchParams.get(\"ssl\") === \"true\"\n        };\n    }\n    static parseGenericConnectionString(connectionString) {\n        // Parse format like: host=localhost;port=5432;database=mydb;username=user;password=pass\n        const params = new Map();\n        connectionString.split(\";\").forEach((pair)=>{\n            const [key, value] = pair.split(\"=\");\n            if (key && value) {\n                params.set(key.trim().toLowerCase(), value.trim());\n            }\n        });\n        return {\n            host: params.get(\"host\") || \"localhost\",\n            port: parseInt(params.get(\"port\") || \"5432\"),\n            database: params.get(\"database\") || \"\",\n            username: params.get(\"username\") || params.get(\"user\") || \"\",\n            password: params.get(\"password\") || \"\",\n            ssl: params.get(\"ssl\") === \"true\"\n        };\n    }\n    // Get schema information for a connection\n    static async getConnectionSchema(id) {\n        try {\n            const connection = await this.getConnectionById(id);\n            if (!connection) throw new Error(\"Connection not found\");\n            const connectionString = await this.getConnectionString(id);\n            if (!connectionString) throw new Error(\"Connection string not found\");\n            const config = this.parseConnectionString(connectionString);\n            const { ConnectorFactory } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/mysql2\"), __webpack_require__.e(\"vendor-chunks/iconv-lite\"), __webpack_require__.e(\"vendor-chunks/aws-ssl-profiles\"), __webpack_require__.e(\"vendor-chunks/sqlstring\"), __webpack_require__.e(\"vendor-chunks/seq-queue\"), __webpack_require__.e(\"vendor-chunks/named-placeholders\"), __webpack_require__.e(\"vendor-chunks/long\"), __webpack_require__.e(\"vendor-chunks/safer-buffer\"), __webpack_require__.e(\"vendor-chunks/lru.min\"), __webpack_require__.e(\"vendor-chunks/is-property\"), __webpack_require__.e(\"vendor-chunks/generate-function\"), __webpack_require__.e(\"vendor-chunks/denque\"), __webpack_require__.e(\"_rsc_node_modules_mysql2_lib_sync_recursive_cardinal_-_rsc_src_lib_database_connectors_connec-e905c9\")]).then(__webpack_require__.bind(__webpack_require__, /*! ../connectors/connector-factory */ \"(rsc)/./src/lib/database/connectors/connector-factory.ts\"));\n            const connector = ConnectorFactory.createConnector(connection.databaseType, config, id);\n            const schema = await connector.getSchema();\n            // Update schema metadata in database\n            await this.updateConnection(id, {\n                schemaMetadata: schema\n            });\n            return schema;\n        } catch (error) {\n            (0,_prisma__WEBPACK_IMPORTED_MODULE_0__.handleDatabaseError)(error);\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database/services/connection-service.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/utils/encryption.ts":
/*!*************************************!*\
  !*** ./src/lib/utils/encryption.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   decrypt: () => (/* binding */ decrypt),\n/* harmony export */   encrypt: () => (/* binding */ encrypt),\n/* harmony export */   generateApiKey: () => (/* binding */ generateApiKey),\n/* harmony export */   generateRandomString: () => (/* binding */ generateRandomString),\n/* harmony export */   hashString: () => (/* binding */ hashString),\n/* harmony export */   simpleDecrypt: () => (/* binding */ simpleDecrypt),\n/* harmony export */   simpleEncrypt: () => (/* binding */ simpleEncrypt),\n/* harmony export */   verifyApiKey: () => (/* binding */ verifyApiKey)\n/* harmony export */ });\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_0__);\n\nconst ALGORITHM = \"aes-256-gcm\";\nconst KEY_LENGTH = 32;\nconst IV_LENGTH = 16;\nconst TAG_LENGTH = 16;\n// Get encryption key from environment variable\nfunction getEncryptionKey() {\n    const key = process.env.ENCRYPTION_KEY;\n    if (!key) {\n        throw new Error(\"ENCRYPTION_KEY environment variable is required\");\n    }\n    // Create a consistent key from the environment variable\n    return crypto__WEBPACK_IMPORTED_MODULE_0__.scryptSync(key, \"salt\", KEY_LENGTH);\n}\n// Encrypt a string (simplified for development)\nfunction encrypt(text) {\n    try {\n        // For development, we'll use a simple base64 encoding with a prefix\n        // In production, this should be proper encryption\n        const encoded = Buffer.from(text).toString(\"base64\");\n        return \"enc:\" + encoded;\n    } catch (error) {\n        console.error(\"Encryption failed:\", error);\n        throw new Error(\"Failed to encrypt data\");\n    }\n}\n// Decrypt a string (simplified for development)\nfunction decrypt(encryptedData) {\n    try {\n        // For development, we'll decode the base64 string\n        // In production, this should be proper decryption\n        if (!encryptedData.startsWith(\"enc:\")) {\n            throw new Error(\"Invalid encrypted data format\");\n        }\n        const encoded = encryptedData.substring(4);\n        const decoded = Buffer.from(encoded, \"base64\").toString(\"utf8\");\n        return decoded;\n    } catch (error) {\n        console.error(\"Decryption failed:\", error);\n        throw new Error(\"Failed to decrypt data\");\n    }\n}\n// Hash a string (for API keys, etc.)\nfunction hashString(text) {\n    return crypto__WEBPACK_IMPORTED_MODULE_0__.createHash(\"sha256\").update(text).digest(\"hex\");\n}\n// Generate a random string\nfunction generateRandomString(length = 32) {\n    return crypto__WEBPACK_IMPORTED_MODULE_0__.randomBytes(length).toString(\"hex\");\n}\n// Generate API key\nfunction generateApiKey() {\n    const key = \"qcs_\" + generateRandomString(32);\n    const hash = hashString(key);\n    return {\n        key,\n        hash\n    };\n}\n// Verify API key\nfunction verifyApiKey(key, hash) {\n    return hashString(key) === hash;\n}\n// Simple encryption for less sensitive data (using a simpler algorithm)\nfunction simpleEncrypt(text) {\n    const key = getEncryptionKey();\n    const iv = crypto__WEBPACK_IMPORTED_MODULE_0__.randomBytes(16);\n    const cipher = crypto__WEBPACK_IMPORTED_MODULE_0__.createCipher(\"aes-256-cbc\", key);\n    let encrypted = cipher.update(text, \"utf8\", \"hex\");\n    encrypted += cipher.final(\"hex\");\n    return iv.toString(\"hex\") + \":\" + encrypted;\n}\n// Simple decryption\nfunction simpleDecrypt(encryptedData) {\n    const key = getEncryptionKey();\n    const parts = encryptedData.split(\":\");\n    const encrypted = parts.length > 1 ? parts[1] : parts[0];\n    if (!encrypted) {\n        throw new Error(\"Invalid encrypted data\");\n    }\n    const decipher = crypto__WEBPACK_IMPORTED_MODULE_0__.createDecipher(\"aes-256-cbc\", key);\n    let decrypted = decipher.update(encrypted, \"hex\", \"utf8\");\n    decrypted += decipher.final(\"utf8\");\n    return decrypted;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3V0aWxzL2VuY3J5cHRpb24udHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUFpQztBQUVqQyxNQUFNQyxZQUFZO0FBQ2xCLE1BQU1DLGFBQWE7QUFDbkIsTUFBTUMsWUFBWTtBQUNsQixNQUFNQyxhQUFhO0FBRW5CLCtDQUErQztBQUMvQyxTQUFTQztJQUNQLE1BQU1DLE1BQU1DLFFBQVFDLEdBQUcsQ0FBQ0MsY0FBYztJQUN0QyxJQUFJLENBQUNILEtBQUs7UUFDUixNQUFNLElBQUlJLE1BQU07SUFDbEI7SUFFQSx3REFBd0Q7SUFDeEQsT0FBT1YsOENBQWlCLENBQUNNLEtBQUssUUFBUUo7QUFDeEM7QUFFQSxnREFBZ0Q7QUFDekMsU0FBU1UsUUFBUUMsSUFBWTtJQUNsQyxJQUFJO1FBQ0Ysb0VBQW9FO1FBQ3BFLGtEQUFrRDtRQUNsRCxNQUFNQyxVQUFVQyxPQUFPQyxJQUFJLENBQUNILE1BQU1JLFFBQVEsQ0FBQztRQUMzQyxPQUFPLFNBQVNIO0lBQ2xCLEVBQUUsT0FBT0ksT0FBTztRQUNkQyxRQUFRRCxLQUFLLENBQUMsc0JBQXNCQTtRQUNwQyxNQUFNLElBQUlSLE1BQU07SUFDbEI7QUFDRjtBQUVBLGdEQUFnRDtBQUN6QyxTQUFTVSxRQUFRQyxhQUFxQjtJQUMzQyxJQUFJO1FBQ0Ysa0RBQWtEO1FBQ2xELGtEQUFrRDtRQUNsRCxJQUFJLENBQUNBLGNBQWNDLFVBQVUsQ0FBQyxTQUFTO1lBQ3JDLE1BQU0sSUFBSVosTUFBTTtRQUNsQjtRQUVBLE1BQU1JLFVBQVVPLGNBQWNFLFNBQVMsQ0FBQztRQUN4QyxNQUFNQyxVQUFVVCxPQUFPQyxJQUFJLENBQUNGLFNBQVMsVUFBVUcsUUFBUSxDQUFDO1FBRXhELE9BQU9PO0lBQ1QsRUFBRSxPQUFPTixPQUFPO1FBQ2RDLFFBQVFELEtBQUssQ0FBQyxzQkFBc0JBO1FBQ3BDLE1BQU0sSUFBSVIsTUFBTTtJQUNsQjtBQUNGO0FBRUEscUNBQXFDO0FBQzlCLFNBQVNlLFdBQVdaLElBQVk7SUFDckMsT0FBT2IsOENBQWlCLENBQUMsVUFBVTJCLE1BQU0sQ0FBQ2QsTUFBTWUsTUFBTSxDQUFDO0FBQ3pEO0FBRUEsMkJBQTJCO0FBQ3BCLFNBQVNDLHFCQUFxQkMsU0FBaUIsRUFBRTtJQUN0RCxPQUFPOUIsK0NBQWtCLENBQUM4QixRQUFRYixRQUFRLENBQUM7QUFDN0M7QUFFQSxtQkFBbUI7QUFDWixTQUFTZTtJQUNkLE1BQU0xQixNQUFNLFNBQVN1QixxQkFBcUI7SUFDMUMsTUFBTUksT0FBT1IsV0FBV25CO0lBQ3hCLE9BQU87UUFBRUE7UUFBSzJCO0lBQUs7QUFDckI7QUFFQSxpQkFBaUI7QUFDVixTQUFTQyxhQUFhNUIsR0FBVyxFQUFFMkIsSUFBWTtJQUNwRCxPQUFPUixXQUFXbkIsU0FBUzJCO0FBQzdCO0FBRUEsd0VBQXdFO0FBQ2pFLFNBQVNFLGNBQWN0QixJQUFZO0lBQ3hDLE1BQU1QLE1BQU1EO0lBQ1osTUFBTStCLEtBQUtwQywrQ0FBa0IsQ0FBQztJQUM5QixNQUFNcUMsU0FBU3JDLGdEQUFtQixDQUFDLGVBQWVNO0lBQ2xELElBQUlpQyxZQUFZRixPQUFPVixNQUFNLENBQUNkLE1BQU0sUUFBUTtJQUM1QzBCLGFBQWFGLE9BQU9HLEtBQUssQ0FBQztJQUMxQixPQUFPSixHQUFHbkIsUUFBUSxDQUFDLFNBQVMsTUFBTXNCO0FBQ3BDO0FBRUEsb0JBQW9CO0FBQ2IsU0FBU0UsY0FBY3BCLGFBQXFCO0lBQ2pELE1BQU1mLE1BQU1EO0lBQ1osTUFBTXFDLFFBQVFyQixjQUFjc0IsS0FBSyxDQUFDO0lBQ2xDLE1BQU1KLFlBQVlHLE1BQU1aLE1BQU0sR0FBRyxJQUFJWSxLQUFLLENBQUMsRUFBRSxHQUFHQSxLQUFLLENBQUMsRUFBRTtJQUN4RCxJQUFJLENBQUNILFdBQVc7UUFDZCxNQUFNLElBQUk3QixNQUFNO0lBQ2xCO0lBQ0EsTUFBTWtDLFdBQVc1QyxrREFBcUIsQ0FBQyxlQUFlTTtJQUN0RCxJQUFJd0MsWUFBWUYsU0FBU2pCLE1BQU0sQ0FBQ1ksV0FBVyxPQUFPO0lBQ2xETyxhQUFhRixTQUFTSixLQUFLLENBQUM7SUFDNUIsT0FBT007QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL3F1ZXJ5Y3JhZnQtc3R1ZGlvLy4vc3JjL2xpYi91dGlscy9lbmNyeXB0aW9uLnRzP2M4YTUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgY3J5cHRvIGZyb20gJ2NyeXB0byc7XG5cbmNvbnN0IEFMR09SSVRITSA9ICdhZXMtMjU2LWdjbSc7XG5jb25zdCBLRVlfTEVOR1RIID0gMzI7XG5jb25zdCBJVl9MRU5HVEggPSAxNjtcbmNvbnN0IFRBR19MRU5HVEggPSAxNjtcblxuLy8gR2V0IGVuY3J5cHRpb24ga2V5IGZyb20gZW52aXJvbm1lbnQgdmFyaWFibGVcbmZ1bmN0aW9uIGdldEVuY3J5cHRpb25LZXkoKTogQnVmZmVyIHtcbiAgY29uc3Qga2V5ID0gcHJvY2Vzcy5lbnYuRU5DUllQVElPTl9LRVk7XG4gIGlmICgha2V5KSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCdFTkNSWVBUSU9OX0tFWSBlbnZpcm9ubWVudCB2YXJpYWJsZSBpcyByZXF1aXJlZCcpO1xuICB9XG4gIFxuICAvLyBDcmVhdGUgYSBjb25zaXN0ZW50IGtleSBmcm9tIHRoZSBlbnZpcm9ubWVudCB2YXJpYWJsZVxuICByZXR1cm4gY3J5cHRvLnNjcnlwdFN5bmMoa2V5LCAnc2FsdCcsIEtFWV9MRU5HVEgpO1xufVxuXG4vLyBFbmNyeXB0IGEgc3RyaW5nIChzaW1wbGlmaWVkIGZvciBkZXZlbG9wbWVudClcbmV4cG9ydCBmdW5jdGlvbiBlbmNyeXB0KHRleHQ6IHN0cmluZyk6IHN0cmluZyB7XG4gIHRyeSB7XG4gICAgLy8gRm9yIGRldmVsb3BtZW50LCB3ZSdsbCB1c2UgYSBzaW1wbGUgYmFzZTY0IGVuY29kaW5nIHdpdGggYSBwcmVmaXhcbiAgICAvLyBJbiBwcm9kdWN0aW9uLCB0aGlzIHNob3VsZCBiZSBwcm9wZXIgZW5jcnlwdGlvblxuICAgIGNvbnN0IGVuY29kZWQgPSBCdWZmZXIuZnJvbSh0ZXh0KS50b1N0cmluZygnYmFzZTY0Jyk7XG4gICAgcmV0dXJuICdlbmM6JyArIGVuY29kZWQ7XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRW5jcnlwdGlvbiBmYWlsZWQ6JywgZXJyb3IpO1xuICAgIHRocm93IG5ldyBFcnJvcignRmFpbGVkIHRvIGVuY3J5cHQgZGF0YScpO1xuICB9XG59XG5cbi8vIERlY3J5cHQgYSBzdHJpbmcgKHNpbXBsaWZpZWQgZm9yIGRldmVsb3BtZW50KVxuZXhwb3J0IGZ1bmN0aW9uIGRlY3J5cHQoZW5jcnlwdGVkRGF0YTogc3RyaW5nKTogc3RyaW5nIHtcbiAgdHJ5IHtcbiAgICAvLyBGb3IgZGV2ZWxvcG1lbnQsIHdlJ2xsIGRlY29kZSB0aGUgYmFzZTY0IHN0cmluZ1xuICAgIC8vIEluIHByb2R1Y3Rpb24sIHRoaXMgc2hvdWxkIGJlIHByb3BlciBkZWNyeXB0aW9uXG4gICAgaWYgKCFlbmNyeXB0ZWREYXRhLnN0YXJ0c1dpdGgoJ2VuYzonKSkge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCdJbnZhbGlkIGVuY3J5cHRlZCBkYXRhIGZvcm1hdCcpO1xuICAgIH1cblxuICAgIGNvbnN0IGVuY29kZWQgPSBlbmNyeXB0ZWREYXRhLnN1YnN0cmluZyg0KTtcbiAgICBjb25zdCBkZWNvZGVkID0gQnVmZmVyLmZyb20oZW5jb2RlZCwgJ2Jhc2U2NCcpLnRvU3RyaW5nKCd1dGY4Jyk7XG5cbiAgICByZXR1cm4gZGVjb2RlZDtcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdEZWNyeXB0aW9uIGZhaWxlZDonLCBlcnJvcik7XG4gICAgdGhyb3cgbmV3IEVycm9yKCdGYWlsZWQgdG8gZGVjcnlwdCBkYXRhJyk7XG4gIH1cbn1cblxuLy8gSGFzaCBhIHN0cmluZyAoZm9yIEFQSSBrZXlzLCBldGMuKVxuZXhwb3J0IGZ1bmN0aW9uIGhhc2hTdHJpbmcodGV4dDogc3RyaW5nKTogc3RyaW5nIHtcbiAgcmV0dXJuIGNyeXB0by5jcmVhdGVIYXNoKCdzaGEyNTYnKS51cGRhdGUodGV4dCkuZGlnZXN0KCdoZXgnKTtcbn1cblxuLy8gR2VuZXJhdGUgYSByYW5kb20gc3RyaW5nXG5leHBvcnQgZnVuY3Rpb24gZ2VuZXJhdGVSYW5kb21TdHJpbmcobGVuZ3RoOiBudW1iZXIgPSAzMik6IHN0cmluZyB7XG4gIHJldHVybiBjcnlwdG8ucmFuZG9tQnl0ZXMobGVuZ3RoKS50b1N0cmluZygnaGV4Jyk7XG59XG5cbi8vIEdlbmVyYXRlIEFQSSBrZXlcbmV4cG9ydCBmdW5jdGlvbiBnZW5lcmF0ZUFwaUtleSgpOiB7IGtleTogc3RyaW5nOyBoYXNoOiBzdHJpbmcgfSB7XG4gIGNvbnN0IGtleSA9ICdxY3NfJyArIGdlbmVyYXRlUmFuZG9tU3RyaW5nKDMyKTtcbiAgY29uc3QgaGFzaCA9IGhhc2hTdHJpbmcoa2V5KTtcbiAgcmV0dXJuIHsga2V5LCBoYXNoIH07XG59XG5cbi8vIFZlcmlmeSBBUEkga2V5XG5leHBvcnQgZnVuY3Rpb24gdmVyaWZ5QXBpS2V5KGtleTogc3RyaW5nLCBoYXNoOiBzdHJpbmcpOiBib29sZWFuIHtcbiAgcmV0dXJuIGhhc2hTdHJpbmcoa2V5KSA9PT0gaGFzaDtcbn1cblxuLy8gU2ltcGxlIGVuY3J5cHRpb24gZm9yIGxlc3Mgc2Vuc2l0aXZlIGRhdGEgKHVzaW5nIGEgc2ltcGxlciBhbGdvcml0aG0pXG5leHBvcnQgZnVuY3Rpb24gc2ltcGxlRW5jcnlwdCh0ZXh0OiBzdHJpbmcpOiBzdHJpbmcge1xuICBjb25zdCBrZXkgPSBnZXRFbmNyeXB0aW9uS2V5KCk7XG4gIGNvbnN0IGl2ID0gY3J5cHRvLnJhbmRvbUJ5dGVzKDE2KTtcbiAgY29uc3QgY2lwaGVyID0gY3J5cHRvLmNyZWF0ZUNpcGhlcignYWVzLTI1Ni1jYmMnLCBrZXkpO1xuICBsZXQgZW5jcnlwdGVkID0gY2lwaGVyLnVwZGF0ZSh0ZXh0LCAndXRmOCcsICdoZXgnKTtcbiAgZW5jcnlwdGVkICs9IGNpcGhlci5maW5hbCgnaGV4Jyk7XG4gIHJldHVybiBpdi50b1N0cmluZygnaGV4JykgKyAnOicgKyBlbmNyeXB0ZWQ7XG59XG5cbi8vIFNpbXBsZSBkZWNyeXB0aW9uXG5leHBvcnQgZnVuY3Rpb24gc2ltcGxlRGVjcnlwdChlbmNyeXB0ZWREYXRhOiBzdHJpbmcpOiBzdHJpbmcge1xuICBjb25zdCBrZXkgPSBnZXRFbmNyeXB0aW9uS2V5KCk7XG4gIGNvbnN0IHBhcnRzID0gZW5jcnlwdGVkRGF0YS5zcGxpdCgnOicpO1xuICBjb25zdCBlbmNyeXB0ZWQgPSBwYXJ0cy5sZW5ndGggPiAxID8gcGFydHNbMV0gOiBwYXJ0c1swXTtcbiAgaWYgKCFlbmNyeXB0ZWQpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ0ludmFsaWQgZW5jcnlwdGVkIGRhdGEnKTtcbiAgfVxuICBjb25zdCBkZWNpcGhlciA9IGNyeXB0by5jcmVhdGVEZWNpcGhlcignYWVzLTI1Ni1jYmMnLCBrZXkpO1xuICBsZXQgZGVjcnlwdGVkID0gZGVjaXBoZXIudXBkYXRlKGVuY3J5cHRlZCwgJ2hleCcsICd1dGY4Jyk7XG4gIGRlY3J5cHRlZCArPSBkZWNpcGhlci5maW5hbCgndXRmOCcpO1xuICByZXR1cm4gZGVjcnlwdGVkO1xufVxuIl0sIm5hbWVzIjpbImNyeXB0byIsIkFMR09SSVRITSIsIktFWV9MRU5HVEgiLCJJVl9MRU5HVEgiLCJUQUdfTEVOR1RIIiwiZ2V0RW5jcnlwdGlvbktleSIsImtleSIsInByb2Nlc3MiLCJlbnYiLCJFTkNSWVBUSU9OX0tFWSIsIkVycm9yIiwic2NyeXB0U3luYyIsImVuY3J5cHQiLCJ0ZXh0IiwiZW5jb2RlZCIsIkJ1ZmZlciIsImZyb20iLCJ0b1N0cmluZyIsImVycm9yIiwiY29uc29sZSIsImRlY3J5cHQiLCJlbmNyeXB0ZWREYXRhIiwic3RhcnRzV2l0aCIsInN1YnN0cmluZyIsImRlY29kZWQiLCJoYXNoU3RyaW5nIiwiY3JlYXRlSGFzaCIsInVwZGF0ZSIsImRpZ2VzdCIsImdlbmVyYXRlUmFuZG9tU3RyaW5nIiwibGVuZ3RoIiwicmFuZG9tQnl0ZXMiLCJnZW5lcmF0ZUFwaUtleSIsImhhc2giLCJ2ZXJpZnlBcGlLZXkiLCJzaW1wbGVFbmNyeXB0IiwiaXYiLCJjaXBoZXIiLCJjcmVhdGVDaXBoZXIiLCJlbmNyeXB0ZWQiLCJmaW5hbCIsInNpbXBsZURlY3J5cHQiLCJwYXJ0cyIsInNwbGl0IiwiZGVjaXBoZXIiLCJjcmVhdGVEZWNpcGhlciIsImRlY3J5cHRlZCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/utils/encryption.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/zod"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fconnections%2Froute&page=%2Fapi%2Fconnections%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fconnections%2Froute.ts&appDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fquery-craft-studio-2%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fquery-craft-studio-2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();