"use strict";(()=>{var e={};e.id=701,e.ids=[701],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},94085:(e,r,t)=>{t.r(r),t.d(r,{headerHooks:()=>b,originalPathname:()=>E,patchFetch:()=>Z,requestAsyncStorage:()=>g,routeModule:()=>f,serverHooks:()=>j,staticGenerationAsyncStorage:()=>U,staticGenerationBailout:()=>B});var a={};t.r(a),t.d(a,{DELETE:()=>m,GET:()=>p,POST:()=>h,PUT:()=>w});var s=t(95419),n=t(69108),o=t(99678),i=t(78070),u=t(68505);class c{static async createUser(e){try{return await u._B.user.create({data:e})}catch(e){(0,u.Lb)(e)}}static async getUserById(e){try{return await u._B.user.findUnique({where:{id:e},include:{databaseConnections:!0,_count:{select:{generatedQueries:!0,querySessions:!0}}}})}catch(e){(0,u.Lb)(e)}}static async getUserByEmail(e){try{return await u._B.user.findUnique({where:{email:e}})}catch(e){(0,u.Lb)(e)}}static async updateUser(e,r){try{return await u._B.user.update({where:{id:e},data:r})}catch(e){(0,u.Lb)(e)}}static async deleteUser(e){try{await u._B.user.delete({where:{id:e}})}catch(e){(0,u.Lb)(e)}}static async getUserProfile(e){try{return await u._B.user.findUnique({where:{id:e},include:{databaseConnections:{where:{isActive:!0},select:{id:!0,name:!0,databaseType:!0,lastConnectedAt:!0}},teamMemberships:{include:{team:{select:{id:!0,name:!0,description:!0}}}},_count:{select:{generatedQueries:!0,querySessions:!0,databaseConnections:!0}}}})}catch(e){(0,u.Lb)(e)}}static async getUserStats(e){try{let[r,t,a,s]=await Promise.all([u._B.generatedQuery.count({where:{userId:e}}),u._B.querySession.count({where:{userId:e}}),u._B.generatedQuery.count({where:{userId:e,createdAt:{gte:new Date(Date.now()-6048e5)}}}),u._B.generatedQuery.groupBy({by:["databaseType"],where:{userId:e},_count:{id:!0},orderBy:{_count:{id:"desc"}},take:3})]);return{totalQueries:r,totalSessions:t,recentQueries:a,topDatabases:s}}catch(e){(0,u.Lb)(e)}}}var d=t(88302);let l=d.z.object({email:d.z.string().email(),name:d.z.string().min(1).max(255),avatar:d.z.string().url().optional()}),y=d.z.object({name:d.z.string().min(1).max(255).optional(),avatar:d.z.string().url().optional()});async function p(e){try{let{searchParams:r}=new URL(e.url),t=r.get("email"),a=r.get("id");if(t){let e=await c.getUserByEmail(t);if(!e)return i.Z.json({error:"User not found"},{status:404});return i.Z.json(e)}if(a){let e=await c.getUserById(a);if(!e)return i.Z.json({error:"User not found"},{status:404});return i.Z.json(e)}return i.Z.json({error:"Email or ID parameter required"},{status:400})}catch(e){return console.error("GET /api/users error:",e),i.Z.json({error:"Internal server error"},{status:500})}}async function h(e){try{let r=await e.json(),t=l.parse(r);if(await c.getUserByEmail(t.email))return i.Z.json({error:"User with this email already exists"},{status:409});let a=await c.createUser(t);return i.Z.json(a,{status:201})}catch(e){if(e instanceof d.z.ZodError)return i.Z.json({error:"Validation failed",details:e.errors},{status:400});return console.error("POST /api/users error:",e),i.Z.json({error:"Internal server error"},{status:500})}}async function w(e){try{let{searchParams:r}=new URL(e.url),t=r.get("id");if(!t)return i.Z.json({error:"User ID required"},{status:400});let a=await e.json(),s=y.parse(a),n=await c.updateUser(t,s);return i.Z.json(n)}catch(e){if(e instanceof d.z.ZodError)return i.Z.json({error:"Validation failed",details:e.errors},{status:400});return console.error("PUT /api/users error:",e),i.Z.json({error:"Internal server error"},{status:500})}}async function m(e){try{let{searchParams:r}=new URL(e.url),t=r.get("id");if(!t)return i.Z.json({error:"User ID required"},{status:400});return await c.deleteUser(t),i.Z.json({success:!0})}catch(e){return console.error("DELETE /api/users error:",e),i.Z.json({error:"Internal server error"},{status:500})}}let f=new s.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/users/route",pathname:"/api/users",filename:"route",bundlePath:"app/api/users/route"},resolvedPagePath:"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/app/api/users/route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:g,staticGenerationAsyncStorage:U,serverHooks:j,headerHooks:b,staticGenerationBailout:B}=f,E="/api/users/route";function Z(){return(0,o.patchFetch)({serverHooks:j,staticGenerationAsyncStorage:U})}},68505:(e,r,t)=>{t.d(r,{Lb:()=>n,_B:()=>s});let a=require("@prisma/client"),s=globalThis.prisma??new a.PrismaClient({log:["query","error","warn"],errorFormat:"pretty"});function n(e){if(console.error("Database error:",e),"P2002"===e.code)throw Error("A record with this information already exists");if("P2025"===e.code)throw Error("Record not found");if("P2003"===e.code)throw Error("Foreign key constraint failed");throw Error("Database operation failed")}}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[638,543],()=>t(94085));module.exports=a})();