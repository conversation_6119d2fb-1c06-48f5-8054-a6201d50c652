(()=>{var e={};e.id=931,e.ids=[931],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},49994:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>a.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var s=t(50482),o=t(69108),n=t(62563),a=t.n(n),l=t(68300),i={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);t.d(r,i);let c=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,25779)),"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/app/page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,98890)),"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"]}],d=["/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/app/page.tsx"],u="/page",p={require:t,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},37954:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,61476,23))},48026:(e,r,t)=>{let{createProxy:s}=t(86843);e.exports=s("/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/node_modules/next/dist/client/link.js")},40646:(e,r,t)=>{e.exports=t(48026)},25779:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>eM,metadata:()=>eP});var s=t(25036),o=t(40646),n=t.n(o),a=t(40002);function l(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}var i=function(e){let r=function(e){let r=a.forwardRef((e,r)=>{let{children:t,...s}=e;if(a.isValidElement(t)){let e,o;let n=(e=Object.getOwnPropertyDescriptor(t.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?t.ref:(e=Object.getOwnPropertyDescriptor(t,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?t.props.ref:t.props.ref||t.ref,i=function(e,r){let t={...r};for(let s in r){let o=e[s],n=r[s];/^on[A-Z]/.test(s)?o&&n?t[s]=(...e)=>{let r=n(...e);return o(...e),r}:o&&(t[s]=o):"style"===s?t[s]={...o,...n}:"className"===s&&(t[s]=[o,n].filter(Boolean).join(" "))}return{...e,...t}}(s,t.props);return t.type!==a.Fragment&&(i.ref=r?function(...e){return r=>{let t=!1,s=e.map(e=>{let s=l(e,r);return t||"function"!=typeof s||(t=!0),s});if(t)return()=>{for(let r=0;r<s.length;r++){let t=s[r];"function"==typeof t?t():l(e[r],null)}}}}(r,n):n),a.cloneElement(t,i)}return a.Children.count(t)>1?a.Children.only(null):null});return r.displayName=`${e}.SlotClone`,r}(e),t=a.forwardRef((e,t)=>{let{children:o,...n}=e,l=a.Children.toArray(o),i=l.find(d);if(i){let e=i.props.children,o=l.map(r=>r!==i?r:a.Children.count(e)>1?a.Children.only(null):a.isValidElement(e)?e.props.children:null);return(0,s.jsx)(r,{...n,ref:t,children:a.isValidElement(e)?a.cloneElement(e,void 0,o):null})}return(0,s.jsx)(r,{...n,ref:t,children:o})});return t.displayName=`${e}.Slot`,t}("Slot"),c=Symbol("radix.slottable");function d(e){return a.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===c}function u(){for(var e,r,t=0,s="",o=arguments.length;t<o;t++)(e=arguments[t])&&(r=function e(r){var t,s,o="";if("string"==typeof r||"number"==typeof r)o+=r;else if("object"==typeof r){if(Array.isArray(r)){var n=r.length;for(t=0;t<n;t++)r[t]&&(s=e(r[t]))&&(o&&(o+=" "),o+=s)}else for(s in r)r[s]&&(o&&(o+=" "),o+=s)}return o}(e))&&(s&&(s+=" "),s+=r);return s}let p=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,m=(e,r)=>t=>{var s;if((null==r?void 0:r.variants)==null)return u(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:o,defaultVariants:n}=r,a=Object.keys(o).map(e=>{let r=null==t?void 0:t[e],s=null==n?void 0:n[e];if(null===r)return null;let a=p(r)||p(s);return o[e][a]}),l=t&&Object.entries(t).reduce((e,r)=>{let[t,s]=r;return void 0===s||(e[t]=s),e},{});return u(e,a,null==r?void 0:null===(s=r.compoundVariants)||void 0===s?void 0:s.reduce((e,r)=>{let{class:t,className:s,...o}=r;return Object.entries(o).every(e=>{let[r,t]=e;return Array.isArray(t)?t.includes({...n,...l}[r]):({...n,...l})[r]===t})?[...e,t,s]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)},x=e=>{let r=b(e),{conflictingClassGroups:t,conflictingClassGroupModifiers:s}=e;return{getClassGroupId:e=>{let t=e.split("-");return""===t[0]&&1!==t.length&&t.shift(),f(t,r)||g(e)},getConflictingClassGroupIds:(e,r)=>{let o=t[e]||[];return r&&s[e]?[...o,...s[e]]:o}}},f=(e,r)=>{if(0===e.length)return r.classGroupId;let t=e[0],s=r.nextPart.get(t),o=s?f(e.slice(1),s):void 0;if(o)return o;if(0===r.validators.length)return;let n=e.join("-");return r.validators.find(({validator:e})=>e(n))?.classGroupId},h=/^\[(.+)\]$/,g=e=>{if(h.test(e)){let r=h.exec(e)[1],t=r?.substring(0,r.indexOf(":"));if(t)return"arbitrary.."+t}},b=e=>{let{theme:r,prefix:t}=e,s={nextPart:new Map,validators:[]};return j(Object.entries(e.classGroups),t).forEach(([e,t])=>{y(t,s,e,r)}),s},y=(e,r,t,s)=>{e.forEach(e=>{if("string"==typeof e){(""===e?r:v(r,e)).classGroupId=t;return}if("function"==typeof e){if(w(e)){y(e(s),r,t,s);return}r.validators.push({validator:e,classGroupId:t});return}Object.entries(e).forEach(([e,o])=>{y(o,v(r,e),t,s)})})},v=(e,r)=>{let t=e;return r.split("-").forEach(e=>{t.nextPart.has(e)||t.nextPart.set(e,{nextPart:new Map,validators:[]}),t=t.nextPart.get(e)}),t},w=e=>e.isThemeGetter,j=(e,r)=>r?e.map(([e,t])=>[e,t.map(e=>"string"==typeof e?r+e:"object"==typeof e?Object.fromEntries(Object.entries(e).map(([e,t])=>[r+e,t])):e)]):e,N=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let r=0,t=new Map,s=new Map,o=(o,n)=>{t.set(o,n),++r>e&&(r=0,s=t,t=new Map)};return{get(e){let r=t.get(e);return void 0!==r?r:void 0!==(r=s.get(e))?(o(e,r),r):void 0},set(e,r){t.has(e)?t.set(e,r):o(e,r)}}},k=e=>{let{separator:r,experimentalParseClassName:t}=e,s=1===r.length,o=r[0],n=r.length,a=e=>{let t;let a=[],l=0,i=0;for(let c=0;c<e.length;c++){let d=e[c];if(0===l){if(d===o&&(s||e.slice(c,c+n)===r)){a.push(e.slice(i,c)),i=c+n;continue}if("/"===d){t=c;continue}}"["===d?l++:"]"===d&&l--}let c=0===a.length?e:e.substring(i),d=c.startsWith("!"),u=d?c.substring(1):c;return{modifiers:a,hasImportantModifier:d,baseClassName:u,maybePostfixModifierPosition:t&&t>i?t-i:void 0}};return t?e=>t({className:e,parseClassName:a}):a},z=e=>{if(e.length<=1)return e;let r=[],t=[];return e.forEach(e=>{"["===e[0]?(r.push(...t.sort(),e),t=[]):t.push(e)}),r.push(...t.sort()),r},q=e=>({cache:N(e.cacheSize),parseClassName:k(e),...x(e)}),S=/\s+/,C=(e,r)=>{let{parseClassName:t,getClassGroupId:s,getConflictingClassGroupIds:o}=r,n=[],a=e.trim().split(S),l="";for(let e=a.length-1;e>=0;e-=1){let r=a[e],{modifiers:i,hasImportantModifier:c,baseClassName:d,maybePostfixModifierPosition:u}=t(r),p=!!u,m=s(p?d.substring(0,u):d);if(!m){if(!p||!(m=s(d))){l=r+(l.length>0?" "+l:l);continue}p=!1}let x=z(i).join(":"),f=c?x+"!":x,h=f+m;if(n.includes(h))continue;n.push(h);let g=o(m,p);for(let e=0;e<g.length;++e){let r=g[e];n.push(f+r)}l=r+(l.length>0?" "+l:l)}return l};function P(){let e,r,t=0,s="";for(;t<arguments.length;)(e=arguments[t++])&&(r=M(e))&&(s&&(s+=" "),s+=r);return s}let M=e=>{let r;if("string"==typeof e)return e;let t="";for(let s=0;s<e.length;s++)e[s]&&(r=M(e[s]))&&(t&&(t+=" "),t+=r);return t},L=e=>{let r=r=>r[e]||[];return r.isThemeGetter=!0,r},A=/^\[(?:([a-z-]+):)?(.+)\]$/i,R=/^\d+\/\d+$/,_=new Set(["px","full","screen"]),E=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,I=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,Q=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,D=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,G=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,O=e=>$(e)||_.has(e)||R.test(e),V=e=>er(e,"length",et),$=e=>!!e&&!Number.isNaN(Number(e)),T=e=>er(e,"number",$),W=e=>!!e&&Number.isInteger(Number(e)),Z=e=>e.endsWith("%")&&$(e.slice(0,-1)),U=e=>A.test(e),B=e=>E.test(e),F=new Set(["length","size","percentage"]),H=e=>er(e,F,es),Y=e=>er(e,"position",es),J=new Set(["image","url"]),X=e=>er(e,J,en),K=e=>er(e,"",eo),ee=()=>!0,er=(e,r,t)=>{let s=A.exec(e);return!!s&&(s[1]?"string"==typeof r?s[1]===r:r.has(s[1]):t(s[2]))},et=e=>I.test(e)&&!Q.test(e),es=()=>!1,eo=e=>D.test(e),en=e=>G.test(e);Symbol.toStringTag;let ea=function(e){let r,t,s;let o=function(a){return t=(r=q([].reduce((e,r)=>r(e),e()))).cache.get,s=r.cache.set,o=n,n(a)};function n(e){let o=t(e);if(o)return o;let n=C(e,r);return s(e,n),n}return function(){return o(P.apply(null,arguments))}}(()=>{let e=L("colors"),r=L("spacing"),t=L("blur"),s=L("brightness"),o=L("borderColor"),n=L("borderRadius"),a=L("borderSpacing"),l=L("borderWidth"),i=L("contrast"),c=L("grayscale"),d=L("hueRotate"),u=L("invert"),p=L("gap"),m=L("gradientColorStops"),x=L("gradientColorStopPositions"),f=L("inset"),h=L("margin"),g=L("opacity"),b=L("padding"),y=L("saturate"),v=L("scale"),w=L("sepia"),j=L("skew"),N=L("space"),k=L("translate"),z=()=>["auto","contain","none"],q=()=>["auto","hidden","clip","visible","scroll"],S=()=>["auto",U,r],C=()=>[U,r],P=()=>["",O,V],M=()=>["auto",$,U],A=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],R=()=>["solid","dashed","dotted","double","none"],_=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],E=()=>["start","end","center","between","around","evenly","stretch"],I=()=>["","0",U],Q=()=>["auto","avoid","all","avoid-page","page","left","right","column"],D=()=>[$,U];return{cacheSize:500,separator:":",theme:{colors:[ee],spacing:[O,V],blur:["none","",B,U],brightness:D(),borderColor:[e],borderRadius:["none","","full",B,U],borderSpacing:C(),borderWidth:P(),contrast:D(),grayscale:I(),hueRotate:D(),invert:I(),gap:C(),gradientColorStops:[e],gradientColorStopPositions:[Z,V],inset:S(),margin:S(),opacity:D(),padding:C(),saturate:D(),scale:D(),sepia:I(),skew:D(),space:C(),translate:C()},classGroups:{aspect:[{aspect:["auto","square","video",U]}],container:["container"],columns:[{columns:[B]}],"break-after":[{"break-after":Q()}],"break-before":[{"break-before":Q()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...A(),U]}],overflow:[{overflow:q()}],"overflow-x":[{"overflow-x":q()}],"overflow-y":[{"overflow-y":q()}],overscroll:[{overscroll:z()}],"overscroll-x":[{"overscroll-x":z()}],"overscroll-y":[{"overscroll-y":z()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[f]}],"inset-x":[{"inset-x":[f]}],"inset-y":[{"inset-y":[f]}],start:[{start:[f]}],end:[{end:[f]}],top:[{top:[f]}],right:[{right:[f]}],bottom:[{bottom:[f]}],left:[{left:[f]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",W,U]}],basis:[{basis:S()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",U]}],grow:[{grow:I()}],shrink:[{shrink:I()}],order:[{order:["first","last","none",W,U]}],"grid-cols":[{"grid-cols":[ee]}],"col-start-end":[{col:["auto",{span:["full",W,U]},U]}],"col-start":[{"col-start":M()}],"col-end":[{"col-end":M()}],"grid-rows":[{"grid-rows":[ee]}],"row-start-end":[{row:["auto",{span:[W,U]},U]}],"row-start":[{"row-start":M()}],"row-end":[{"row-end":M()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",U]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",U]}],gap:[{gap:[p]}],"gap-x":[{"gap-x":[p]}],"gap-y":[{"gap-y":[p]}],"justify-content":[{justify:["normal",...E()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...E(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...E(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[b]}],px:[{px:[b]}],py:[{py:[b]}],ps:[{ps:[b]}],pe:[{pe:[b]}],pt:[{pt:[b]}],pr:[{pr:[b]}],pb:[{pb:[b]}],pl:[{pl:[b]}],m:[{m:[h]}],mx:[{mx:[h]}],my:[{my:[h]}],ms:[{ms:[h]}],me:[{me:[h]}],mt:[{mt:[h]}],mr:[{mr:[h]}],mb:[{mb:[h]}],ml:[{ml:[h]}],"space-x":[{"space-x":[N]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[N]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",U,r]}],"min-w":[{"min-w":[U,r,"min","max","fit"]}],"max-w":[{"max-w":[U,r,"none","full","min","max","fit","prose",{screen:[B]},B]}],h:[{h:[U,r,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[U,r,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[U,r,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[U,r,"auto","min","max","fit"]}],"font-size":[{text:["base",B,V]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",T]}],"font-family":[{font:[ee]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",U]}],"line-clamp":[{"line-clamp":["none",$,T]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",O,U]}],"list-image":[{"list-image":["none",U]}],"list-style-type":[{list:["none","disc","decimal",U]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[g]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[g]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...R(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",O,V]}],"underline-offset":[{"underline-offset":["auto",O,U]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:C()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",U]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",U]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[g]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...A(),Y]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",H]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},X]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[x]}],"gradient-via-pos":[{via:[x]}],"gradient-to-pos":[{to:[x]}],"gradient-from":[{from:[m]}],"gradient-via":[{via:[m]}],"gradient-to":[{to:[m]}],rounded:[{rounded:[n]}],"rounded-s":[{"rounded-s":[n]}],"rounded-e":[{"rounded-e":[n]}],"rounded-t":[{"rounded-t":[n]}],"rounded-r":[{"rounded-r":[n]}],"rounded-b":[{"rounded-b":[n]}],"rounded-l":[{"rounded-l":[n]}],"rounded-ss":[{"rounded-ss":[n]}],"rounded-se":[{"rounded-se":[n]}],"rounded-ee":[{"rounded-ee":[n]}],"rounded-es":[{"rounded-es":[n]}],"rounded-tl":[{"rounded-tl":[n]}],"rounded-tr":[{"rounded-tr":[n]}],"rounded-br":[{"rounded-br":[n]}],"rounded-bl":[{"rounded-bl":[n]}],"border-w":[{border:[l]}],"border-w-x":[{"border-x":[l]}],"border-w-y":[{"border-y":[l]}],"border-w-s":[{"border-s":[l]}],"border-w-e":[{"border-e":[l]}],"border-w-t":[{"border-t":[l]}],"border-w-r":[{"border-r":[l]}],"border-w-b":[{"border-b":[l]}],"border-w-l":[{"border-l":[l]}],"border-opacity":[{"border-opacity":[g]}],"border-style":[{border:[...R(),"hidden"]}],"divide-x":[{"divide-x":[l]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[l]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[g]}],"divide-style":[{divide:R()}],"border-color":[{border:[o]}],"border-color-x":[{"border-x":[o]}],"border-color-y":[{"border-y":[o]}],"border-color-s":[{"border-s":[o]}],"border-color-e":[{"border-e":[o]}],"border-color-t":[{"border-t":[o]}],"border-color-r":[{"border-r":[o]}],"border-color-b":[{"border-b":[o]}],"border-color-l":[{"border-l":[o]}],"divide-color":[{divide:[o]}],"outline-style":[{outline:["",...R()]}],"outline-offset":[{"outline-offset":[O,U]}],"outline-w":[{outline:[O,V]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:P()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[g]}],"ring-offset-w":[{"ring-offset":[O,V]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",B,K]}],"shadow-color":[{shadow:[ee]}],opacity:[{opacity:[g]}],"mix-blend":[{"mix-blend":[..._(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":_()}],filter:[{filter:["","none"]}],blur:[{blur:[t]}],brightness:[{brightness:[s]}],contrast:[{contrast:[i]}],"drop-shadow":[{"drop-shadow":["","none",B,U]}],grayscale:[{grayscale:[c]}],"hue-rotate":[{"hue-rotate":[d]}],invert:[{invert:[u]}],saturate:[{saturate:[y]}],sepia:[{sepia:[w]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[t]}],"backdrop-brightness":[{"backdrop-brightness":[s]}],"backdrop-contrast":[{"backdrop-contrast":[i]}],"backdrop-grayscale":[{"backdrop-grayscale":[c]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[d]}],"backdrop-invert":[{"backdrop-invert":[u]}],"backdrop-opacity":[{"backdrop-opacity":[g]}],"backdrop-saturate":[{"backdrop-saturate":[y]}],"backdrop-sepia":[{"backdrop-sepia":[w]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[a]}],"border-spacing-x":[{"border-spacing-x":[a]}],"border-spacing-y":[{"border-spacing-y":[a]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",U]}],duration:[{duration:D()}],ease:[{ease:["linear","in","out","in-out",U]}],delay:[{delay:D()}],animate:[{animate:["none","spin","ping","pulse","bounce",U]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[v]}],"scale-x":[{"scale-x":[v]}],"scale-y":[{"scale-y":[v]}],rotate:[{rotate:[W,U]}],"translate-x":[{"translate-x":[k]}],"translate-y":[{"translate-y":[k]}],"skew-x":[{"skew-x":[j]}],"skew-y":[{"skew-y":[j]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",U]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",U]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":C()}],"scroll-mx":[{"scroll-mx":C()}],"scroll-my":[{"scroll-my":C()}],"scroll-ms":[{"scroll-ms":C()}],"scroll-me":[{"scroll-me":C()}],"scroll-mt":[{"scroll-mt":C()}],"scroll-mr":[{"scroll-mr":C()}],"scroll-mb":[{"scroll-mb":C()}],"scroll-ml":[{"scroll-ml":C()}],"scroll-p":[{"scroll-p":C()}],"scroll-px":[{"scroll-px":C()}],"scroll-py":[{"scroll-py":C()}],"scroll-ps":[{"scroll-ps":C()}],"scroll-pe":[{"scroll-pe":C()}],"scroll-pt":[{"scroll-pt":C()}],"scroll-pr":[{"scroll-pr":C()}],"scroll-pb":[{"scroll-pb":C()}],"scroll-pl":[{"scroll-pl":C()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",U]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[O,V,T]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}});function el(...e){return ea(u(e))}let ei=m("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),ec=a.forwardRef(({className:e,variant:r,size:t,asChild:o=!1,...n},a)=>{let l=o?i:"button";return s.jsx(l,{className:el(ei({variant:r,size:t,className:e})),ref:a,...n})});ec.displayName="Button";let ed=a.forwardRef(({className:e,...r},t)=>s.jsx("div",{ref:t,className:el("rounded-lg border bg-card text-card-foreground shadow-sm",e),...r}));ed.displayName="Card";let eu=a.forwardRef(({className:e,...r},t)=>s.jsx("div",{ref:t,className:el("flex flex-col space-y-1.5 p-6",e),...r}));eu.displayName="CardHeader";let ep=a.forwardRef(({className:e,...r},t)=>s.jsx("h3",{ref:t,className:el("text-2xl font-semibold leading-none tracking-tight",e),...r}));ep.displayName="CardTitle";let em=a.forwardRef(({className:e,...r},t)=>s.jsx("p",{ref:t,className:el("text-sm text-muted-foreground",e),...r}));em.displayName="CardDescription";let ex=a.forwardRef(({className:e,...r},t)=>s.jsx("div",{ref:t,className:el("p-6 pt-0",e),...r}));ex.displayName="CardContent",a.forwardRef(({className:e,...r},t)=>s.jsx("div",{ref:t,className:el("flex items-center p-6 pt-0",e),...r})).displayName="CardFooter";let ef=m("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function eh({className:e,variant:r,...t}){return s.jsx("div",{className:el(ef({variant:r}),e),...t})}/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var eg={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let eb=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),ey=(e,r)=>{let t=(0,a.forwardRef)(({color:t="currentColor",size:s=24,strokeWidth:o=2,absoluteStrokeWidth:n,className:l="",children:i,...c},d)=>(0,a.createElement)("svg",{ref:d,...eg,width:s,height:s,stroke:t,strokeWidth:n?24*Number(o)/Number(s):o,className:["lucide",`lucide-${eb(e)}`,l].join(" "),...c},[...r.map(([e,r])=>(0,a.createElement)(e,r)),...Array.isArray(i)?i:[i]]));return t.displayName=`${e}`,t},ev=ey("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]),ew=ey("Sparkles",[["path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z",key:"17u4zn"}],["path",{d:"M5 3v4",key:"bklmnn"}],["path",{d:"M19 17v4",key:"iiml17"}],["path",{d:"M3 5h4",key:"nem4j1"}],["path",{d:"M17 19h4",key:"lbex7p"}]]),ej=ey("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]),eN=ey("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),ek=ey("BrainCircuit",[["path",{d:"M12 4.5a2.5 2.5 0 0 0-4.96-.46 2.5 2.5 0 0 0-1.98 3 2.5 2.5 0 0 0-1.32 4.24 3 3 0 0 0 .34 5.58 2.5 2.5 0 0 0 2.96 3.08 2.5 2.5 0 0 0 4.91.05L12 20V4.5Z",key:"ixwj2a"}],["path",{d:"M16 8V5c0-1.1.9-2 2-2",key:"13dx7u"}],["path",{d:"M12 13h4",key:"1ku699"}],["path",{d:"M12 18h6a2 2 0 0 1 2 2v1",key:"105ag5"}],["path",{d:"M12 8h8",key:"1lhi5i"}],["path",{d:"M20.5 8a.5.5 0 1 1-1 0 .5.5 0 0 1 1 0Z",key:"1s25gz"}],["path",{d:"M16.5 13a.5.5 0 1 1-1 0 .5.5 0 0 1 1 0Z",key:"127460"}],["path",{d:"M20.5 21a.5.5 0 1 1-1 0 .5.5 0 0 1 1 0Z",key:"fys062"}],["path",{d:"M18.5 3a.5.5 0 1 1-1 0 .5.5 0 0 1 1 0Z",key:"1vib61"}]]),ez=ey("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]]),eq=ey("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]]),eS=ey("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]),eC=ey("ShieldCheck",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]]),eP={title:"QueryCraft Studio - AI-Powered SQL Development Platform",description:"Transform your SQL development workflow with intelligent AI agents specialized in MySQL and PostgreSQL optimization."};function eM(){return(0,s.jsxs)("div",{className:"flex flex-col min-h-screen",children:[s.jsx("header",{className:"border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:(0,s.jsxs)("div",{className:"container-responsive flex h-16 items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[s.jsx(ev,{className:"h-8 w-8 text-primary"}),s.jsx("span",{className:"text-xl font-bold",children:"QueryCraft Studio"})]}),(0,s.jsxs)("nav",{className:"hidden md:flex items-center gap-6",children:[s.jsx(n(),{href:"#features",className:"text-sm font-medium hover:text-primary transition-colors",children:"Features"}),s.jsx(n(),{href:"#pricing",className:"text-sm font-medium hover:text-primary transition-colors",children:"Pricing"}),s.jsx(n(),{href:"#docs",className:"text-sm font-medium hover:text-primary transition-colors",children:"Docs"}),s.jsx(ec,{variant:"outline",size:"sm",asChild:!0,children:s.jsx(n(),{href:"/auth/signin",children:"Sign In"})}),s.jsx(ec,{size:"sm",asChild:!0,children:s.jsx(n(),{href:"/auth/signup",children:"Get Started"})})]})]})}),s.jsx("section",{className:"flex-1 flex items-center justify-center py-20 px-4",children:(0,s.jsxs)("div",{className:"container-responsive text-center space-y-8",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)(eh,{variant:"secondary",className:"mb-4",children:[s.jsx(ew,{className:"h-3 w-3 mr-1"}),"AI-Powered SQL Development"]}),(0,s.jsxs)("h1",{className:"text-4xl md:text-6xl font-bold tracking-tight",children:["Transform Your"," ",s.jsx("span",{className:"querycraft-gradient bg-clip-text text-transparent",children:"SQL Development"})]}),s.jsx("p",{className:"text-xl text-muted-foreground max-w-3xl mx-auto",children:"Leverage the power of specialized AI agents to write, debug, optimize, and document your SQL queries with unprecedented intelligence and efficiency."})]}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[s.jsx(ec,{size:"lg",className:"text-lg px-8",asChild:!0,children:(0,s.jsxs)(n(),{href:"/dashboard",children:["Start Building",s.jsx(ej,{className:"ml-2 h-5 w-5"})]})}),s.jsx(ec,{variant:"outline",size:"lg",className:"text-lg px-8",asChild:!0,children:s.jsx(n(),{href:"#demo",children:"Watch Demo"})})]}),(0,s.jsxs)("div",{className:"flex items-center justify-center gap-8 text-sm text-muted-foreground",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[s.jsx(eN,{className:"h-4 w-4 text-querycraft-green-600"}),"Free to start"]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[s.jsx(eN,{className:"h-4 w-4 text-querycraft-green-600"}),"No credit card required"]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[s.jsx(eN,{className:"h-4 w-4 text-querycraft-green-600"}),"MySQL & PostgreSQL"]})]})]})}),s.jsx("section",{id:"features",className:"py-20 bg-muted/50",children:(0,s.jsxs)("div",{className:"container-responsive",children:[(0,s.jsxs)("div",{className:"text-center space-y-4 mb-16",children:[s.jsx("h2",{className:"text-3xl md:text-4xl font-bold",children:"Intelligent SQL Development"}),s.jsx("p",{className:"text-xl text-muted-foreground max-w-2xl mx-auto",children:"Seven specialized AI agents working together to revolutionize your SQL workflow"})]}),(0,s.jsxs)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-8",children:[(0,s.jsxs)(ed,{className:"querycraft-card",children:[(0,s.jsxs)(eu,{children:[s.jsx(ek,{className:"h-10 w-10 text-primary mb-2"}),s.jsx(ep,{children:"Natural Language to SQL"}),s.jsx(em,{children:"Describe your data needs in plain English and get optimized SQL queries instantly"})]}),s.jsx(ex,{children:(0,s.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,s.jsxs)("li",{className:"flex items-center gap-2",children:[s.jsx(eN,{className:"h-4 w-4 text-querycraft-green-600"}),"Complex query generation"]}),(0,s.jsxs)("li",{className:"flex items-center gap-2",children:[s.jsx(eN,{className:"h-4 w-4 text-querycraft-green-600"}),"Context-aware suggestions"]}),(0,s.jsxs)("li",{className:"flex items-center gap-2",children:[s.jsx(eN,{className:"h-4 w-4 text-querycraft-green-600"}),"Multi-table joins"]})]})})]}),(0,s.jsxs)(ed,{className:"querycraft-card",children:[(0,s.jsxs)(eu,{children:[s.jsx(ez,{className:"h-10 w-10 text-primary mb-2"}),s.jsx(ep,{children:"Interactive Debugging"}),s.jsx(em,{children:"Get instant error analysis and step-by-step fixes for your SQL queries"})]}),s.jsx(ex,{children:(0,s.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,s.jsxs)("li",{className:"flex items-center gap-2",children:[s.jsx(eN,{className:"h-4 w-4 text-querycraft-green-600"}),"Syntax error detection"]}),(0,s.jsxs)("li",{className:"flex items-center gap-2",children:[s.jsx(eN,{className:"h-4 w-4 text-querycraft-green-600"}),"Logic validation"]}),(0,s.jsxs)("li",{className:"flex items-center gap-2",children:[s.jsx(eN,{className:"h-4 w-4 text-querycraft-green-600"}),"Automated fixes"]})]})})]}),(0,s.jsxs)(ed,{className:"querycraft-card",children:[(0,s.jsxs)(eu,{children:[s.jsx(eq,{className:"h-10 w-10 text-primary mb-2"}),s.jsx(ep,{children:"Performance Optimization"}),s.jsx(em,{children:"Database-specific recommendations to make your queries lightning fast"})]}),s.jsx(ex,{children:(0,s.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,s.jsxs)("li",{className:"flex items-center gap-2",children:[s.jsx(eN,{className:"h-4 w-4 text-querycraft-green-600"}),"Index suggestions"]}),(0,s.jsxs)("li",{className:"flex items-center gap-2",children:[s.jsx(eN,{className:"h-4 w-4 text-querycraft-green-600"}),"Query rewriting"]}),(0,s.jsxs)("li",{className:"flex items-center gap-2",children:[s.jsx(eN,{className:"h-4 w-4 text-querycraft-green-600"}),"Performance metrics"]})]})})]}),(0,s.jsxs)(ed,{className:"querycraft-card",children:[(0,s.jsxs)(eu,{children:[s.jsx(ev,{className:"h-10 w-10 text-primary mb-2"}),s.jsx(ep,{children:"Schema Intelligence"}),s.jsx(em,{children:"Intelligent discovery and analysis of your database structure and relationships"})]}),s.jsx(ex,{children:(0,s.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,s.jsxs)("li",{className:"flex items-center gap-2",children:[s.jsx(eN,{className:"h-4 w-4 text-querycraft-green-600"}),"Relationship mapping"]}),(0,s.jsxs)("li",{className:"flex items-center gap-2",children:[s.jsx(eN,{className:"h-4 w-4 text-querycraft-green-600"}),"Data profiling"]}),(0,s.jsxs)("li",{className:"flex items-center gap-2",children:[s.jsx(eN,{className:"h-4 w-4 text-querycraft-green-600"}),"Usage analytics"]})]})})]}),(0,s.jsxs)(ed,{className:"querycraft-card",children:[(0,s.jsxs)(eu,{children:[s.jsx(eS,{className:"h-10 w-10 text-primary mb-2"}),s.jsx(ep,{children:"Team Collaboration"}),s.jsx(em,{children:"Share queries, review code, and build knowledge bases with your team"})]}),s.jsx(ex,{children:(0,s.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,s.jsxs)("li",{className:"flex items-center gap-2",children:[s.jsx(eN,{className:"h-4 w-4 text-querycraft-green-600"}),"Shared workspaces"]}),(0,s.jsxs)("li",{className:"flex items-center gap-2",children:[s.jsx(eN,{className:"h-4 w-4 text-querycraft-green-600"}),"Version control"]}),(0,s.jsxs)("li",{className:"flex items-center gap-2",children:[s.jsx(eN,{className:"h-4 w-4 text-querycraft-green-600"}),"Code reviews"]})]})})]}),(0,s.jsxs)(ed,{className:"querycraft-card",children:[(0,s.jsxs)(eu,{children:[s.jsx(eC,{className:"h-10 w-10 text-primary mb-2"}),s.jsx(ep,{children:"Enterprise Security"}),s.jsx(em,{children:"SOC 2 compliant with advanced security controls and audit logging"})]}),s.jsx(ex,{children:(0,s.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,s.jsxs)("li",{className:"flex items-center gap-2",children:[s.jsx(eN,{className:"h-4 w-4 text-querycraft-green-600"}),"End-to-end encryption"]}),(0,s.jsxs)("li",{className:"flex items-center gap-2",children:[s.jsx(eN,{className:"h-4 w-4 text-querycraft-green-600"}),"SSO integration"]}),(0,s.jsxs)("li",{className:"flex items-center gap-2",children:[s.jsx(eN,{className:"h-4 w-4 text-querycraft-green-600"}),"Audit trails"]})]})})]})]})]})}),s.jsx("section",{className:"py-20",children:(0,s.jsxs)("div",{className:"container-responsive text-center space-y-8",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[s.jsx("h2",{className:"text-3xl md:text-4xl font-bold",children:"Ready to Transform Your SQL Development?"}),s.jsx("p",{className:"text-xl text-muted-foreground max-w-2xl mx-auto",children:"Join thousands of developers who are already building better SQL with AI assistance"})]}),s.jsx(ec,{size:"lg",className:"text-lg px-8",asChild:!0,children:(0,s.jsxs)(n(),{href:"/dashboard",children:["Get Started for Free",s.jsx(ej,{className:"ml-2 h-5 w-5"})]})})]})}),s.jsx("footer",{className:"border-t bg-muted/50",children:s.jsx("div",{className:"container-responsive py-8",children:(0,s.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center gap-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[s.jsx(ev,{className:"h-6 w-6 text-primary"}),s.jsx("span",{className:"font-semibold",children:"QueryCraft Studio"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-6 text-sm text-muted-foreground",children:[s.jsx(n(),{href:"/privacy",className:"hover:text-foreground transition-colors",children:"Privacy Policy"}),s.jsx(n(),{href:"/terms",className:"hover:text-foreground transition-colors",children:"Terms of Service"}),s.jsx(n(),{href:"/contact",className:"hover:text-foreground transition-colors",children:"Contact"})]})]})})})]})}}};var r=require("../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[638,599,476,15],()=>t(49994));module.exports=s})();