(()=>{var e={};e.id=271,e.ids=[271],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},58629:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>x,tree:()=>o});var s=a(50482),r=a(69108),i=a(62563),n=a.n(i),l=a(68300),d={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);a.d(t,d);let o=["",{children:["auth",{children:["signup",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,73035)),"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/app/auth/signup/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,98890)),"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,69361,23)),"next/dist/client/components/not-found-error"]}],c=["/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/app/auth/signup/page.tsx"],u="/auth/signup/page",m={require:a,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/auth/signup/page",pathname:"/auth/signup",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},32313:(e,t,a)=>{Promise.resolve().then(a.bind(a,3372))},97909:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(69224).Z)("BrainCircuit",[["path",{d:"M12 4.5a2.5 2.5 0 0 0-4.96-.46 2.5 2.5 0 0 0-1.98 3 2.5 2.5 0 0 0-1.32 4.24 3 3 0 0 0 .34 5.58 2.5 2.5 0 0 0 2.96 3.08 2.5 2.5 0 0 0 4.91.05L12 20V4.5Z",key:"ixwj2a"}],["path",{d:"M16 8V5c0-1.1.9-2 2-2",key:"13dx7u"}],["path",{d:"M12 13h4",key:"1ku699"}],["path",{d:"M12 18h6a2 2 0 0 1 2 2v1",key:"105ag5"}],["path",{d:"M12 8h8",key:"1lhi5i"}],["path",{d:"M20.5 8a.5.5 0 1 1-1 0 .5.5 0 0 1 1 0Z",key:"1s25gz"}],["path",{d:"M16.5 13a.5.5 0 1 1-1 0 .5.5 0 0 1 1 0Z",key:"127460"}],["path",{d:"M20.5 21a.5.5 0 1 1-1 0 .5.5 0 0 1 1 0Z",key:"fys062"}],["path",{d:"M18.5 3a.5.5 0 1 1-1 0 .5.5 0 0 1 1 0Z",key:"1vib61"}]])},62312:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(69224).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},1222:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(69224).Z)("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]])},53148:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(69224).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},82947:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(69224).Z)("Github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]])},94530:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(69224).Z)("Loader",[["line",{x1:"12",x2:"12",y1:"2",y2:"6",key:"gza1u7"}],["line",{x1:"12",x2:"12",y1:"18",y2:"22",key:"1qhbu9"}],["line",{x1:"4.93",x2:"7.76",y1:"4.93",y2:"7.76",key:"xae44r"}],["line",{x1:"16.24",x2:"19.07",y1:"16.24",y2:"19.07",key:"bxnmvf"}],["line",{x1:"2",x2:"6",y1:"12",y2:"12",key:"89khin"}],["line",{x1:"18",x2:"22",y1:"12",y2:"12",key:"pb8tfm"}],["line",{x1:"4.93",x2:"7.76",y1:"19.07",y2:"16.24",key:"1uxjnu"}],["line",{x1:"16.24",x2:"19.07",y1:"7.76",y2:"4.93",key:"6duxfx"}]])},71206:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(69224).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},20783:(e,t,a)=>{e.exports=a(61476)},22254:(e,t,a)=>{e.exports=a(14767)},3372:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>N});var s=a(95344),r=a(3729),i=a(47674),n=a(22254),l=a(20783),d=a.n(l),o=a(5094),c=a(46540),u=a(23673),m=a(33668),x=a(50909),p=a(40874),f=a(62312),h=a(94530),y=a(97909),g=a(71206),v=a(82947),b=a(1222),j=a(53148);function N(){let[e,t]=(0,r.useState)(""),[a,l]=(0,r.useState)(""),[N,w]=(0,r.useState)(""),[k,Z]=(0,r.useState)(""),[C,P]=(0,r.useState)(!1),[M,_]=(0,r.useState)(!1),[q,R]=(0,r.useState)(""),[z,A]=(0,r.useState)(!1),S=(0,n.useRouter)(),D=async t=>{if(t.preventDefault(),_(!0),R(""),N!==k){R("Passwords do not match"),_(!1);return}try{let t=await fetch("/api/auth/register",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:e,email:a,password:N})}),s=await t.json();if(!t.ok){R(s.error||"An error occurred");return}A(!0),setTimeout(async()=>{let e=await (0,i.signIn)("credentials",{email:a,password:N,redirect:!1});e?.ok&&S.push("/dashboard")},1500)}catch(e){R("An error occurred. Please try again.")}finally{_(!1)}},E=async e=>{_(!0);try{await (0,i.signIn)(e,{callbackUrl:"/dashboard"})}catch(e){R("An error occurred. Please try again."),_(!1)}};return z?s.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 p-4",children:s.jsx(u.Zb,{className:"w-full max-w-md",children:(0,s.jsxs)(u.aY,{className:"text-center py-8",children:[s.jsx("div",{className:"flex justify-center mb-4",children:s.jsx("div",{className:"h-12 w-12 bg-green-100 rounded-full flex items-center justify-center",children:s.jsx(f.Z,{className:"h-6 w-6 text-green-600"})})}),s.jsx("h2",{className:"text-xl font-semibold mb-2",children:"Account created successfully!"}),s.jsx("p",{className:"text-muted-foreground mb-4",children:"You're being signed in automatically..."}),s.jsx("div",{className:"flex justify-center",children:s.jsx(h.Z,{className:"h-5 w-5 animate-spin"})})]})})}):s.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 p-4",children:(0,s.jsxs)(u.Zb,{className:"w-full max-w-md",children:[(0,s.jsxs)(u.Ol,{className:"text-center",children:[s.jsx("div",{className:"flex justify-center mb-4",children:s.jsx("div",{className:"h-12 w-12 bg-primary rounded-lg flex items-center justify-center",children:s.jsx(y.Z,{className:"h-6 w-6 text-primary-foreground"})})}),s.jsx(u.ll,{className:"text-2xl font-bold",children:"Create your account"}),s.jsx("p",{className:"text-muted-foreground",children:"Get started with QueryCraft Studio"})]}),(0,s.jsxs)(u.aY,{className:"space-y-4",children:[q&&s.jsx(p.bZ,{variant:"destructive",children:s.jsx(p.X,{children:q})}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)(o.z,{variant:"outline",className:"w-full",onClick:()=>E("google"),disabled:M,children:[s.jsx(g.Z,{className:"h-4 w-4 mr-2"}),"Continue with Google"]}),(0,s.jsxs)(o.z,{variant:"outline",className:"w-full",onClick:()=>E("github"),disabled:M,children:[s.jsx(v.Z,{className:"h-4 w-4 mr-2"}),"Continue with GitHub"]})]}),(0,s.jsxs)("div",{className:"relative",children:[s.jsx("div",{className:"absolute inset-0 flex items-center",children:s.jsx(x.Z,{className:"w-full"})}),s.jsx("div",{className:"relative flex justify-center text-xs uppercase",children:s.jsx("span",{className:"bg-background px-2 text-muted-foreground",children:"Or continue with email"})})]}),(0,s.jsxs)("form",{onSubmit:D,className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx(m._,{htmlFor:"name",children:"Full Name"}),s.jsx(c.I,{id:"name",type:"text",placeholder:"Enter your full name",value:e,onChange:e=>t(e.target.value),required:!0,disabled:M})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx(m._,{htmlFor:"email",children:"Email"}),s.jsx(c.I,{id:"email",type:"email",placeholder:"Enter your email",value:a,onChange:e=>l(e.target.value),required:!0,disabled:M})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx(m._,{htmlFor:"password",children:"Password"}),(0,s.jsxs)("div",{className:"relative",children:[s.jsx(c.I,{id:"password",type:C?"text":"password",placeholder:"Create a password",value:N,onChange:e=>w(e.target.value),required:!0,disabled:M,minLength:6}),s.jsx(o.z,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>P(!C),disabled:M,children:C?s.jsx(b.Z,{className:"h-4 w-4"}):s.jsx(j.Z,{className:"h-4 w-4"})})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx(m._,{htmlFor:"confirmPassword",children:"Confirm Password"}),s.jsx(c.I,{id:"confirmPassword",type:"password",placeholder:"Confirm your password",value:k,onChange:e=>Z(e.target.value),required:!0,disabled:M})]}),s.jsx(o.z,{type:"submit",className:"w-full",disabled:M,children:M?(0,s.jsxs)(s.Fragment,{children:[s.jsx(h.Z,{className:"h-4 w-4 mr-2 animate-spin"}),"Creating account..."]}):"Create account"})]}),(0,s.jsxs)("div",{className:"text-center text-sm",children:[s.jsx("span",{className:"text-muted-foreground",children:"Already have an account? "}),s.jsx(d(),{href:"/auth/signin",className:"text-primary hover:underline",children:"Sign in"})]}),s.jsx("div",{className:"text-center text-sm",children:s.jsx(d(),{href:"/",className:"text-muted-foreground hover:underline",children:"← Back to home"})})]})]})})}},40874:(e,t,a)=>{"use strict";a.d(t,{X:()=>o,bZ:()=>d});var s=a(95344),r=a(3729),i=a(49247),n=a(11453);let l=(0,i.j)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),d=r.forwardRef(({className:e,variant:t,...a},r)=>s.jsx("div",{ref:r,role:"alert",className:(0,n.cn)(l({variant:t}),e),...a}));d.displayName="Alert",r.forwardRef(({className:e,...t},a)=>s.jsx("h5",{ref:a,className:(0,n.cn)("mb-1 font-medium leading-none tracking-tight",e),...t})).displayName="AlertTitle";let o=r.forwardRef(({className:e,...t},a)=>s.jsx("div",{ref:a,className:(0,n.cn)("text-sm [&_p]:leading-relaxed",e),...t}));o.displayName="AlertDescription"},5094:(e,t,a)=>{"use strict";a.d(t,{z:()=>o});var s=a(95344),r=a(3729),i=a(32751),n=a(49247),l=a(11453);let d=(0,n.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),o=r.forwardRef(({className:e,variant:t,size:a,asChild:r=!1,...n},o)=>{let c=r?i.g7:"button";return s.jsx(c,{className:(0,l.cn)(d({variant:t,size:a,className:e})),ref:o,...n})});o.displayName="Button"},23673:(e,t,a)=>{"use strict";a.d(t,{Ol:()=>l,Zb:()=>n,aY:()=>o,ll:()=>d});var s=a(95344),r=a(3729),i=a(11453);let n=r.forwardRef(({className:e,...t},a)=>s.jsx("div",{ref:a,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));n.displayName="Card";let l=r.forwardRef(({className:e,...t},a)=>s.jsx("div",{ref:a,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...t}));l.displayName="CardHeader";let d=r.forwardRef(({className:e,...t},a)=>s.jsx("h3",{ref:a,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));d.displayName="CardTitle",r.forwardRef(({className:e,...t},a)=>s.jsx("p",{ref:a,className:(0,i.cn)("text-sm text-muted-foreground",e),...t})).displayName="CardDescription";let o=r.forwardRef(({className:e,...t},a)=>s.jsx("div",{ref:a,className:(0,i.cn)("p-6 pt-0",e),...t}));o.displayName="CardContent",r.forwardRef(({className:e,...t},a)=>s.jsx("div",{ref:a,className:(0,i.cn)("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter"},46540:(e,t,a)=>{"use strict";a.d(t,{I:()=>n});var s=a(95344),r=a(3729),i=a(11453);let n=r.forwardRef(({className:e,type:t,...a},r)=>s.jsx("input",{type:t,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:r,...a}));n.displayName="Input"},33668:(e,t,a)=>{"use strict";a.d(t,{_:()=>c});var s=a(95344),r=a(3729),i=a(62409),n=r.forwardRef((e,t)=>(0,s.jsx)(i.WV.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));n.displayName="Label";var l=a(49247),d=a(11453);let o=(0,l.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=r.forwardRef(({className:e,...t},a)=>s.jsx(n,{ref:a,className:(0,d.cn)(o(),e),...t}));c.displayName=n.displayName},50909:(e,t,a)=>{"use strict";a.d(t,{Z:()=>c});var s=a(95344),r=a(3729),i=a(62409),n="horizontal",l=["horizontal","vertical"],d=r.forwardRef((e,t)=>{let{decorative:a,orientation:r=n,...d}=e,o=l.includes(r)?r:n;return(0,s.jsx)(i.WV.div,{"data-orientation":o,...a?{role:"none"}:{"aria-orientation":"vertical"===o?o:void 0,role:"separator"},...d,ref:t})});d.displayName="Separator";var o=a(11453);let c=r.forwardRef(({className:e,orientation:t="horizontal",decorative:a=!0,...r},i)=>s.jsx(d,{ref:i,decorative:a,orientation:t,className:(0,o.cn)("shrink-0 bg-border","horizontal"===t?"h-[1px] w-full":"h-full w-[1px]",e),...r}));c.displayName=d.displayName},73035:(e,t,a)=>{"use strict";a.r(t),a.d(t,{$$typeof:()=>i,__esModule:()=>r,default:()=>n});let s=(0,a(86843).createProxy)(String.raw`/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/app/auth/signup/page.tsx`),{__esModule:r,$$typeof:i}=s,n=s.default}};var t=require("../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[638,599,476,15],()=>a(58629));module.exports=s})();