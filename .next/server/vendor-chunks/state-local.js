"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/state-local";
exports.ids = ["vendor-chunks/state-local"];
exports.modules = {

/***/ "(ssr)/./node_modules/state-local/lib/es/state-local.js":
/*!********************************************************!*\
  !*** ./node_modules/state-local/lib/es/state-local.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nfunction _defineProperty(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\nfunction ownKeys(object, enumerableOnly) {\n    var keys = Object.keys(object);\n    if (Object.getOwnPropertySymbols) {\n        var symbols = Object.getOwnPropertySymbols(object);\n        if (enumerableOnly) symbols = symbols.filter(function(sym) {\n            return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n        });\n        keys.push.apply(keys, symbols);\n    }\n    return keys;\n}\nfunction _objectSpread2(target) {\n    for(var i = 1; i < arguments.length; i++){\n        var source = arguments[i] != null ? arguments[i] : {};\n        if (i % 2) {\n            ownKeys(Object(source), true).forEach(function(key) {\n                _defineProperty(target, key, source[key]);\n            });\n        } else if (Object.getOwnPropertyDescriptors) {\n            Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n        } else {\n            ownKeys(Object(source)).forEach(function(key) {\n                Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n            });\n        }\n    }\n    return target;\n}\nfunction compose() {\n    for(var _len = arguments.length, fns = new Array(_len), _key = 0; _key < _len; _key++){\n        fns[_key] = arguments[_key];\n    }\n    return function(x) {\n        return fns.reduceRight(function(y, f) {\n            return f(y);\n        }, x);\n    };\n}\nfunction curry(fn) {\n    return function curried() {\n        var _this = this;\n        for(var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++){\n            args[_key2] = arguments[_key2];\n        }\n        return args.length >= fn.length ? fn.apply(this, args) : function() {\n            for(var _len3 = arguments.length, nextArgs = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++){\n                nextArgs[_key3] = arguments[_key3];\n            }\n            return curried.apply(_this, [].concat(args, nextArgs));\n        };\n    };\n}\nfunction isObject(value) {\n    return ({}).toString.call(value).includes(\"Object\");\n}\nfunction isEmpty(obj) {\n    return !Object.keys(obj).length;\n}\nfunction isFunction(value) {\n    return typeof value === \"function\";\n}\nfunction hasOwnProperty(object, property) {\n    return Object.prototype.hasOwnProperty.call(object, property);\n}\nfunction validateChanges(initial, changes) {\n    if (!isObject(changes)) errorHandler(\"changeType\");\n    if (Object.keys(changes).some(function(field) {\n        return !hasOwnProperty(initial, field);\n    })) errorHandler(\"changeField\");\n    return changes;\n}\nfunction validateSelector(selector) {\n    if (!isFunction(selector)) errorHandler(\"selectorType\");\n}\nfunction validateHandler(handler) {\n    if (!(isFunction(handler) || isObject(handler))) errorHandler(\"handlerType\");\n    if (isObject(handler) && Object.values(handler).some(function(_handler) {\n        return !isFunction(_handler);\n    })) errorHandler(\"handlersType\");\n}\nfunction validateInitial(initial) {\n    if (!initial) errorHandler(\"initialIsRequired\");\n    if (!isObject(initial)) errorHandler(\"initialType\");\n    if (isEmpty(initial)) errorHandler(\"initialContent\");\n}\nfunction throwError(errorMessages, type) {\n    throw new Error(errorMessages[type] || errorMessages[\"default\"]);\n}\nvar errorMessages = {\n    initialIsRequired: \"initial state is required\",\n    initialType: \"initial state should be an object\",\n    initialContent: \"initial state shouldn't be an empty object\",\n    handlerType: \"handler should be an object or a function\",\n    handlersType: \"all handlers should be a functions\",\n    selectorType: \"selector should be a function\",\n    changeType: \"provided value of changes should be an object\",\n    changeField: 'it seams you want to change a field in the state which is not specified in the \"initial\" state',\n    \"default\": \"an unknown error accured in `state-local` package\"\n};\nvar errorHandler = curry(throwError)(errorMessages);\nvar validators = {\n    changes: validateChanges,\n    selector: validateSelector,\n    handler: validateHandler,\n    initial: validateInitial\n};\nfunction create(initial) {\n    var handler = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    validators.initial(initial);\n    validators.handler(handler);\n    var state = {\n        current: initial\n    };\n    var didUpdate = curry(didStateUpdate)(state, handler);\n    var update = curry(updateState)(state);\n    var validate = curry(validators.changes)(initial);\n    var getChanges = curry(extractChanges)(state);\n    function getState() {\n        var selector = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : function(state) {\n            return state;\n        };\n        validators.selector(selector);\n        return selector(state.current);\n    }\n    function setState(causedChanges) {\n        compose(didUpdate, update, validate, getChanges)(causedChanges);\n    }\n    return [\n        getState,\n        setState\n    ];\n}\nfunction extractChanges(state, causedChanges) {\n    return isFunction(causedChanges) ? causedChanges(state.current) : causedChanges;\n}\nfunction updateState(state, changes) {\n    state.current = _objectSpread2(_objectSpread2({}, state.current), changes);\n    return changes;\n}\nfunction didStateUpdate(state, handler, changes) {\n    isFunction(handler) ? handler(state.current) : Object.keys(changes).forEach(function(field) {\n        var _handler$field;\n        return (_handler$field = handler[field]) === null || _handler$field === void 0 ? void 0 : _handler$field.call(handler, state.current[field]);\n    });\n    return changes;\n}\nvar index = {\n    create: create\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (index);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/state-local/lib/es/state-local.js\n");

/***/ })

};
;