/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/named-placeholders";
exports.ids = ["vendor-chunks/named-placeholders"];
exports.modules = {

/***/ "(rsc)/./node_modules/named-placeholders/index.js":
/*!**************************************************!*\
  !*** ./node_modules/named-placeholders/index.js ***!
  \**************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n// based on code from Brian White @mscdex mariasql library - https://github.com/mscdex/node-mariasql/blob/master/lib/Client.js#L272-L332\n// License: https://github.com/mscdex/node-mariasql/blob/master/LICENSE\nconst RE_PARAM = /(?:\\?)|(?::(\\d+|(?:[a-zA-Z][a-zA-Z0-9_]*)))/g, DQUOTE = 34, SQUOTE = 39, BSLASH = 92;\nfunction parse(query) {\n    let ppos = RE_PARAM.exec(query);\n    let curpos = 0;\n    let start = 0;\n    let end;\n    const parts = [];\n    let inQuote = false;\n    let escape = false;\n    let qchr;\n    const tokens = [];\n    let qcnt = 0;\n    let lastTokenEndPos = 0;\n    let i;\n    if (ppos) {\n        do {\n            for(i = curpos, end = ppos.index; i < end; ++i){\n                let chr = query.charCodeAt(i);\n                if (chr === BSLASH) escape = !escape;\n                else {\n                    if (escape) {\n                        escape = false;\n                        continue;\n                    }\n                    if (inQuote && chr === qchr) {\n                        if (query.charCodeAt(i + 1) === qchr) {\n                            // quote escaped via \"\" or ''\n                            ++i;\n                            continue;\n                        }\n                        inQuote = false;\n                    } else if (chr === DQUOTE || chr === SQUOTE) {\n                        inQuote = true;\n                        qchr = chr;\n                    }\n                }\n            }\n            if (!inQuote) {\n                parts.push(query.substring(start, end));\n                tokens.push(ppos[0].length === 1 ? qcnt++ : ppos[1]);\n                start = end + ppos[0].length;\n                lastTokenEndPos = start;\n            }\n            curpos = end + ppos[0].length;\n        }while (ppos = RE_PARAM.exec(query));\n        if (tokens.length) {\n            if (curpos < query.length) {\n                parts.push(query.substring(lastTokenEndPos));\n            }\n            return [\n                parts,\n                tokens\n            ];\n        }\n    }\n    return [\n        query\n    ];\n}\n;\nfunction createCompiler(config) {\n    if (!config) config = {};\n    if (!config.placeholder) {\n        config.placeholder = \"?\";\n    }\n    let ncache = 100;\n    let cache;\n    if (typeof config.cache === \"number\") {\n        ncache = config.cache;\n    }\n    if (typeof config.cache === \"object\") {\n        cache = config.cache;\n    }\n    if (config.cache !== false && !cache) {\n        cache = new (__webpack_require__(/*! lru-cache */ \"(rsc)/./node_modules/named-placeholders/node_modules/lru-cache/index.js\"))({\n            max: ncache\n        });\n    }\n    function toArrayParams(tree, params) {\n        const arr = [];\n        if (tree.length == 1) {\n            return [\n                tree[0],\n                []\n            ];\n        }\n        if (typeof params == \"undefined\") throw new Error(\"Named query contains placeholders, but parameters object is undefined\");\n        const tokens = tree[1];\n        for(let i = 0; i < tokens.length; ++i){\n            arr.push(params[tokens[i]]);\n        }\n        return [\n            tree[0],\n            arr\n        ];\n    }\n    function noTailingSemicolon(s) {\n        if (s.slice(-1) == \":\") {\n            return s.slice(0, -1);\n        }\n        return s;\n    }\n    function join(tree) {\n        if (tree.length == 1) {\n            return tree;\n        }\n        let unnamed = noTailingSemicolon(tree[0][0]);\n        for(let i = 1; i < tree[0].length; ++i){\n            if (tree[0][i - 1].slice(-1) == \":\") {\n                unnamed += config.placeholder;\n            }\n            unnamed += config.placeholder;\n            unnamed += noTailingSemicolon(tree[0][i]);\n        }\n        const last = tree[0][tree[0].length - 1];\n        if (tree[0].length == tree[1].length) {\n            if (last.slice(-1) == \":\") {\n                unnamed += config.placeholder;\n            }\n            unnamed += config.placeholder;\n        }\n        return [\n            unnamed,\n            tree[1]\n        ];\n    }\n    function compile(query, paramsObj) {\n        let tree;\n        if (cache && (tree = cache.get(query))) {\n            return toArrayParams(tree, paramsObj);\n        }\n        tree = join(parse(query));\n        if (cache) {\n            cache.set(query, tree);\n        }\n        return toArrayParams(tree, paramsObj);\n    }\n    compile.parse = parse;\n    return compile;\n}\n// named :one :two to postgres-style numbered $1 $2 $3\nfunction toNumbered(q, params) {\n    const tree = parse(q);\n    const paramsArr = [];\n    if (tree.length == 1) {\n        return [\n            tree[0],\n            paramsArr\n        ];\n    }\n    const pIndexes = {};\n    let pLastIndex = 0;\n    let qs = \"\";\n    let varIndex;\n    const varNames = [];\n    for(let i = 0; i < tree[0].length; ++i){\n        varIndex = pIndexes[tree[1][i]];\n        if (!varIndex) {\n            varIndex = ++pLastIndex;\n            pIndexes[tree[1][i]] = varIndex;\n        }\n        if (tree[1][i]) {\n            varNames[varIndex - 1] = tree[1][i];\n            qs += tree[0][i] + \"$\" + varIndex;\n        } else {\n            qs += tree[0][i];\n        }\n    }\n    return [\n        qs,\n        varNames.map((n)=>params[n])\n    ];\n}\nmodule.exports = createCompiler;\nmodule.exports.toNumbered = toNumbered;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/named-placeholders/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/named-placeholders/node_modules/lru-cache/index.js":
/*!*************************************************************************!*\
  !*** ./node_modules/named-placeholders/node_modules/lru-cache/index.js ***!
  \*************************************************************************/
/***/ ((module) => {

eval("const perf = typeof performance === \"object\" && performance && typeof performance.now === \"function\" ? performance : Date;\nconst hasAbortController = typeof AbortController === \"function\";\n// minimal backwards-compatibility polyfill\n// this doesn't have nearly all the checks and whatnot that\n// actual AbortController/Signal has, but it's enough for\n// our purposes, and if used properly, behaves the same.\nconst AC = hasAbortController ? AbortController : class AbortController1 {\n    constructor(){\n        this.signal = new AS();\n    }\n    abort(reason = new Error(\"This operation was aborted\")) {\n        this.signal.reason = this.signal.reason || reason;\n        this.signal.aborted = true;\n        this.signal.dispatchEvent({\n            type: \"abort\",\n            target: this.signal\n        });\n    }\n};\nconst hasAbortSignal = typeof AbortSignal === \"function\";\n// Some polyfills put this on the AC class, not global\nconst hasACAbortSignal = typeof AC.AbortSignal === \"function\";\nconst AS = hasAbortSignal ? AbortSignal : hasACAbortSignal ? AC.AbortController : class AbortSignal1 {\n    constructor(){\n        this.reason = undefined;\n        this.aborted = false;\n        this._listeners = [];\n    }\n    dispatchEvent(e) {\n        if (e.type === \"abort\") {\n            this.aborted = true;\n            this.onabort(e);\n            this._listeners.forEach((f)=>f(e), this);\n        }\n    }\n    onabort() {}\n    addEventListener(ev, fn) {\n        if (ev === \"abort\") {\n            this._listeners.push(fn);\n        }\n    }\n    removeEventListener(ev, fn) {\n        if (ev === \"abort\") {\n            this._listeners = this._listeners.filter((f)=>f !== fn);\n        }\n    }\n};\nconst warned = new Set();\nconst deprecatedOption = (opt, instead)=>{\n    const code = `LRU_CACHE_OPTION_${opt}`;\n    if (shouldWarn(code)) {\n        warn(code, `${opt} option`, `options.${instead}`, LRUCache);\n    }\n};\nconst deprecatedMethod = (method, instead)=>{\n    const code = `LRU_CACHE_METHOD_${method}`;\n    if (shouldWarn(code)) {\n        const { prototype } = LRUCache;\n        const { get } = Object.getOwnPropertyDescriptor(prototype, method);\n        warn(code, `${method} method`, `cache.${instead}()`, get);\n    }\n};\nconst deprecatedProperty = (field, instead)=>{\n    const code = `LRU_CACHE_PROPERTY_${field}`;\n    if (shouldWarn(code)) {\n        const { prototype } = LRUCache;\n        const { get } = Object.getOwnPropertyDescriptor(prototype, field);\n        warn(code, `${field} property`, `cache.${instead}`, get);\n    }\n};\nconst emitWarning = (...a)=>{\n    typeof process === \"object\" && process && typeof process.emitWarning === \"function\" ? process.emitWarning(...a) : console.error(...a);\n};\nconst shouldWarn = (code)=>!warned.has(code);\nconst warn = (code, what, instead, fn)=>{\n    warned.add(code);\n    const msg = `The ${what} is deprecated. Please use ${instead} instead.`;\n    emitWarning(msg, \"DeprecationWarning\", code, fn);\n};\nconst isPosInt = (n)=>n && n === Math.floor(n) && n > 0 && isFinite(n);\n/* istanbul ignore next - This is a little bit ridiculous, tbh.\n * The maximum array length is 2^32-1 or thereabouts on most JS impls.\n * And well before that point, you're caching the entire world, I mean,\n * that's ~32GB of just integers for the next/prev links, plus whatever\n * else to hold that many keys and values.  Just filling the memory with\n * zeroes at init time is brutal when you get that big.\n * But why not be complete?\n * Maybe in the future, these limits will have expanded. */ const getUintArray = (max)=>!isPosInt(max) ? null : max <= Math.pow(2, 8) ? Uint8Array : max <= Math.pow(2, 16) ? Uint16Array : max <= Math.pow(2, 32) ? Uint32Array : max <= Number.MAX_SAFE_INTEGER ? ZeroArray : null;\nclass ZeroArray extends Array {\n    constructor(size){\n        super(size);\n        this.fill(0);\n    }\n}\nclass Stack {\n    constructor(max){\n        if (max === 0) {\n            return [];\n        }\n        const UintArray = getUintArray(max);\n        this.heap = new UintArray(max);\n        this.length = 0;\n    }\n    push(n) {\n        this.heap[this.length++] = n;\n    }\n    pop() {\n        return this.heap[--this.length];\n    }\n}\nclass LRUCache {\n    constructor(options = {}){\n        const { max = 0, ttl, ttlResolution = 1, ttlAutopurge, updateAgeOnGet, updateAgeOnHas, allowStale, dispose, disposeAfter, noDisposeOnSet, noUpdateTTL, maxSize = 0, maxEntrySize = 0, sizeCalculation, fetchMethod, fetchContext, noDeleteOnFetchRejection, noDeleteOnStaleGet, allowStaleOnFetchRejection, allowStaleOnFetchAbort, ignoreFetchAbort } = options;\n        // deprecated options, don't trigger a warning for getting them if\n        // the thing being passed in is another LRUCache we're copying.\n        const { length, maxAge, stale } = options instanceof LRUCache ? {} : options;\n        if (max !== 0 && !isPosInt(max)) {\n            throw new TypeError(\"max option must be a nonnegative integer\");\n        }\n        const UintArray = max ? getUintArray(max) : Array;\n        if (!UintArray) {\n            throw new Error(\"invalid max value: \" + max);\n        }\n        this.max = max;\n        this.maxSize = maxSize;\n        this.maxEntrySize = maxEntrySize || this.maxSize;\n        this.sizeCalculation = sizeCalculation || length;\n        if (this.sizeCalculation) {\n            if (!this.maxSize && !this.maxEntrySize) {\n                throw new TypeError(\"cannot set sizeCalculation without setting maxSize or maxEntrySize\");\n            }\n            if (typeof this.sizeCalculation !== \"function\") {\n                throw new TypeError(\"sizeCalculation set to non-function\");\n            }\n        }\n        this.fetchMethod = fetchMethod || null;\n        if (this.fetchMethod && typeof this.fetchMethod !== \"function\") {\n            throw new TypeError(\"fetchMethod must be a function if specified\");\n        }\n        this.fetchContext = fetchContext;\n        if (!this.fetchMethod && fetchContext !== undefined) {\n            throw new TypeError(\"cannot set fetchContext without fetchMethod\");\n        }\n        this.keyMap = new Map();\n        this.keyList = new Array(max).fill(null);\n        this.valList = new Array(max).fill(null);\n        this.next = new UintArray(max);\n        this.prev = new UintArray(max);\n        this.head = 0;\n        this.tail = 0;\n        this.free = new Stack(max);\n        this.initialFill = 1;\n        this.size = 0;\n        if (typeof dispose === \"function\") {\n            this.dispose = dispose;\n        }\n        if (typeof disposeAfter === \"function\") {\n            this.disposeAfter = disposeAfter;\n            this.disposed = [];\n        } else {\n            this.disposeAfter = null;\n            this.disposed = null;\n        }\n        this.noDisposeOnSet = !!noDisposeOnSet;\n        this.noUpdateTTL = !!noUpdateTTL;\n        this.noDeleteOnFetchRejection = !!noDeleteOnFetchRejection;\n        this.allowStaleOnFetchRejection = !!allowStaleOnFetchRejection;\n        this.allowStaleOnFetchAbort = !!allowStaleOnFetchAbort;\n        this.ignoreFetchAbort = !!ignoreFetchAbort;\n        // NB: maxEntrySize is set to maxSize if it's set\n        if (this.maxEntrySize !== 0) {\n            if (this.maxSize !== 0) {\n                if (!isPosInt(this.maxSize)) {\n                    throw new TypeError(\"maxSize must be a positive integer if specified\");\n                }\n            }\n            if (!isPosInt(this.maxEntrySize)) {\n                throw new TypeError(\"maxEntrySize must be a positive integer if specified\");\n            }\n            this.initializeSizeTracking();\n        }\n        this.allowStale = !!allowStale || !!stale;\n        this.noDeleteOnStaleGet = !!noDeleteOnStaleGet;\n        this.updateAgeOnGet = !!updateAgeOnGet;\n        this.updateAgeOnHas = !!updateAgeOnHas;\n        this.ttlResolution = isPosInt(ttlResolution) || ttlResolution === 0 ? ttlResolution : 1;\n        this.ttlAutopurge = !!ttlAutopurge;\n        this.ttl = ttl || maxAge || 0;\n        if (this.ttl) {\n            if (!isPosInt(this.ttl)) {\n                throw new TypeError(\"ttl must be a positive integer if specified\");\n            }\n            this.initializeTTLTracking();\n        }\n        // do not allow completely unbounded caches\n        if (this.max === 0 && this.ttl === 0 && this.maxSize === 0) {\n            throw new TypeError(\"At least one of max, maxSize, or ttl is required\");\n        }\n        if (!this.ttlAutopurge && !this.max && !this.maxSize) {\n            const code = \"LRU_CACHE_UNBOUNDED\";\n            if (shouldWarn(code)) {\n                warned.add(code);\n                const msg = \"TTL caching without ttlAutopurge, max, or maxSize can \" + \"result in unbounded memory consumption.\";\n                emitWarning(msg, \"UnboundedCacheWarning\", code, LRUCache);\n            }\n        }\n        if (stale) {\n            deprecatedOption(\"stale\", \"allowStale\");\n        }\n        if (maxAge) {\n            deprecatedOption(\"maxAge\", \"ttl\");\n        }\n        if (length) {\n            deprecatedOption(\"length\", \"sizeCalculation\");\n        }\n    }\n    getRemainingTTL(key) {\n        return this.has(key, {\n            updateAgeOnHas: false\n        }) ? Infinity : 0;\n    }\n    initializeTTLTracking() {\n        this.ttls = new ZeroArray(this.max);\n        this.starts = new ZeroArray(this.max);\n        this.setItemTTL = (index, ttl, start = perf.now())=>{\n            this.starts[index] = ttl !== 0 ? start : 0;\n            this.ttls[index] = ttl;\n            if (ttl !== 0 && this.ttlAutopurge) {\n                const t = setTimeout(()=>{\n                    if (this.isStale(index)) {\n                        this.delete(this.keyList[index]);\n                    }\n                }, ttl + 1);\n                /* istanbul ignore else - unref() not supported on all platforms */ if (t.unref) {\n                    t.unref();\n                }\n            }\n        };\n        this.updateItemAge = (index)=>{\n            this.starts[index] = this.ttls[index] !== 0 ? perf.now() : 0;\n        };\n        this.statusTTL = (status, index)=>{\n            if (status) {\n                status.ttl = this.ttls[index];\n                status.start = this.starts[index];\n                status.now = cachedNow || getNow();\n                status.remainingTTL = status.now + status.ttl - status.start;\n            }\n        };\n        // debounce calls to perf.now() to 1s so we're not hitting\n        // that costly call repeatedly.\n        let cachedNow = 0;\n        const getNow = ()=>{\n            const n = perf.now();\n            if (this.ttlResolution > 0) {\n                cachedNow = n;\n                const t = setTimeout(()=>cachedNow = 0, this.ttlResolution);\n                /* istanbul ignore else - not available on all platforms */ if (t.unref) {\n                    t.unref();\n                }\n            }\n            return n;\n        };\n        this.getRemainingTTL = (key)=>{\n            const index = this.keyMap.get(key);\n            if (index === undefined) {\n                return 0;\n            }\n            return this.ttls[index] === 0 || this.starts[index] === 0 ? Infinity : this.starts[index] + this.ttls[index] - (cachedNow || getNow());\n        };\n        this.isStale = (index)=>{\n            return this.ttls[index] !== 0 && this.starts[index] !== 0 && (cachedNow || getNow()) - this.starts[index] > this.ttls[index];\n        };\n    }\n    updateItemAge(_index) {}\n    statusTTL(_status, _index) {}\n    setItemTTL(_index, _ttl, _start) {}\n    isStale(_index) {\n        return false;\n    }\n    initializeSizeTracking() {\n        this.calculatedSize = 0;\n        this.sizes = new ZeroArray(this.max);\n        this.removeItemSize = (index)=>{\n            this.calculatedSize -= this.sizes[index];\n            this.sizes[index] = 0;\n        };\n        this.requireSize = (k, v, size, sizeCalculation)=>{\n            // provisionally accept background fetches.\n            // actual value size will be checked when they return.\n            if (this.isBackgroundFetch(v)) {\n                return 0;\n            }\n            if (!isPosInt(size)) {\n                if (sizeCalculation) {\n                    if (typeof sizeCalculation !== \"function\") {\n                        throw new TypeError(\"sizeCalculation must be a function\");\n                    }\n                    size = sizeCalculation(v, k);\n                    if (!isPosInt(size)) {\n                        throw new TypeError(\"sizeCalculation return invalid (expect positive integer)\");\n                    }\n                } else {\n                    throw new TypeError(\"invalid size value (must be positive integer). \" + \"When maxSize or maxEntrySize is used, sizeCalculation or size \" + \"must be set.\");\n                }\n            }\n            return size;\n        };\n        this.addItemSize = (index, size, status)=>{\n            this.sizes[index] = size;\n            if (this.maxSize) {\n                const maxSize = this.maxSize - this.sizes[index];\n                while(this.calculatedSize > maxSize){\n                    this.evict(true);\n                }\n            }\n            this.calculatedSize += this.sizes[index];\n            if (status) {\n                status.entrySize = size;\n                status.totalCalculatedSize = this.calculatedSize;\n            }\n        };\n    }\n    removeItemSize(_index) {}\n    addItemSize(_index, _size) {}\n    requireSize(_k, _v, size, sizeCalculation) {\n        if (size || sizeCalculation) {\n            throw new TypeError(\"cannot set size without setting maxSize or maxEntrySize on cache\");\n        }\n    }\n    *indexes({ allowStale = this.allowStale } = {}) {\n        if (this.size) {\n            for(let i = this.tail; true;){\n                if (!this.isValidIndex(i)) {\n                    break;\n                }\n                if (allowStale || !this.isStale(i)) {\n                    yield i;\n                }\n                if (i === this.head) {\n                    break;\n                } else {\n                    i = this.prev[i];\n                }\n            }\n        }\n    }\n    *rindexes({ allowStale = this.allowStale } = {}) {\n        if (this.size) {\n            for(let i = this.head; true;){\n                if (!this.isValidIndex(i)) {\n                    break;\n                }\n                if (allowStale || !this.isStale(i)) {\n                    yield i;\n                }\n                if (i === this.tail) {\n                    break;\n                } else {\n                    i = this.next[i];\n                }\n            }\n        }\n    }\n    isValidIndex(index) {\n        return index !== undefined && this.keyMap.get(this.keyList[index]) === index;\n    }\n    *entries() {\n        for (const i of this.indexes()){\n            if (this.valList[i] !== undefined && this.keyList[i] !== undefined && !this.isBackgroundFetch(this.valList[i])) {\n                yield [\n                    this.keyList[i],\n                    this.valList[i]\n                ];\n            }\n        }\n    }\n    *rentries() {\n        for (const i of this.rindexes()){\n            if (this.valList[i] !== undefined && this.keyList[i] !== undefined && !this.isBackgroundFetch(this.valList[i])) {\n                yield [\n                    this.keyList[i],\n                    this.valList[i]\n                ];\n            }\n        }\n    }\n    *keys() {\n        for (const i of this.indexes()){\n            if (this.keyList[i] !== undefined && !this.isBackgroundFetch(this.valList[i])) {\n                yield this.keyList[i];\n            }\n        }\n    }\n    *rkeys() {\n        for (const i of this.rindexes()){\n            if (this.keyList[i] !== undefined && !this.isBackgroundFetch(this.valList[i])) {\n                yield this.keyList[i];\n            }\n        }\n    }\n    *values() {\n        for (const i of this.indexes()){\n            if (this.valList[i] !== undefined && !this.isBackgroundFetch(this.valList[i])) {\n                yield this.valList[i];\n            }\n        }\n    }\n    *rvalues() {\n        for (const i of this.rindexes()){\n            if (this.valList[i] !== undefined && !this.isBackgroundFetch(this.valList[i])) {\n                yield this.valList[i];\n            }\n        }\n    }\n    [Symbol.iterator]() {\n        return this.entries();\n    }\n    find(fn, getOptions) {\n        for (const i of this.indexes()){\n            const v = this.valList[i];\n            const value = this.isBackgroundFetch(v) ? v.__staleWhileFetching : v;\n            if (value === undefined) continue;\n            if (fn(value, this.keyList[i], this)) {\n                return this.get(this.keyList[i], getOptions);\n            }\n        }\n    }\n    forEach(fn, thisp = this) {\n        for (const i of this.indexes()){\n            const v = this.valList[i];\n            const value = this.isBackgroundFetch(v) ? v.__staleWhileFetching : v;\n            if (value === undefined) continue;\n            fn.call(thisp, value, this.keyList[i], this);\n        }\n    }\n    rforEach(fn, thisp = this) {\n        for (const i of this.rindexes()){\n            const v = this.valList[i];\n            const value = this.isBackgroundFetch(v) ? v.__staleWhileFetching : v;\n            if (value === undefined) continue;\n            fn.call(thisp, value, this.keyList[i], this);\n        }\n    }\n    get prune() {\n        deprecatedMethod(\"prune\", \"purgeStale\");\n        return this.purgeStale;\n    }\n    purgeStale() {\n        let deleted = false;\n        for (const i of this.rindexes({\n            allowStale: true\n        })){\n            if (this.isStale(i)) {\n                this.delete(this.keyList[i]);\n                deleted = true;\n            }\n        }\n        return deleted;\n    }\n    dump() {\n        const arr = [];\n        for (const i of this.indexes({\n            allowStale: true\n        })){\n            const key = this.keyList[i];\n            const v = this.valList[i];\n            const value = this.isBackgroundFetch(v) ? v.__staleWhileFetching : v;\n            if (value === undefined) continue;\n            const entry = {\n                value\n            };\n            if (this.ttls) {\n                entry.ttl = this.ttls[i];\n                // always dump the start relative to a portable timestamp\n                // it's ok for this to be a bit slow, it's a rare operation.\n                const age = perf.now() - this.starts[i];\n                entry.start = Math.floor(Date.now() - age);\n            }\n            if (this.sizes) {\n                entry.size = this.sizes[i];\n            }\n            arr.unshift([\n                key,\n                entry\n            ]);\n        }\n        return arr;\n    }\n    load(arr) {\n        this.clear();\n        for (const [key, entry] of arr){\n            if (entry.start) {\n                // entry.start is a portable timestamp, but we may be using\n                // node's performance.now(), so calculate the offset.\n                // it's ok for this to be a bit slow, it's a rare operation.\n                const age = Date.now() - entry.start;\n                entry.start = perf.now() - age;\n            }\n            this.set(key, entry.value, entry);\n        }\n    }\n    dispose(_v, _k, _reason) {}\n    set(k, v, { ttl = this.ttl, start, noDisposeOnSet = this.noDisposeOnSet, size = 0, sizeCalculation = this.sizeCalculation, noUpdateTTL = this.noUpdateTTL, status } = {}) {\n        size = this.requireSize(k, v, size, sizeCalculation);\n        // if the item doesn't fit, don't do anything\n        // NB: maxEntrySize set to maxSize by default\n        if (this.maxEntrySize && size > this.maxEntrySize) {\n            if (status) {\n                status.set = \"miss\";\n                status.maxEntrySizeExceeded = true;\n            }\n            // have to delete, in case a background fetch is there already.\n            // in non-async cases, this is a no-op\n            this.delete(k);\n            return this;\n        }\n        let index = this.size === 0 ? undefined : this.keyMap.get(k);\n        if (index === undefined) {\n            // addition\n            index = this.newIndex();\n            this.keyList[index] = k;\n            this.valList[index] = v;\n            this.keyMap.set(k, index);\n            this.next[this.tail] = index;\n            this.prev[index] = this.tail;\n            this.tail = index;\n            this.size++;\n            this.addItemSize(index, size, status);\n            if (status) {\n                status.set = \"add\";\n            }\n            noUpdateTTL = false;\n        } else {\n            // update\n            this.moveToTail(index);\n            const oldVal = this.valList[index];\n            if (v !== oldVal) {\n                if (this.isBackgroundFetch(oldVal)) {\n                    oldVal.__abortController.abort(new Error(\"replaced\"));\n                } else {\n                    if (!noDisposeOnSet) {\n                        this.dispose(oldVal, k, \"set\");\n                        if (this.disposeAfter) {\n                            this.disposed.push([\n                                oldVal,\n                                k,\n                                \"set\"\n                            ]);\n                        }\n                    }\n                }\n                this.removeItemSize(index);\n                this.valList[index] = v;\n                this.addItemSize(index, size, status);\n                if (status) {\n                    status.set = \"replace\";\n                    const oldValue = oldVal && this.isBackgroundFetch(oldVal) ? oldVal.__staleWhileFetching : oldVal;\n                    if (oldValue !== undefined) status.oldValue = oldValue;\n                }\n            } else if (status) {\n                status.set = \"update\";\n            }\n        }\n        if (ttl !== 0 && this.ttl === 0 && !this.ttls) {\n            this.initializeTTLTracking();\n        }\n        if (!noUpdateTTL) {\n            this.setItemTTL(index, ttl, start);\n        }\n        this.statusTTL(status, index);\n        if (this.disposeAfter) {\n            while(this.disposed.length){\n                this.disposeAfter(...this.disposed.shift());\n            }\n        }\n        return this;\n    }\n    newIndex() {\n        if (this.size === 0) {\n            return this.tail;\n        }\n        if (this.size === this.max && this.max !== 0) {\n            return this.evict(false);\n        }\n        if (this.free.length !== 0) {\n            return this.free.pop();\n        }\n        // initial fill, just keep writing down the list\n        return this.initialFill++;\n    }\n    pop() {\n        if (this.size) {\n            const val = this.valList[this.head];\n            this.evict(true);\n            return val;\n        }\n    }\n    evict(free) {\n        const head = this.head;\n        const k = this.keyList[head];\n        const v = this.valList[head];\n        if (this.isBackgroundFetch(v)) {\n            v.__abortController.abort(new Error(\"evicted\"));\n        } else {\n            this.dispose(v, k, \"evict\");\n            if (this.disposeAfter) {\n                this.disposed.push([\n                    v,\n                    k,\n                    \"evict\"\n                ]);\n            }\n        }\n        this.removeItemSize(head);\n        // if we aren't about to use the index, then null these out\n        if (free) {\n            this.keyList[head] = null;\n            this.valList[head] = null;\n            this.free.push(head);\n        }\n        this.head = this.next[head];\n        this.keyMap.delete(k);\n        this.size--;\n        return head;\n    }\n    has(k, { updateAgeOnHas = this.updateAgeOnHas, status } = {}) {\n        const index = this.keyMap.get(k);\n        if (index !== undefined) {\n            if (!this.isStale(index)) {\n                if (updateAgeOnHas) {\n                    this.updateItemAge(index);\n                }\n                if (status) status.has = \"hit\";\n                this.statusTTL(status, index);\n                return true;\n            } else if (status) {\n                status.has = \"stale\";\n                this.statusTTL(status, index);\n            }\n        } else if (status) {\n            status.has = \"miss\";\n        }\n        return false;\n    }\n    // like get(), but without any LRU updating or TTL expiration\n    peek(k, { allowStale = this.allowStale } = {}) {\n        const index = this.keyMap.get(k);\n        if (index !== undefined && (allowStale || !this.isStale(index))) {\n            const v = this.valList[index];\n            // either stale and allowed, or forcing a refresh of non-stale value\n            return this.isBackgroundFetch(v) ? v.__staleWhileFetching : v;\n        }\n    }\n    backgroundFetch(k, index, options, context) {\n        const v = index === undefined ? undefined : this.valList[index];\n        if (this.isBackgroundFetch(v)) {\n            return v;\n        }\n        const ac = new AC();\n        if (options.signal) {\n            options.signal.addEventListener(\"abort\", ()=>ac.abort(options.signal.reason));\n        }\n        const fetchOpts = {\n            signal: ac.signal,\n            options,\n            context\n        };\n        const cb = (v, updateCache = false)=>{\n            const { aborted } = ac.signal;\n            const ignoreAbort = options.ignoreFetchAbort && v !== undefined;\n            if (options.status) {\n                if (aborted && !updateCache) {\n                    options.status.fetchAborted = true;\n                    options.status.fetchError = ac.signal.reason;\n                    if (ignoreAbort) options.status.fetchAbortIgnored = true;\n                } else {\n                    options.status.fetchResolved = true;\n                }\n            }\n            if (aborted && !ignoreAbort && !updateCache) {\n                return fetchFail(ac.signal.reason);\n            }\n            // either we didn't abort, and are still here, or we did, and ignored\n            if (this.valList[index] === p) {\n                if (v === undefined) {\n                    if (p.__staleWhileFetching) {\n                        this.valList[index] = p.__staleWhileFetching;\n                    } else {\n                        this.delete(k);\n                    }\n                } else {\n                    if (options.status) options.status.fetchUpdated = true;\n                    this.set(k, v, fetchOpts.options);\n                }\n            }\n            return v;\n        };\n        const eb = (er)=>{\n            if (options.status) {\n                options.status.fetchRejected = true;\n                options.status.fetchError = er;\n            }\n            return fetchFail(er);\n        };\n        const fetchFail = (er)=>{\n            const { aborted } = ac.signal;\n            const allowStaleAborted = aborted && options.allowStaleOnFetchAbort;\n            const allowStale = allowStaleAborted || options.allowStaleOnFetchRejection;\n            const noDelete = allowStale || options.noDeleteOnFetchRejection;\n            if (this.valList[index] === p) {\n                // if we allow stale on fetch rejections, then we need to ensure that\n                // the stale value is not removed from the cache when the fetch fails.\n                const del = !noDelete || p.__staleWhileFetching === undefined;\n                if (del) {\n                    this.delete(k);\n                } else if (!allowStaleAborted) {\n                    // still replace the *promise* with the stale value,\n                    // since we are done with the promise at this point.\n                    // leave it untouched if we're still waiting for an\n                    // aborted background fetch that hasn't yet returned.\n                    this.valList[index] = p.__staleWhileFetching;\n                }\n            }\n            if (allowStale) {\n                if (options.status && p.__staleWhileFetching !== undefined) {\n                    options.status.returnedStale = true;\n                }\n                return p.__staleWhileFetching;\n            } else if (p.__returned === p) {\n                throw er;\n            }\n        };\n        const pcall = (res, rej)=>{\n            this.fetchMethod(k, v, fetchOpts).then((v)=>res(v), rej);\n            // ignored, we go until we finish, regardless.\n            // defer check until we are actually aborting,\n            // so fetchMethod can override.\n            ac.signal.addEventListener(\"abort\", ()=>{\n                if (!options.ignoreFetchAbort || options.allowStaleOnFetchAbort) {\n                    res();\n                    // when it eventually resolves, update the cache.\n                    if (options.allowStaleOnFetchAbort) {\n                        res = (v)=>cb(v, true);\n                    }\n                }\n            });\n        };\n        if (options.status) options.status.fetchDispatched = true;\n        const p = new Promise(pcall).then(cb, eb);\n        p.__abortController = ac;\n        p.__staleWhileFetching = v;\n        p.__returned = null;\n        if (index === undefined) {\n            // internal, don't expose status.\n            this.set(k, p, {\n                ...fetchOpts.options,\n                status: undefined\n            });\n            index = this.keyMap.get(k);\n        } else {\n            this.valList[index] = p;\n        }\n        return p;\n    }\n    isBackgroundFetch(p) {\n        return p && typeof p === \"object\" && typeof p.then === \"function\" && Object.prototype.hasOwnProperty.call(p, \"__staleWhileFetching\") && Object.prototype.hasOwnProperty.call(p, \"__returned\") && (p.__returned === p || p.__returned === null);\n    }\n    // this takes the union of get() and set() opts, because it does both\n    async fetch(k, { // get options\n    allowStale = this.allowStale, updateAgeOnGet = this.updateAgeOnGet, noDeleteOnStaleGet = this.noDeleteOnStaleGet, // set options\n    ttl = this.ttl, noDisposeOnSet = this.noDisposeOnSet, size = 0, sizeCalculation = this.sizeCalculation, noUpdateTTL = this.noUpdateTTL, // fetch exclusive options\n    noDeleteOnFetchRejection = this.noDeleteOnFetchRejection, allowStaleOnFetchRejection = this.allowStaleOnFetchRejection, ignoreFetchAbort = this.ignoreFetchAbort, allowStaleOnFetchAbort = this.allowStaleOnFetchAbort, fetchContext = this.fetchContext, forceRefresh = false, status, signal } = {}) {\n        if (!this.fetchMethod) {\n            if (status) status.fetch = \"get\";\n            return this.get(k, {\n                allowStale,\n                updateAgeOnGet,\n                noDeleteOnStaleGet,\n                status\n            });\n        }\n        const options = {\n            allowStale,\n            updateAgeOnGet,\n            noDeleteOnStaleGet,\n            ttl,\n            noDisposeOnSet,\n            size,\n            sizeCalculation,\n            noUpdateTTL,\n            noDeleteOnFetchRejection,\n            allowStaleOnFetchRejection,\n            allowStaleOnFetchAbort,\n            ignoreFetchAbort,\n            status,\n            signal\n        };\n        let index = this.keyMap.get(k);\n        if (index === undefined) {\n            if (status) status.fetch = \"miss\";\n            const p = this.backgroundFetch(k, index, options, fetchContext);\n            return p.__returned = p;\n        } else {\n            // in cache, maybe already fetching\n            const v = this.valList[index];\n            if (this.isBackgroundFetch(v)) {\n                const stale = allowStale && v.__staleWhileFetching !== undefined;\n                if (status) {\n                    status.fetch = \"inflight\";\n                    if (stale) status.returnedStale = true;\n                }\n                return stale ? v.__staleWhileFetching : v.__returned = v;\n            }\n            // if we force a refresh, that means do NOT serve the cached value,\n            // unless we are already in the process of refreshing the cache.\n            const isStale = this.isStale(index);\n            if (!forceRefresh && !isStale) {\n                if (status) status.fetch = \"hit\";\n                this.moveToTail(index);\n                if (updateAgeOnGet) {\n                    this.updateItemAge(index);\n                }\n                this.statusTTL(status, index);\n                return v;\n            }\n            // ok, it is stale or a forced refresh, and not already fetching.\n            // refresh the cache.\n            const p = this.backgroundFetch(k, index, options, fetchContext);\n            const hasStale = p.__staleWhileFetching !== undefined;\n            const staleVal = hasStale && allowStale;\n            if (status) {\n                status.fetch = hasStale && isStale ? \"stale\" : \"refresh\";\n                if (staleVal && isStale) status.returnedStale = true;\n            }\n            return staleVal ? p.__staleWhileFetching : p.__returned = p;\n        }\n    }\n    get(k, { allowStale = this.allowStale, updateAgeOnGet = this.updateAgeOnGet, noDeleteOnStaleGet = this.noDeleteOnStaleGet, status } = {}) {\n        const index = this.keyMap.get(k);\n        if (index !== undefined) {\n            const value = this.valList[index];\n            const fetching = this.isBackgroundFetch(value);\n            this.statusTTL(status, index);\n            if (this.isStale(index)) {\n                if (status) status.get = \"stale\";\n                // delete only if not an in-flight background fetch\n                if (!fetching) {\n                    if (!noDeleteOnStaleGet) {\n                        this.delete(k);\n                    }\n                    if (status) status.returnedStale = allowStale;\n                    return allowStale ? value : undefined;\n                } else {\n                    if (status) {\n                        status.returnedStale = allowStale && value.__staleWhileFetching !== undefined;\n                    }\n                    return allowStale ? value.__staleWhileFetching : undefined;\n                }\n            } else {\n                if (status) status.get = \"hit\";\n                // if we're currently fetching it, we don't actually have it yet\n                // it's not stale, which means this isn't a staleWhileRefetching.\n                // If it's not stale, and fetching, AND has a __staleWhileFetching\n                // value, then that means the user fetched with {forceRefresh:true},\n                // so it's safe to return that value.\n                if (fetching) {\n                    return value.__staleWhileFetching;\n                }\n                this.moveToTail(index);\n                if (updateAgeOnGet) {\n                    this.updateItemAge(index);\n                }\n                return value;\n            }\n        } else if (status) {\n            status.get = \"miss\";\n        }\n    }\n    connect(p, n) {\n        this.prev[n] = p;\n        this.next[p] = n;\n    }\n    moveToTail(index) {\n        // if tail already, nothing to do\n        // if head, move head to next[index]\n        // else\n        //   move next[prev[index]] to next[index] (head has no prev)\n        //   move prev[next[index]] to prev[index]\n        // prev[index] = tail\n        // next[tail] = index\n        // tail = index\n        if (index !== this.tail) {\n            if (index === this.head) {\n                this.head = this.next[index];\n            } else {\n                this.connect(this.prev[index], this.next[index]);\n            }\n            this.connect(this.tail, index);\n            this.tail = index;\n        }\n    }\n    get del() {\n        deprecatedMethod(\"del\", \"delete\");\n        return this.delete;\n    }\n    delete(k) {\n        let deleted = false;\n        if (this.size !== 0) {\n            const index = this.keyMap.get(k);\n            if (index !== undefined) {\n                deleted = true;\n                if (this.size === 1) {\n                    this.clear();\n                } else {\n                    this.removeItemSize(index);\n                    const v = this.valList[index];\n                    if (this.isBackgroundFetch(v)) {\n                        v.__abortController.abort(new Error(\"deleted\"));\n                    } else {\n                        this.dispose(v, k, \"delete\");\n                        if (this.disposeAfter) {\n                            this.disposed.push([\n                                v,\n                                k,\n                                \"delete\"\n                            ]);\n                        }\n                    }\n                    this.keyMap.delete(k);\n                    this.keyList[index] = null;\n                    this.valList[index] = null;\n                    if (index === this.tail) {\n                        this.tail = this.prev[index];\n                    } else if (index === this.head) {\n                        this.head = this.next[index];\n                    } else {\n                        this.next[this.prev[index]] = this.next[index];\n                        this.prev[this.next[index]] = this.prev[index];\n                    }\n                    this.size--;\n                    this.free.push(index);\n                }\n            }\n        }\n        if (this.disposed) {\n            while(this.disposed.length){\n                this.disposeAfter(...this.disposed.shift());\n            }\n        }\n        return deleted;\n    }\n    clear() {\n        for (const index of this.rindexes({\n            allowStale: true\n        })){\n            const v = this.valList[index];\n            if (this.isBackgroundFetch(v)) {\n                v.__abortController.abort(new Error(\"deleted\"));\n            } else {\n                const k = this.keyList[index];\n                this.dispose(v, k, \"delete\");\n                if (this.disposeAfter) {\n                    this.disposed.push([\n                        v,\n                        k,\n                        \"delete\"\n                    ]);\n                }\n            }\n        }\n        this.keyMap.clear();\n        this.valList.fill(null);\n        this.keyList.fill(null);\n        if (this.ttls) {\n            this.ttls.fill(0);\n            this.starts.fill(0);\n        }\n        if (this.sizes) {\n            this.sizes.fill(0);\n        }\n        this.head = 0;\n        this.tail = 0;\n        this.initialFill = 1;\n        this.free.length = 0;\n        this.calculatedSize = 0;\n        this.size = 0;\n        if (this.disposed) {\n            while(this.disposed.length){\n                this.disposeAfter(...this.disposed.shift());\n            }\n        }\n    }\n    get reset() {\n        deprecatedMethod(\"reset\", \"clear\");\n        return this.clear;\n    }\n    get length() {\n        deprecatedProperty(\"length\", \"size\");\n        return this.size;\n    }\n    static get AbortController() {\n        return AC;\n    }\n    static get AbortSignal() {\n        return AS;\n    }\n}\nmodule.exports = LRUCache;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/named-placeholders/node_modules/lru-cache/index.js\n");

/***/ })

};
;