"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@monaco-editor";
exports.ids = ["vendor-chunks/@monaco-editor"];
exports.modules = {

/***/ "(ssr)/./node_modules/@monaco-editor/loader/lib/es/_virtual/_rollupPluginBabelHelpers.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/@monaco-editor/loader/lib/es/_virtual/_rollupPluginBabelHelpers.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   arrayLikeToArray: () => (/* binding */ _arrayLikeToArray),\n/* harmony export */   arrayWithHoles: () => (/* binding */ _arrayWithHoles),\n/* harmony export */   defineProperty: () => (/* binding */ _defineProperty),\n/* harmony export */   iterableToArrayLimit: () => (/* binding */ _iterableToArrayLimit),\n/* harmony export */   nonIterableRest: () => (/* binding */ _nonIterableRest),\n/* harmony export */   objectSpread2: () => (/* binding */ _objectSpread2),\n/* harmony export */   objectWithoutProperties: () => (/* binding */ _objectWithoutProperties),\n/* harmony export */   objectWithoutPropertiesLoose: () => (/* binding */ _objectWithoutPropertiesLoose),\n/* harmony export */   slicedToArray: () => (/* binding */ _slicedToArray),\n/* harmony export */   unsupportedIterableToArray: () => (/* binding */ _unsupportedIterableToArray)\n/* harmony export */ });\nfunction _defineProperty(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\nfunction ownKeys(object, enumerableOnly) {\n    var keys = Object.keys(object);\n    if (Object.getOwnPropertySymbols) {\n        var symbols = Object.getOwnPropertySymbols(object);\n        if (enumerableOnly) symbols = symbols.filter(function(sym) {\n            return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n        });\n        keys.push.apply(keys, symbols);\n    }\n    return keys;\n}\nfunction _objectSpread2(target) {\n    for(var i = 1; i < arguments.length; i++){\n        var source = arguments[i] != null ? arguments[i] : {};\n        if (i % 2) {\n            ownKeys(Object(source), true).forEach(function(key) {\n                _defineProperty(target, key, source[key]);\n            });\n        } else if (Object.getOwnPropertyDescriptors) {\n            Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n        } else {\n            ownKeys(Object(source)).forEach(function(key) {\n                Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n            });\n        }\n    }\n    return target;\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n    if (source == null) return {};\n    var target = {};\n    var sourceKeys = Object.keys(source);\n    var key, i;\n    for(i = 0; i < sourceKeys.length; i++){\n        key = sourceKeys[i];\n        if (excluded.indexOf(key) >= 0) continue;\n        target[key] = source[key];\n    }\n    return target;\n}\nfunction _objectWithoutProperties(source, excluded) {\n    if (source == null) return {};\n    var target = _objectWithoutPropertiesLoose(source, excluded);\n    var key, i;\n    if (Object.getOwnPropertySymbols) {\n        var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n        for(i = 0; i < sourceSymbolKeys.length; i++){\n            key = sourceSymbolKeys[i];\n            if (excluded.indexOf(key) >= 0) continue;\n            if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n            target[key] = source[key];\n        }\n    }\n    return target;\n}\nfunction _slicedToArray(arr, i) {\n    return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\nfunction _arrayWithHoles(arr) {\n    if (Array.isArray(arr)) return arr;\n}\nfunction _iterableToArrayLimit(arr, i) {\n    if (typeof Symbol === \"undefined\" || !(Symbol.iterator in Object(arr))) return;\n    var _arr = [];\n    var _n = true;\n    var _d = false;\n    var _e = undefined;\n    try {\n        for(var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true){\n            _arr.push(_s.value);\n            if (i && _arr.length === i) break;\n        }\n    } catch (err) {\n        _d = true;\n        _e = err;\n    } finally{\n        try {\n            if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n        } finally{\n            if (_d) throw _e;\n        }\n    }\n    return _arr;\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n    if (!o) return;\n    if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n    var n = Object.prototype.toString.call(o).slice(8, -1);\n    if (n === \"Object\" && o.constructor) n = o.constructor.name;\n    if (n === \"Map\" || n === \"Set\") return Array.from(o);\n    if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n    if (len == null || len > arr.length) len = arr.length;\n    for(var i = 0, arr2 = new Array(len); i < len; i++)arr2[i] = arr[i];\n    return arr2;\n}\nfunction _nonIterableRest() {\n    throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@monaco-editor/loader/lib/es/_virtual/_rollupPluginBabelHelpers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@monaco-editor/loader/lib/es/config/index.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@monaco-editor/loader/lib/es/config/index.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nvar config = {\n    paths: {\n        vs: \"https://cdn.jsdelivr.net/npm/monaco-editor@0.52.2/min/vs\"\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (config);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1vbmFjby1lZGl0b3IvbG9hZGVyL2xpYi9lcy9jb25maWcvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLElBQUlBLFNBQVM7SUFDWEMsT0FBTztRQUNMQyxJQUFJO0lBQ047QUFDRjtBQUVBLGlFQUFlRixNQUFNQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcXVlcnljcmFmdC1zdHVkaW8vLi9ub2RlX21vZHVsZXMvQG1vbmFjby1lZGl0b3IvbG9hZGVyL2xpYi9lcy9jb25maWcvaW5kZXguanM/MzdmZCJdLCJzb3VyY2VzQ29udGVudCI6WyJ2YXIgY29uZmlnID0ge1xuICBwYXRoczoge1xuICAgIHZzOiAnaHR0cHM6Ly9jZG4uanNkZWxpdnIubmV0L25wbS9tb25hY28tZWRpdG9yQDAuNTIuMi9taW4vdnMnXG4gIH1cbn07XG5cbmV4cG9ydCBkZWZhdWx0IGNvbmZpZztcbiJdLCJuYW1lcyI6WyJjb25maWciLCJwYXRocyIsInZzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@monaco-editor/loader/lib/es/config/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@monaco-editor/loader/lib/es/index.js":
/*!************************************************************!*\
  !*** ./node_modules/@monaco-editor/loader/lib/es/index.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* reexport safe */ _loader_index_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _loader_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./loader/index.js */ \"(ssr)/./node_modules/@monaco-editor/loader/lib/es/loader/index.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1vbmFjby1lZGl0b3IvbG9hZGVyL2xpYi9lcy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUF1QztBQUNLIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcXVlcnljcmFmdC1zdHVkaW8vLi9ub2RlX21vZHVsZXMvQG1vbmFjby1lZGl0b3IvbG9hZGVyL2xpYi9lcy9pbmRleC5qcz9mZmI2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBsb2FkZXIgZnJvbSAnLi9sb2FkZXIvaW5kZXguanMnO1xuZXhwb3J0IHsgZGVmYXVsdCB9IGZyb20gJy4vbG9hZGVyL2luZGV4LmpzJztcbiJdLCJuYW1lcyI6WyJsb2FkZXIiLCJkZWZhdWx0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@monaco-editor/loader/lib/es/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@monaco-editor/loader/lib/es/loader/index.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@monaco-editor/loader/lib/es/loader/index.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../_virtual/_rollupPluginBabelHelpers.js */ \"(ssr)/./node_modules/@monaco-editor/loader/lib/es/_virtual/_rollupPluginBabelHelpers.js\");\n/* harmony import */ var state_local__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! state-local */ \"(ssr)/./node_modules/state-local/lib/es/state-local.js\");\n/* harmony import */ var _config_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../config/index.js */ \"(ssr)/./node_modules/@monaco-editor/loader/lib/es/config/index.js\");\n/* harmony import */ var _validators_index_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../validators/index.js */ \"(ssr)/./node_modules/@monaco-editor/loader/lib/es/validators/index.js\");\n/* harmony import */ var _utils_compose_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/compose.js */ \"(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/compose.js\");\n/* harmony import */ var _utils_deepMerge_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../utils/deepMerge.js */ \"(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/deepMerge.js\");\n/* harmony import */ var _utils_makeCancelable_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../utils/makeCancelable.js */ \"(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/makeCancelable.js\");\n\n\n\n\n\n\n\n/** the local state of the module */ var _state$create = state_local__WEBPACK_IMPORTED_MODULE_1__[\"default\"].create({\n    config: _config_index_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n    isInitialized: false,\n    resolve: null,\n    reject: null,\n    monaco: null\n}), _state$create2 = (0,_virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_0__.slicedToArray)(_state$create, 2), getState = _state$create2[0], setState = _state$create2[1];\n/**\n * set the loader configuration\n * @param {Object} config - the configuration object\n */ function config(globalConfig) {\n    var _validators$config = _validators_index_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"].config(globalConfig), monaco = _validators$config.monaco, config = (0,_virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_0__.objectWithoutProperties)(_validators$config, [\n        \"monaco\"\n    ]);\n    setState(function(state) {\n        return {\n            config: (0,_utils_deepMerge_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(state.config, config),\n            monaco: monaco\n        };\n    });\n}\n/**\n * handles the initialization of the monaco-editor\n * @return {Promise} - returns an instance of monaco (with a cancelable promise)\n */ function init() {\n    var state = getState(function(_ref) {\n        var monaco = _ref.monaco, isInitialized = _ref.isInitialized, resolve = _ref.resolve;\n        return {\n            monaco: monaco,\n            isInitialized: isInitialized,\n            resolve: resolve\n        };\n    });\n    if (!state.isInitialized) {\n        setState({\n            isInitialized: true\n        });\n        if (state.monaco) {\n            state.resolve(state.monaco);\n            return (0,_utils_makeCancelable_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(wrapperPromise);\n        }\n        if (window.monaco && window.monaco.editor) {\n            storeMonacoInstance(window.monaco);\n            state.resolve(window.monaco);\n            return (0,_utils_makeCancelable_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(wrapperPromise);\n        }\n        (0,_utils_compose_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(injectScripts, getMonacoLoaderScript)(configureLoader);\n    }\n    return (0,_utils_makeCancelable_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(wrapperPromise);\n}\n/**\n * injects provided scripts into the document.body\n * @param {Object} script - an HTML script element\n * @return {Object} - the injected HTML script element\n */ function injectScripts(script) {\n    return document.body.appendChild(script);\n}\n/**\n * creates an HTML script element with/without provided src\n * @param {string} [src] - the source path of the script\n * @return {Object} - the created HTML script element\n */ function createScript(src) {\n    var script = document.createElement(\"script\");\n    return src && (script.src = src), script;\n}\n/**\n * creates an HTML script element with the monaco loader src\n * @return {Object} - the created HTML script element\n */ function getMonacoLoaderScript(configureLoader) {\n    var state = getState(function(_ref2) {\n        var config = _ref2.config, reject = _ref2.reject;\n        return {\n            config: config,\n            reject: reject\n        };\n    });\n    var loaderScript = createScript(\"\".concat(state.config.paths.vs, \"/loader.js\"));\n    loaderScript.onload = function() {\n        return configureLoader();\n    };\n    loaderScript.onerror = state.reject;\n    return loaderScript;\n}\n/**\n * configures the monaco loader\n */ function configureLoader() {\n    var state = getState(function(_ref3) {\n        var config = _ref3.config, resolve = _ref3.resolve, reject = _ref3.reject;\n        return {\n            config: config,\n            resolve: resolve,\n            reject: reject\n        };\n    });\n    var require = window.require;\n    require.config(state.config);\n    require([\n        \"vs/editor/editor.main\"\n    ], function(monaco) {\n        storeMonacoInstance(monaco);\n        state.resolve(monaco);\n    }, function(error) {\n        state.reject(error);\n    });\n}\n/**\n * store monaco instance in local state\n */ function storeMonacoInstance(monaco) {\n    if (!getState().monaco) {\n        setState({\n            monaco: monaco\n        });\n    }\n}\n/**\n * internal helper function\n * extracts stored monaco instance\n * @return {Object|null} - the monaco instance\n */ function __getMonacoInstance() {\n    return getState(function(_ref4) {\n        var monaco = _ref4.monaco;\n        return monaco;\n    });\n}\nvar wrapperPromise = new Promise(function(resolve, reject) {\n    return setState({\n        resolve: resolve,\n        reject: reject\n    });\n});\nvar loader = {\n    config: config,\n    init: init,\n    __getMonacoInstance: __getMonacoInstance\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (loader);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@monaco-editor/loader/lib/es/loader/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/compose.js":
/*!********************************************************************!*\
  !*** ./node_modules/@monaco-editor/loader/lib/es/utils/compose.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nvar compose = function compose() {\n    for(var _len = arguments.length, fns = new Array(_len), _key = 0; _key < _len; _key++){\n        fns[_key] = arguments[_key];\n    }\n    return function(x) {\n        return fns.reduceRight(function(y, f) {\n            return f(y);\n        }, x);\n    };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (compose);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1vbmFjby1lZGl0b3IvbG9hZGVyL2xpYi9lcy91dGlscy9jb21wb3NlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxJQUFJQSxVQUFVLFNBQVNBO0lBQ3JCLElBQUssSUFBSUMsT0FBT0MsVUFBVUMsTUFBTSxFQUFFQyxNQUFNLElBQUlDLE1BQU1KLE9BQU9LLE9BQU8sR0FBR0EsT0FBT0wsTUFBTUssT0FBUTtRQUN0RkYsR0FBRyxDQUFDRSxLQUFLLEdBQUdKLFNBQVMsQ0FBQ0ksS0FBSztJQUM3QjtJQUVBLE9BQU8sU0FBVUMsQ0FBQztRQUNoQixPQUFPSCxJQUFJSSxXQUFXLENBQUMsU0FBVUMsQ0FBQyxFQUFFQyxDQUFDO1lBQ25DLE9BQU9BLEVBQUVEO1FBQ1gsR0FBR0Y7SUFDTDtBQUNGO0FBRUEsaUVBQWVQLE9BQU9BLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9xdWVyeWNyYWZ0LXN0dWRpby8uL25vZGVfbW9kdWxlcy9AbW9uYWNvLWVkaXRvci9sb2FkZXIvbGliL2VzL3V0aWxzL2NvbXBvc2UuanM/YTYxYyJdLCJzb3VyY2VzQ29udGVudCI6WyJ2YXIgY29tcG9zZSA9IGZ1bmN0aW9uIGNvbXBvc2UoKSB7XG4gIGZvciAodmFyIF9sZW4gPSBhcmd1bWVudHMubGVuZ3RoLCBmbnMgPSBuZXcgQXJyYXkoX2xlbiksIF9rZXkgPSAwOyBfa2V5IDwgX2xlbjsgX2tleSsrKSB7XG4gICAgZm5zW19rZXldID0gYXJndW1lbnRzW19rZXldO1xuICB9XG5cbiAgcmV0dXJuIGZ1bmN0aW9uICh4KSB7XG4gICAgcmV0dXJuIGZucy5yZWR1Y2VSaWdodChmdW5jdGlvbiAoeSwgZikge1xuICAgICAgcmV0dXJuIGYoeSk7XG4gICAgfSwgeCk7XG4gIH07XG59O1xuXG5leHBvcnQgZGVmYXVsdCBjb21wb3NlO1xuIl0sIm5hbWVzIjpbImNvbXBvc2UiLCJfbGVuIiwiYXJndW1lbnRzIiwibGVuZ3RoIiwiZm5zIiwiQXJyYXkiLCJfa2V5IiwieCIsInJlZHVjZVJpZ2h0IiwieSIsImYiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/compose.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/curry.js":
/*!******************************************************************!*\
  !*** ./node_modules/@monaco-editor/loader/lib/es/utils/curry.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nfunction curry(fn) {\n    return function curried() {\n        var _this = this;\n        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n            args[_key] = arguments[_key];\n        }\n        return args.length >= fn.length ? fn.apply(this, args) : function() {\n            for(var _len2 = arguments.length, nextArgs = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++){\n                nextArgs[_key2] = arguments[_key2];\n            }\n            return curried.apply(_this, [].concat(args, nextArgs));\n        };\n    };\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (curry);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1vbmFjby1lZGl0b3IvbG9hZGVyL2xpYi9lcy91dGlscy9jdXJyeS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsU0FBU0EsTUFBTUMsRUFBRTtJQUNmLE9BQU8sU0FBU0M7UUFDZCxJQUFJQyxRQUFRLElBQUk7UUFFaEIsSUFBSyxJQUFJQyxPQUFPQyxVQUFVQyxNQUFNLEVBQUVDLE9BQU8sSUFBSUMsTUFBTUosT0FBT0ssT0FBTyxHQUFHQSxPQUFPTCxNQUFNSyxPQUFRO1lBQ3ZGRixJQUFJLENBQUNFLEtBQUssR0FBR0osU0FBUyxDQUFDSSxLQUFLO1FBQzlCO1FBRUEsT0FBT0YsS0FBS0QsTUFBTSxJQUFJTCxHQUFHSyxNQUFNLEdBQUdMLEdBQUdTLEtBQUssQ0FBQyxJQUFJLEVBQUVILFFBQVE7WUFDdkQsSUFBSyxJQUFJSSxRQUFRTixVQUFVQyxNQUFNLEVBQUVNLFdBQVcsSUFBSUosTUFBTUcsUUFBUUUsUUFBUSxHQUFHQSxRQUFRRixPQUFPRSxRQUFTO2dCQUNqR0QsUUFBUSxDQUFDQyxNQUFNLEdBQUdSLFNBQVMsQ0FBQ1EsTUFBTTtZQUNwQztZQUVBLE9BQU9YLFFBQVFRLEtBQUssQ0FBQ1AsT0FBTyxFQUFFLENBQUNXLE1BQU0sQ0FBQ1AsTUFBTUs7UUFDOUM7SUFDRjtBQUNGO0FBRUEsaUVBQWVaLEtBQUtBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9xdWVyeWNyYWZ0LXN0dWRpby8uL25vZGVfbW9kdWxlcy9AbW9uYWNvLWVkaXRvci9sb2FkZXIvbGliL2VzL3V0aWxzL2N1cnJ5LmpzPzEyNWYiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gY3VycnkoZm4pIHtcbiAgcmV0dXJuIGZ1bmN0aW9uIGN1cnJpZWQoKSB7XG4gICAgdmFyIF90aGlzID0gdGhpcztcblxuICAgIGZvciAodmFyIF9sZW4gPSBhcmd1bWVudHMubGVuZ3RoLCBhcmdzID0gbmV3IEFycmF5KF9sZW4pLCBfa2V5ID0gMDsgX2tleSA8IF9sZW47IF9rZXkrKykge1xuICAgICAgYXJnc1tfa2V5XSA9IGFyZ3VtZW50c1tfa2V5XTtcbiAgICB9XG5cbiAgICByZXR1cm4gYXJncy5sZW5ndGggPj0gZm4ubGVuZ3RoID8gZm4uYXBwbHkodGhpcywgYXJncykgOiBmdW5jdGlvbiAoKSB7XG4gICAgICBmb3IgKHZhciBfbGVuMiA9IGFyZ3VtZW50cy5sZW5ndGgsIG5leHRBcmdzID0gbmV3IEFycmF5KF9sZW4yKSwgX2tleTIgPSAwOyBfa2V5MiA8IF9sZW4yOyBfa2V5MisrKSB7XG4gICAgICAgIG5leHRBcmdzW19rZXkyXSA9IGFyZ3VtZW50c1tfa2V5Ml07XG4gICAgICB9XG5cbiAgICAgIHJldHVybiBjdXJyaWVkLmFwcGx5KF90aGlzLCBbXS5jb25jYXQoYXJncywgbmV4dEFyZ3MpKTtcbiAgICB9O1xuICB9O1xufVxuXG5leHBvcnQgZGVmYXVsdCBjdXJyeTtcbiJdLCJuYW1lcyI6WyJjdXJyeSIsImZuIiwiY3VycmllZCIsIl90aGlzIiwiX2xlbiIsImFyZ3VtZW50cyIsImxlbmd0aCIsImFyZ3MiLCJBcnJheSIsIl9rZXkiLCJhcHBseSIsIl9sZW4yIiwibmV4dEFyZ3MiLCJfa2V5MiIsImNvbmNhdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/curry.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/deepMerge.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@monaco-editor/loader/lib/es/utils/deepMerge.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../_virtual/_rollupPluginBabelHelpers.js */ \"(ssr)/./node_modules/@monaco-editor/loader/lib/es/_virtual/_rollupPluginBabelHelpers.js\");\n\nfunction merge(target, source) {\n    Object.keys(source).forEach(function(key) {\n        if (source[key] instanceof Object) {\n            if (target[key]) {\n                Object.assign(source[key], merge(target[key], source[key]));\n            }\n        }\n    });\n    return (0,_virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_0__.objectSpread2)((0,_virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_0__.objectSpread2)({}, target), source);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (merge);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1vbmFjby1lZGl0b3IvbG9hZGVyL2xpYi9lcy91dGlscy9kZWVwTWVyZ2UuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBMkY7QUFFM0YsU0FBU0UsTUFBTUMsTUFBTSxFQUFFQyxNQUFNO0lBQzNCQyxPQUFPQyxJQUFJLENBQUNGLFFBQVFHLE9BQU8sQ0FBQyxTQUFVQyxHQUFHO1FBQ3ZDLElBQUlKLE1BQU0sQ0FBQ0ksSUFBSSxZQUFZSCxRQUFRO1lBQ2pDLElBQUlGLE1BQU0sQ0FBQ0ssSUFBSSxFQUFFO2dCQUNmSCxPQUFPSSxNQUFNLENBQUNMLE1BQU0sQ0FBQ0ksSUFBSSxFQUFFTixNQUFNQyxNQUFNLENBQUNLLElBQUksRUFBRUosTUFBTSxDQUFDSSxJQUFJO1lBQzNEO1FBQ0Y7SUFDRjtJQUNBLE9BQU9QLG1GQUFjQSxDQUFDQSxtRkFBY0EsQ0FBQyxDQUFDLEdBQUdFLFNBQVNDO0FBQ3BEO0FBRUEsaUVBQWVGLEtBQUtBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9xdWVyeWNyYWZ0LXN0dWRpby8uL25vZGVfbW9kdWxlcy9AbW9uYWNvLWVkaXRvci9sb2FkZXIvbGliL2VzL3V0aWxzL2RlZXBNZXJnZS5qcz9iNmM1Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IG9iamVjdFNwcmVhZDIgYXMgX29iamVjdFNwcmVhZDIgfSBmcm9tICcuLi9fdmlydHVhbC9fcm9sbHVwUGx1Z2luQmFiZWxIZWxwZXJzLmpzJztcblxuZnVuY3Rpb24gbWVyZ2UodGFyZ2V0LCBzb3VyY2UpIHtcbiAgT2JqZWN0LmtleXMoc291cmNlKS5mb3JFYWNoKGZ1bmN0aW9uIChrZXkpIHtcbiAgICBpZiAoc291cmNlW2tleV0gaW5zdGFuY2VvZiBPYmplY3QpIHtcbiAgICAgIGlmICh0YXJnZXRba2V5XSkge1xuICAgICAgICBPYmplY3QuYXNzaWduKHNvdXJjZVtrZXldLCBtZXJnZSh0YXJnZXRba2V5XSwgc291cmNlW2tleV0pKTtcbiAgICAgIH1cbiAgICB9XG4gIH0pO1xuICByZXR1cm4gX29iamVjdFNwcmVhZDIoX29iamVjdFNwcmVhZDIoe30sIHRhcmdldCksIHNvdXJjZSk7XG59XG5cbmV4cG9ydCBkZWZhdWx0IG1lcmdlO1xuIl0sIm5hbWVzIjpbIm9iamVjdFNwcmVhZDIiLCJfb2JqZWN0U3ByZWFkMiIsIm1lcmdlIiwidGFyZ2V0Iiwic291cmNlIiwiT2JqZWN0Iiwia2V5cyIsImZvckVhY2giLCJrZXkiLCJhc3NpZ24iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/deepMerge.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/isObject.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@monaco-editor/loader/lib/es/utils/isObject.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nfunction isObject(value) {\n    return ({}).toString.call(value).includes(\"Object\");\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (isObject);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1vbmFjby1lZGl0b3IvbG9hZGVyL2xpYi9lcy91dGlscy9pc09iamVjdC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsU0FBU0EsU0FBU0MsS0FBSztJQUNyQixPQUFPLEVBQUMsR0FBRUMsUUFBUSxDQUFDQyxJQUFJLENBQUNGLE9BQU9HLFFBQVEsQ0FBQztBQUMxQztBQUVBLGlFQUFlSixRQUFRQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcXVlcnljcmFmdC1zdHVkaW8vLi9ub2RlX21vZHVsZXMvQG1vbmFjby1lZGl0b3IvbG9hZGVyL2xpYi9lcy91dGlscy9pc09iamVjdC5qcz80YTZkIl0sInNvdXJjZXNDb250ZW50IjpbImZ1bmN0aW9uIGlzT2JqZWN0KHZhbHVlKSB7XG4gIHJldHVybiB7fS50b1N0cmluZy5jYWxsKHZhbHVlKS5pbmNsdWRlcygnT2JqZWN0Jyk7XG59XG5cbmV4cG9ydCBkZWZhdWx0IGlzT2JqZWN0O1xuIl0sIm5hbWVzIjpbImlzT2JqZWN0IiwidmFsdWUiLCJ0b1N0cmluZyIsImNhbGwiLCJpbmNsdWRlcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/isObject.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/makeCancelable.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@monaco-editor/loader/lib/es/utils/makeCancelable.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CANCELATION_MESSAGE: () => (/* binding */ CANCELATION_MESSAGE),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n// The source (has been changed) is https://github.com/facebook/react/issues/5465#issuecomment-157888325\nvar CANCELATION_MESSAGE = {\n    type: \"cancelation\",\n    msg: \"operation is manually canceled\"\n};\nfunction makeCancelable(promise) {\n    var hasCanceled_ = false;\n    var wrappedPromise = new Promise(function(resolve, reject) {\n        promise.then(function(val) {\n            return hasCanceled_ ? reject(CANCELATION_MESSAGE) : resolve(val);\n        });\n        promise[\"catch\"](reject);\n    });\n    return wrappedPromise.cancel = function() {\n        return hasCanceled_ = true;\n    }, wrappedPromise;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (makeCancelable);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/makeCancelable.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@monaco-editor/loader/lib/es/validators/index.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@monaco-editor/loader/lib/es/validators/index.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   errorHandler: () => (/* binding */ errorHandler),\n/* harmony export */   errorMessages: () => (/* binding */ errorMessages)\n/* harmony export */ });\n/* harmony import */ var _utils_curry_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/curry.js */ \"(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/curry.js\");\n/* harmony import */ var _utils_isObject_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/isObject.js */ \"(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/isObject.js\");\n\n\n/**\n * validates the configuration object and informs about deprecation\n * @param {Object} config - the configuration object \n * @return {Object} config - the validated configuration object\n */ function validateConfig(config) {\n    if (!config) errorHandler(\"configIsRequired\");\n    if (!(0,_utils_isObject_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(config)) errorHandler(\"configType\");\n    if (config.urls) {\n        informAboutDeprecation();\n        return {\n            paths: {\n                vs: config.urls.monacoBase\n            }\n        };\n    }\n    return config;\n}\n/**\n * logs deprecation message\n */ function informAboutDeprecation() {\n    console.warn(errorMessages.deprecation);\n}\nfunction throwError(errorMessages, type) {\n    throw new Error(errorMessages[type] || errorMessages[\"default\"]);\n}\nvar errorMessages = {\n    configIsRequired: \"the configuration object is required\",\n    configType: \"the configuration object should be an object\",\n    \"default\": \"an unknown error accured in `@monaco-editor/loader` package\",\n    deprecation: \"Deprecation warning!\\n    You are using deprecated way of configuration.\\n\\n    Instead of using\\n      monaco.config({ urls: { monacoBase: '...' } })\\n    use\\n      monaco.config({ paths: { vs: '...' } })\\n\\n    For more please check the link https://github.com/suren-atoyan/monaco-loader#config\\n  \"\n};\nvar errorHandler = (0,_utils_curry_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(throwError)(errorMessages);\nvar validators = {\n    config: validateConfig\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (validators);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@monaco-editor/loader/lib/es/validators/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@monaco-editor/react/dist/index.mjs":
/*!**********************************************************!*\
  !*** ./node_modules/@monaco-editor/react/dist/index.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DiffEditor: () => (/* binding */ we),\n/* harmony export */   Editor: () => (/* binding */ de),\n/* harmony export */   \"default\": () => (/* binding */ Ft),\n/* harmony export */   loader: () => (/* reexport safe */ _monaco_editor_loader__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   useMonaco: () => (/* binding */ Le)\n/* harmony export */ });\n/* harmony import */ var _monaco_editor_loader__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @monaco-editor/loader */ \"(ssr)/./node_modules/@monaco-editor/loader/lib/es/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n\n\n\n\n\n\nvar le = {\n    wrapper: {\n        display: \"flex\",\n        position: \"relative\",\n        textAlign: \"initial\"\n    },\n    fullWidth: {\n        width: \"100%\"\n    },\n    hide: {\n        display: \"none\"\n    }\n}, v = le;\n\nvar ae = {\n    container: {\n        display: \"flex\",\n        height: \"100%\",\n        width: \"100%\",\n        justifyContent: \"center\",\n        alignItems: \"center\"\n    }\n}, Y = ae;\nfunction Me({ children: e }) {\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"div\", {\n        style: Y.container\n    }, e);\n}\nvar Z = Me;\nvar $ = Z;\nfunction Ee({ width: e, height: r, isEditorReady: n, loading: t, _ref: a, className: m, wrapperProps: E }) {\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"section\", {\n        style: {\n            ...v.wrapper,\n            width: e,\n            height: r\n        },\n        ...E\n    }, !n && /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement($, null, t), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"div\", {\n        ref: a,\n        style: {\n            ...v.fullWidth,\n            ...!n && v.hide\n        },\n        className: m\n    }));\n}\nvar ee = Ee;\nvar H = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(ee);\n\nfunction Ce(e) {\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(e, []);\n}\nvar k = Ce;\n\nfunction he(e, r, n = !0) {\n    let t = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(!0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(t.current || !n ? ()=>{\n        t.current = !1;\n    } : e, r);\n}\nvar l = he;\nfunction D() {}\nfunction h(e, r, n, t) {\n    return De(e, t) || be(e, r, n, t);\n}\nfunction De(e, r) {\n    return e.editor.getModel(te(e, r));\n}\nfunction be(e, r, n, t) {\n    return e.editor.createModel(r, n, t ? te(e, t) : void 0);\n}\nfunction te(e, r) {\n    return e.Uri.parse(r);\n}\nfunction Oe({ original: e, modified: r, language: n, originalLanguage: t, modifiedLanguage: a, originalModelPath: m, modifiedModelPath: E, keepCurrentOriginalModel: g = !1, keepCurrentModifiedModel: N = !1, theme: x = \"light\", loading: P = \"Loading...\", options: y = {}, height: V = \"100%\", width: z = \"100%\", className: F, wrapperProps: j = {}, beforeMount: A = D, onMount: q = D }) {\n    let [M, O] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(!1), [T, s] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(!0), u = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null), c = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null), w = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null), d = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(q), o = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(A), b = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(!1);\n    k(()=>{\n        let i = _monaco_editor_loader__WEBPACK_IMPORTED_MODULE_0__[\"default\"].init();\n        return i.then((f)=>(c.current = f) && s(!1)).catch((f)=>f?.type !== \"cancelation\" && console.error(\"Monaco initialization: error:\", f)), ()=>u.current ? I() : i.cancel();\n    }), l(()=>{\n        if (u.current && c.current) {\n            let i = u.current.getOriginalEditor(), f = h(c.current, e || \"\", t || n || \"text\", m || \"\");\n            f !== i.getModel() && i.setModel(f);\n        }\n    }, [\n        m\n    ], M), l(()=>{\n        if (u.current && c.current) {\n            let i = u.current.getModifiedEditor(), f = h(c.current, r || \"\", a || n || \"text\", E || \"\");\n            f !== i.getModel() && i.setModel(f);\n        }\n    }, [\n        E\n    ], M), l(()=>{\n        let i = u.current.getModifiedEditor();\n        i.getOption(c.current.editor.EditorOption.readOnly) ? i.setValue(r || \"\") : r !== i.getValue() && (i.executeEdits(\"\", [\n            {\n                range: i.getModel().getFullModelRange(),\n                text: r || \"\",\n                forceMoveMarkers: !0\n            }\n        ]), i.pushUndoStop());\n    }, [\n        r\n    ], M), l(()=>{\n        u.current?.getModel()?.original.setValue(e || \"\");\n    }, [\n        e\n    ], M), l(()=>{\n        let { original: i, modified: f } = u.current.getModel();\n        c.current.editor.setModelLanguage(i, t || n || \"text\"), c.current.editor.setModelLanguage(f, a || n || \"text\");\n    }, [\n        n,\n        t,\n        a\n    ], M), l(()=>{\n        c.current?.editor.setTheme(x);\n    }, [\n        x\n    ], M), l(()=>{\n        u.current?.updateOptions(y);\n    }, [\n        y\n    ], M);\n    let L = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!c.current) return;\n        o.current(c.current);\n        let i = h(c.current, e || \"\", t || n || \"text\", m || \"\"), f = h(c.current, r || \"\", a || n || \"text\", E || \"\");\n        u.current?.setModel({\n            original: i,\n            modified: f\n        });\n    }, [\n        n,\n        r,\n        a,\n        e,\n        t,\n        m,\n        E\n    ]), U = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        !b.current && w.current && (u.current = c.current.editor.createDiffEditor(w.current, {\n            automaticLayout: !0,\n            ...y\n        }), L(), c.current?.editor.setTheme(x), O(!0), b.current = !0);\n    }, [\n        y,\n        x,\n        L\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        M && d.current(u.current, c.current);\n    }, [\n        M\n    ]), (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        !T && !M && U();\n    }, [\n        T,\n        M,\n        U\n    ]);\n    function I() {\n        let i = u.current?.getModel();\n        g || i?.original?.dispose(), N || i?.modified?.dispose(), u.current?.dispose();\n    }\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(H, {\n        width: z,\n        height: V,\n        isEditorReady: M,\n        loading: P,\n        _ref: w,\n        className: F,\n        wrapperProps: j\n    });\n}\nvar ie = Oe;\nvar we = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(ie);\n\n\nfunction Pe() {\n    let [e, r] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_monaco_editor_loader__WEBPACK_IMPORTED_MODULE_0__[\"default\"].__getMonacoInstance());\n    return k(()=>{\n        let n;\n        return e || (n = _monaco_editor_loader__WEBPACK_IMPORTED_MODULE_0__[\"default\"].init(), n.then((t)=>{\n            r(t);\n        })), ()=>n?.cancel();\n    }), e;\n}\nvar Le = Pe;\n\n\n\n\nfunction He(e) {\n    let r = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        r.current = e;\n    }, [\n        e\n    ]), r.current;\n}\nvar se = He;\nvar _ = new Map;\nfunction Ve({ defaultValue: e, defaultLanguage: r, defaultPath: n, value: t, language: a, path: m, theme: E = \"light\", line: g, loading: N = \"Loading...\", options: x = {}, overrideServices: P = {}, saveViewState: y = !0, keepCurrentModel: V = !1, width: z = \"100%\", height: F = \"100%\", className: j, wrapperProps: A = {}, beforeMount: q = D, onMount: M = D, onChange: O, onValidate: T = D }) {\n    let [s, u] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(!1), [c, w] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(!0), d = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null), o = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null), b = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null), L = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(M), U = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(q), I = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(), i = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(t), f = se(m), Q = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(!1), B = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(!1);\n    k(()=>{\n        let p = _monaco_editor_loader__WEBPACK_IMPORTED_MODULE_0__[\"default\"].init();\n        return p.then((R)=>(d.current = R) && w(!1)).catch((R)=>R?.type !== \"cancelation\" && console.error(\"Monaco initialization: error:\", R)), ()=>o.current ? pe() : p.cancel();\n    }), l(()=>{\n        let p = h(d.current, e || t || \"\", r || a || \"\", m || n || \"\");\n        p !== o.current?.getModel() && (y && _.set(f, o.current?.saveViewState()), o.current?.setModel(p), y && o.current?.restoreViewState(_.get(m)));\n    }, [\n        m\n    ], s), l(()=>{\n        o.current?.updateOptions(x);\n    }, [\n        x\n    ], s), l(()=>{\n        !o.current || t === void 0 || (o.current.getOption(d.current.editor.EditorOption.readOnly) ? o.current.setValue(t) : t !== o.current.getValue() && (B.current = !0, o.current.executeEdits(\"\", [\n            {\n                range: o.current.getModel().getFullModelRange(),\n                text: t,\n                forceMoveMarkers: !0\n            }\n        ]), o.current.pushUndoStop(), B.current = !1));\n    }, [\n        t\n    ], s), l(()=>{\n        let p = o.current?.getModel();\n        p && a && d.current?.editor.setModelLanguage(p, a);\n    }, [\n        a\n    ], s), l(()=>{\n        g !== void 0 && o.current?.revealLine(g);\n    }, [\n        g\n    ], s), l(()=>{\n        d.current?.editor.setTheme(E);\n    }, [\n        E\n    ], s);\n    let X = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!(!b.current || !d.current) && !Q.current) {\n            U.current(d.current);\n            let p = m || n, R = h(d.current, t || e || \"\", r || a || \"\", p || \"\");\n            o.current = d.current?.editor.create(b.current, {\n                model: R,\n                automaticLayout: !0,\n                ...x\n            }, P), y && o.current.restoreViewState(_.get(p)), d.current.editor.setTheme(E), g !== void 0 && o.current.revealLine(g), u(!0), Q.current = !0;\n        }\n    }, [\n        e,\n        r,\n        n,\n        t,\n        a,\n        m,\n        x,\n        P,\n        y,\n        E,\n        g\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        s && L.current(o.current, d.current);\n    }, [\n        s\n    ]), (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        !c && !s && X();\n    }, [\n        c,\n        s,\n        X\n    ]), i.current = t, (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        s && O && (I.current?.dispose(), I.current = o.current?.onDidChangeModelContent((p)=>{\n            B.current || O(o.current.getValue(), p);\n        }));\n    }, [\n        s,\n        O\n    ]), (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (s) {\n            let p = d.current.editor.onDidChangeMarkers((R)=>{\n                let G = o.current.getModel()?.uri;\n                if (G && R.find((J)=>J.path === G.path)) {\n                    let J = d.current.editor.getModelMarkers({\n                        resource: G\n                    });\n                    T?.(J);\n                }\n            });\n            return ()=>{\n                p?.dispose();\n            };\n        }\n        return ()=>{};\n    }, [\n        s,\n        T\n    ]);\n    function pe() {\n        I.current?.dispose(), V ? y && _.set(m, o.current.saveViewState()) : o.current.getModel()?.dispose(), o.current.dispose();\n    }\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(H, {\n        width: z,\n        height: F,\n        isEditorReady: s,\n        loading: N,\n        _ref: b,\n        className: j,\n        wrapperProps: A\n    });\n}\nvar fe = Ve;\nvar de = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(fe);\nvar Ft = de;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@monaco-editor/react/dist/index.mjs\n");

/***/ })

};
;