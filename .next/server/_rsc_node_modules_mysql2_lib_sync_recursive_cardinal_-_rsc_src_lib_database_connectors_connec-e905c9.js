/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_node_modules_mysql2_lib_sync_recursive_cardinal_-_rsc_src_lib_database_connectors_connec-e905c9";
exports.ids = ["_rsc_node_modules_mysql2_lib_sync_recursive_cardinal_-_rsc_src_lib_database_connectors_connec-e905c9"];
exports.modules = {

/***/ "(rsc)/./node_modules/mysql2/lib sync recursive ^cardinal.*$":
/*!****************************************************!*\
  !*** ./node_modules/mysql2/lib/ sync ^cardinal.*$ ***!
  \****************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/mysql2/lib sync recursive ^cardinal.*$";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./src/lib/database/connectors/base-connector.ts":
/*!*******************************************************!*\
  !*** ./src/lib/database/connectors/base-connector.ts ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseConnector: () => (/* binding */ BaseConnector)\n/* harmony export */ });\nclass BaseConnector {\n    constructor(config){\n        this.config = {\n            ...config,\n            connectionTimeout: config.connectionTimeout || 30000,\n            queryTimeout: config.queryTimeout || 60000\n        };\n    }\n    sanitizeQuery(sql) {\n        // Remove dangerous SQL patterns\n        const dangerousPatterns = [\n            /;\\s*(drop|delete|truncate|alter)\\s+/gi,\n            /;\\s*exec\\s*\\(/gi,\n            /;\\s*execute\\s*\\(/gi,\n            /xp_cmdshell/gi,\n            /sp_executesql/gi\n        ];\n        for (const pattern of dangerousPatterns){\n            if (pattern.test(sql)) {\n                throw new Error(\"Query contains potentially dangerous SQL patterns\");\n            }\n        }\n        return sql.trim();\n    }\n    async withTimeout(promise, timeoutMs, errorMessage) {\n        const timeout = new Promise((_, reject)=>setTimeout(()=>reject(new Error(errorMessage)), timeoutMs));\n        return Promise.race([\n            promise,\n            timeout\n        ]);\n    }\n    formatError(error) {\n        if (error instanceof Error) {\n            return error;\n        }\n        return new Error(String(error));\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RhdGFiYXNlL2Nvbm5lY3RvcnMvYmFzZS1jb25uZWN0b3IudHMiLCJtYXBwaW5ncyI6Ijs7OztBQWtETyxNQUFlQTtJQUlwQkMsWUFBWUMsTUFBMEIsQ0FBRTtRQUN0QyxJQUFJLENBQUNBLE1BQU0sR0FBRztZQUNaLEdBQUdBLE1BQU07WUFDVEMsbUJBQW1CRCxPQUFPQyxpQkFBaUIsSUFBSTtZQUMvQ0MsY0FBY0YsT0FBT0UsWUFBWSxJQUFJO1FBQ3ZDO0lBQ0Y7SUFTVUMsY0FBY0MsR0FBVyxFQUFVO1FBQzNDLGdDQUFnQztRQUNoQyxNQUFNQyxvQkFBb0I7WUFDeEI7WUFDQTtZQUNBO1lBQ0E7WUFDQTtTQUNEO1FBRUQsS0FBSyxNQUFNQyxXQUFXRCxrQkFBbUI7WUFDdkMsSUFBSUMsUUFBUUMsSUFBSSxDQUFDSCxNQUFNO2dCQUNyQixNQUFNLElBQUlJLE1BQU07WUFDbEI7UUFDRjtRQUVBLE9BQU9KLElBQUlLLElBQUk7SUFDakI7SUFFQSxNQUFnQkMsWUFDZEMsT0FBbUIsRUFDbkJDLFNBQWlCLEVBQ2pCQyxZQUFvQixFQUNSO1FBQ1osTUFBTUMsVUFBVSxJQUFJQyxRQUFlLENBQUNDLEdBQUdDLFNBQ3JDQyxXQUFXLElBQU1ELE9BQU8sSUFBSVQsTUFBTUssZ0JBQWdCRDtRQUdwRCxPQUFPRyxRQUFRSSxJQUFJLENBQUM7WUFBQ1I7WUFBU0c7U0FBUTtJQUN4QztJQUVVTSxZQUFZQyxLQUFVLEVBQVM7UUFDdkMsSUFBSUEsaUJBQWlCYixPQUFPO1lBQzFCLE9BQU9hO1FBQ1Q7UUFDQSxPQUFPLElBQUliLE1BQU1jLE9BQU9EO0lBQzFCO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9xdWVyeWNyYWZ0LXN0dWRpby8uL3NyYy9saWIvZGF0YWJhc2UvY29ubmVjdG9ycy9iYXNlLWNvbm5lY3Rvci50cz8yM2E4Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBpbnRlcmZhY2UgRGF0YWJhc2VDb25uZWN0aW9uIHtcbiAgaG9zdDogc3RyaW5nO1xuICBwb3J0OiBudW1iZXI7XG4gIGRhdGFiYXNlOiBzdHJpbmc7XG4gIHVzZXJuYW1lOiBzdHJpbmc7XG4gIHBhc3N3b3JkOiBzdHJpbmc7XG4gIHNzbD86IGJvb2xlYW47XG4gIGNvbm5lY3Rpb25UaW1lb3V0PzogbnVtYmVyO1xuICBxdWVyeVRpbWVvdXQ/OiBudW1iZXI7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgUXVlcnlSZXN1bHQge1xuICByb3dzOiBhbnlbXTtcbiAgZmllbGRzOiBBcnJheTx7XG4gICAgbmFtZTogc3RyaW5nO1xuICAgIHR5cGU6IHN0cmluZztcbiAgICBudWxsYWJsZTogYm9vbGVhbjtcbiAgfT47XG4gIHJvd0NvdW50OiBudW1iZXI7XG4gIGV4ZWN1dGlvblRpbWU6IG51bWJlcjtcbn1cblxuZXhwb3J0IGludGVyZmFjZSBTY2hlbWFJbmZvIHtcbiAgdGFibGVzOiBBcnJheTx7XG4gICAgbmFtZTogc3RyaW5nO1xuICAgIHNjaGVtYTogc3RyaW5nO1xuICAgIGNvbHVtbnM6IEFycmF5PHtcbiAgICAgIG5hbWU6IHN0cmluZztcbiAgICAgIHR5cGU6IHN0cmluZztcbiAgICAgIG51bGxhYmxlOiBib29sZWFuO1xuICAgICAgZGVmYXVsdFZhbHVlPzogYW55O1xuICAgICAgaXNQcmltYXJ5S2V5OiBib29sZWFuO1xuICAgICAgaXNGb3JlaWduS2V5OiBib29sZWFuO1xuICAgICAgcmVmZXJlbmNlZFRhYmxlPzogc3RyaW5nO1xuICAgICAgcmVmZXJlbmNlZENvbHVtbj86IHN0cmluZztcbiAgICB9PjtcbiAgICBpbmRleGVzOiBBcnJheTx7XG4gICAgICBuYW1lOiBzdHJpbmc7XG4gICAgICBjb2x1bW5zOiBzdHJpbmdbXTtcbiAgICAgIGlzVW5pcXVlOiBib29sZWFuO1xuICAgICAgaXNQcmltYXJ5OiBib29sZWFuO1xuICAgIH0+O1xuICB9PjtcbiAgdmlld3M6IEFycmF5PHtcbiAgICBuYW1lOiBzdHJpbmc7XG4gICAgc2NoZW1hOiBzdHJpbmc7XG4gICAgZGVmaW5pdGlvbjogc3RyaW5nO1xuICB9Pjtcbn1cblxuZXhwb3J0IGFic3RyYWN0IGNsYXNzIEJhc2VDb25uZWN0b3Ige1xuICBwcm90ZWN0ZWQgY29uZmlnOiBEYXRhYmFzZUNvbm5lY3Rpb247XG4gIHByb3RlY3RlZCBwb29sOiBhbnk7XG5cbiAgY29uc3RydWN0b3IoY29uZmlnOiBEYXRhYmFzZUNvbm5lY3Rpb24pIHtcbiAgICB0aGlzLmNvbmZpZyA9IHtcbiAgICAgIC4uLmNvbmZpZyxcbiAgICAgIGNvbm5lY3Rpb25UaW1lb3V0OiBjb25maWcuY29ubmVjdGlvblRpbWVvdXQgfHwgMzAwMDAsXG4gICAgICBxdWVyeVRpbWVvdXQ6IGNvbmZpZy5xdWVyeVRpbWVvdXQgfHwgNjAwMDAsXG4gICAgfTtcbiAgfVxuXG4gIGFic3RyYWN0IGNvbm5lY3QoKTogUHJvbWlzZTx2b2lkPjtcbiAgYWJzdHJhY3QgZGlzY29ubmVjdCgpOiBQcm9taXNlPHZvaWQ+O1xuICBhYnN0cmFjdCB0ZXN0Q29ubmVjdGlvbigpOiBQcm9taXNlPGJvb2xlYW4+O1xuICBhYnN0cmFjdCBleGVjdXRlUXVlcnkoc3FsOiBzdHJpbmcsIHBhcmFtcz86IGFueVtdKTogUHJvbWlzZTxRdWVyeVJlc3VsdD47XG4gIGFic3RyYWN0IGdldFNjaGVtYSgpOiBQcm9taXNlPFNjaGVtYUluZm8+O1xuICBhYnN0cmFjdCB2YWxpZGF0ZVF1ZXJ5KHNxbDogc3RyaW5nKTogUHJvbWlzZTx7IGlzVmFsaWQ6IGJvb2xlYW47IGVycm9yPzogc3RyaW5nIH0+O1xuXG4gIHByb3RlY3RlZCBzYW5pdGl6ZVF1ZXJ5KHNxbDogc3RyaW5nKTogc3RyaW5nIHtcbiAgICAvLyBSZW1vdmUgZGFuZ2Vyb3VzIFNRTCBwYXR0ZXJuc1xuICAgIGNvbnN0IGRhbmdlcm91c1BhdHRlcm5zID0gW1xuICAgICAgLztcXHMqKGRyb3B8ZGVsZXRlfHRydW5jYXRlfGFsdGVyKVxccysvZ2ksXG4gICAgICAvO1xccypleGVjXFxzKlxcKC9naSxcbiAgICAgIC87XFxzKmV4ZWN1dGVcXHMqXFwoL2dpLFxuICAgICAgL3hwX2NtZHNoZWxsL2dpLFxuICAgICAgL3NwX2V4ZWN1dGVzcWwvZ2ksXG4gICAgXTtcblxuICAgIGZvciAoY29uc3QgcGF0dGVybiBvZiBkYW5nZXJvdXNQYXR0ZXJucykge1xuICAgICAgaWYgKHBhdHRlcm4udGVzdChzcWwpKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignUXVlcnkgY29udGFpbnMgcG90ZW50aWFsbHkgZGFuZ2Vyb3VzIFNRTCBwYXR0ZXJucycpO1xuICAgICAgfVxuICAgIH1cblxuICAgIHJldHVybiBzcWwudHJpbSgpO1xuICB9XG5cbiAgcHJvdGVjdGVkIGFzeW5jIHdpdGhUaW1lb3V0PFQ+KFxuICAgIHByb21pc2U6IFByb21pc2U8VD4sXG4gICAgdGltZW91dE1zOiBudW1iZXIsXG4gICAgZXJyb3JNZXNzYWdlOiBzdHJpbmdcbiAgKTogUHJvbWlzZTxUPiB7XG4gICAgY29uc3QgdGltZW91dCA9IG5ldyBQcm9taXNlPG5ldmVyPigoXywgcmVqZWN0KSA9PlxuICAgICAgc2V0VGltZW91dCgoKSA9PiByZWplY3QobmV3IEVycm9yKGVycm9yTWVzc2FnZSkpLCB0aW1lb3V0TXMpXG4gICAgKTtcblxuICAgIHJldHVybiBQcm9taXNlLnJhY2UoW3Byb21pc2UsIHRpbWVvdXRdKTtcbiAgfVxuXG4gIHByb3RlY3RlZCBmb3JtYXRFcnJvcihlcnJvcjogYW55KTogRXJyb3Ige1xuICAgIGlmIChlcnJvciBpbnN0YW5jZW9mIEVycm9yKSB7XG4gICAgICByZXR1cm4gZXJyb3I7XG4gICAgfVxuICAgIHJldHVybiBuZXcgRXJyb3IoU3RyaW5nKGVycm9yKSk7XG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJCYXNlQ29ubmVjdG9yIiwiY29uc3RydWN0b3IiLCJjb25maWciLCJjb25uZWN0aW9uVGltZW91dCIsInF1ZXJ5VGltZW91dCIsInNhbml0aXplUXVlcnkiLCJzcWwiLCJkYW5nZXJvdXNQYXR0ZXJucyIsInBhdHRlcm4iLCJ0ZXN0IiwiRXJyb3IiLCJ0cmltIiwid2l0aFRpbWVvdXQiLCJwcm9taXNlIiwidGltZW91dE1zIiwiZXJyb3JNZXNzYWdlIiwidGltZW91dCIsIlByb21pc2UiLCJfIiwicmVqZWN0Iiwic2V0VGltZW91dCIsInJhY2UiLCJmb3JtYXRFcnJvciIsImVycm9yIiwiU3RyaW5nIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database/connectors/base-connector.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database/connectors/connector-factory.ts":
/*!**********************************************************!*\
  !*** ./src/lib/database/connectors/connector-factory.ts ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConnectorFactory: () => (/* binding */ ConnectorFactory)\n/* harmony export */ });\n/* harmony import */ var _mysql_connector__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./mysql-connector */ \"(rsc)/./src/lib/database/connectors/mysql-connector.ts\");\n/* harmony import */ var _postgresql_connector__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./postgresql-connector */ \"(rsc)/./src/lib/database/connectors/postgresql-connector.ts\");\n\n\nclass ConnectorFactory {\n    static{\n        this.connectors = new Map();\n    }\n    static createConnector(type, config, connectionId) {\n        // If connectionId is provided and connector exists, return cached connector\n        if (connectionId && this.connectors.has(connectionId)) {\n            return this.connectors.get(connectionId);\n        }\n        let connector;\n        switch(type){\n            case \"MYSQL\":\n                connector = new _mysql_connector__WEBPACK_IMPORTED_MODULE_0__.MySQLConnector(config);\n                break;\n            case \"POSTGRESQL\":\n                connector = new _postgresql_connector__WEBPACK_IMPORTED_MODULE_1__.PostgreSQLConnector(config);\n                break;\n            default:\n                throw new Error(`Unsupported database type: ${type}`);\n        }\n        // Cache the connector if connectionId is provided\n        if (connectionId) {\n            this.connectors.set(connectionId, connector);\n        }\n        return connector;\n    }\n    static async removeConnector(connectionId) {\n        const connector = this.connectors.get(connectionId);\n        if (connector) {\n            try {\n                await connector.disconnect();\n            } catch (error) {\n                console.error(`Error disconnecting connector ${connectionId}:`, error);\n            }\n            this.connectors.delete(connectionId);\n        }\n    }\n    static async removeAllConnectors() {\n        const disconnectPromises = Array.from(this.connectors.entries()).map(async ([id, connector])=>{\n            try {\n                await connector.disconnect();\n            } catch (error) {\n                console.error(`Error disconnecting connector ${id}:`, error);\n            }\n        });\n        await Promise.allSettled(disconnectPromises);\n        this.connectors.clear();\n    }\n    static getActiveConnections() {\n        return Array.from(this.connectors.keys());\n    }\n    static getConnectionCount() {\n        return this.connectors.size;\n    }\n    static hasConnection(connectionId) {\n        return this.connectors.has(connectionId);\n    }\n}\n// Graceful shutdown handler\nprocess.on(\"SIGTERM\", async ()=>{\n    console.log(\"Received SIGTERM, closing database connections...\");\n    await ConnectorFactory.removeAllConnectors();\n    process.exit(0);\n});\nprocess.on(\"SIGINT\", async ()=>{\n    console.log(\"Received SIGINT, closing database connections...\");\n    await ConnectorFactory.removeAllConnectors();\n    process.exit(0);\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database/connectors/connector-factory.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database/connectors/mysql-connector.ts":
/*!********************************************************!*\
  !*** ./src/lib/database/connectors/mysql-connector.ts ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MySQLConnector: () => (/* binding */ MySQLConnector)\n/* harmony export */ });\n/* harmony import */ var mysql2_promise__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mysql2/promise */ \"(rsc)/./node_modules/mysql2/promise.js\");\n/* harmony import */ var _base_connector__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./base-connector */ \"(rsc)/./src/lib/database/connectors/base-connector.ts\");\n\n\nclass MySQLConnector extends _base_connector__WEBPACK_IMPORTED_MODULE_1__.BaseConnector {\n    constructor(config){\n        super(config);\n        this.pool = mysql2_promise__WEBPACK_IMPORTED_MODULE_0__.createPool({\n            host: config.host,\n            port: config.port,\n            database: config.database,\n            user: config.username,\n            password: config.password,\n            ssl: config.ssl ? {\n                rejectUnauthorized: false\n            } : undefined,\n            connectionLimit: 10,\n            charset: \"utf8mb4\"\n        });\n    }\n    async connect() {\n        try {\n            await this.withTimeout(this.pool.getConnection(), this.config.connectionTimeout, \"MySQL connection timeout\");\n        } catch (error) {\n            throw this.formatError(error);\n        }\n    }\n    async disconnect() {\n        try {\n            await this.pool.end();\n        } catch (error) {\n            throw this.formatError(error);\n        }\n    }\n    async testConnection() {\n        let connection = null;\n        try {\n            connection = await this.withTimeout(this.pool.getConnection(), this.config.connectionTimeout, \"MySQL connection timeout\");\n            await connection.query(\"SELECT 1\");\n            return true;\n        } catch (error) {\n            console.error(\"MySQL connection test failed:\", error);\n            return false;\n        } finally{\n            if (connection) {\n                connection.release();\n            }\n        }\n    }\n    async executeQuery(sql, params = []) {\n        const sanitizedSql = this.sanitizeQuery(sql);\n        let connection = null;\n        try {\n            const startTime = Date.now();\n            connection = await this.pool.getConnection();\n            const [rows, fields] = await this.withTimeout(connection.execute(sanitizedSql, params), this.config.queryTimeout, \"Query execution timeout\");\n            const executionTime = Date.now() - startTime;\n            const formattedFields = fields.map((field)=>({\n                    name: field.name,\n                    type: this.mapMySQLType(field.type || 0),\n                    nullable: true\n                }));\n            return {\n                rows: Array.isArray(rows) ? rows : [],\n                fields: formattedFields,\n                rowCount: Array.isArray(rows) ? rows.length : 0,\n                executionTime\n            };\n        } catch (error) {\n            throw this.formatError(error);\n        } finally{\n            if (connection) {\n                connection.release();\n            }\n        }\n    }\n    async getSchema() {\n        const tablesQuery = `\n      SELECT \n        t.TABLE_NAME as table_name,\n        t.TABLE_SCHEMA as table_schema,\n        c.COLUMN_NAME as column_name,\n        c.DATA_TYPE as data_type,\n        c.IS_NULLABLE as is_nullable,\n        c.COLUMN_DEFAULT as column_default,\n        CASE WHEN c.COLUMN_KEY = 'PRI' THEN 1 ELSE 0 END as is_primary_key,\n        CASE WHEN c.COLUMN_KEY = 'MUL' THEN 1 ELSE 0 END as is_foreign_key,\n        kcu.REFERENCED_TABLE_NAME as referenced_table_name,\n        kcu.REFERENCED_COLUMN_NAME as referenced_column_name\n      FROM information_schema.TABLES t\n      LEFT JOIN information_schema.COLUMNS c ON t.TABLE_NAME = c.TABLE_NAME AND t.TABLE_SCHEMA = c.TABLE_SCHEMA\n      LEFT JOIN information_schema.KEY_COLUMN_USAGE kcu ON c.TABLE_NAME = kcu.TABLE_NAME \n        AND c.COLUMN_NAME = kcu.COLUMN_NAME \n        AND c.TABLE_SCHEMA = kcu.TABLE_SCHEMA\n        AND kcu.REFERENCED_TABLE_NAME IS NOT NULL\n      WHERE t.TABLE_SCHEMA = ? AND t.TABLE_TYPE = 'BASE TABLE'\n      ORDER BY t.TABLE_NAME, c.ORDINAL_POSITION;\n    `;\n        const indexesQuery = `\n      SELECT \n        s.INDEX_NAME as index_name,\n        s.TABLE_NAME as table_name,\n        s.TABLE_SCHEMA as schema_name,\n        GROUP_CONCAT(s.COLUMN_NAME ORDER BY s.SEQ_IN_INDEX) as columns,\n        CASE WHEN s.NON_UNIQUE = 0 THEN 1 ELSE 0 END as is_unique,\n        CASE WHEN s.INDEX_NAME = 'PRIMARY' THEN 1 ELSE 0 END as is_primary\n      FROM information_schema.STATISTICS s\n      WHERE s.TABLE_SCHEMA = ?\n      GROUP BY s.INDEX_NAME, s.TABLE_NAME, s.TABLE_SCHEMA, s.NON_UNIQUE\n      ORDER BY s.TABLE_NAME, s.INDEX_NAME;\n    `;\n        const viewsQuery = `\n      SELECT \n        TABLE_NAME as view_name,\n        TABLE_SCHEMA as schema_name,\n        VIEW_DEFINITION as view_definition\n      FROM information_schema.VIEWS\n      WHERE TABLE_SCHEMA = ?\n      ORDER BY TABLE_NAME;\n    `;\n        try {\n            const [tablesResult, indexesResult, viewsResult] = await Promise.all([\n                this.executeQuery(tablesQuery, [\n                    this.config.database\n                ]),\n                this.executeQuery(indexesQuery, [\n                    this.config.database\n                ]),\n                this.executeQuery(viewsQuery, [\n                    this.config.database\n                ])\n            ]);\n            // Process tables and columns\n            const tablesMap = new Map();\n            for (const row of tablesResult.rows){\n                const tableKey = `${row.table_schema}.${row.table_name}`;\n                if (!tablesMap.has(tableKey)) {\n                    tablesMap.set(tableKey, {\n                        name: row.table_name,\n                        schema: row.table_schema,\n                        columns: [],\n                        indexes: []\n                    });\n                }\n                if (row.column_name) {\n                    tablesMap.get(tableKey).columns.push({\n                        name: row.column_name,\n                        type: row.data_type,\n                        nullable: row.is_nullable === \"YES\",\n                        defaultValue: row.column_default,\n                        isPrimaryKey: row.is_primary_key === 1,\n                        isForeignKey: row.is_foreign_key === 1,\n                        referencedTable: row.referenced_table_name,\n                        referencedColumn: row.referenced_column_name\n                    });\n                }\n            }\n            // Process indexes\n            for (const row of indexesResult.rows){\n                const tableKey = `${row.schema_name}.${row.table_name}`;\n                if (tablesMap.has(tableKey)) {\n                    tablesMap.get(tableKey).indexes.push({\n                        name: row.index_name,\n                        columns: row.columns ? row.columns.split(\",\") : [],\n                        isUnique: row.is_unique === 1,\n                        isPrimary: row.is_primary === 1\n                    });\n                }\n            }\n            return {\n                tables: Array.from(tablesMap.values()),\n                views: viewsResult.rows.map((row)=>({\n                        name: row.view_name,\n                        schema: row.schema_name,\n                        definition: row.view_definition\n                    }))\n            };\n        } catch (error) {\n            throw this.formatError(error);\n        }\n    }\n    async validateQuery(sql) {\n        try {\n            const sanitizedSql = this.sanitizeQuery(sql);\n            // Use EXPLAIN to validate without executing\n            const explainSql = `EXPLAIN ${sanitizedSql}`;\n            await this.executeQuery(explainSql);\n            return {\n                isValid: true\n            };\n        } catch (error) {\n            return {\n                isValid: false,\n                error: error instanceof Error ? error.message : String(error)\n            };\n        }\n    }\n    mapMySQLType(type) {\n        // Map MySQL field types to readable names\n        const typeMap = {\n            0: \"decimal\",\n            1: \"tinyint\",\n            2: \"smallint\",\n            3: \"int\",\n            4: \"float\",\n            5: \"double\",\n            7: \"timestamp\",\n            8: \"bigint\",\n            9: \"mediumint\",\n            10: \"date\",\n            11: \"time\",\n            12: \"datetime\",\n            13: \"year\",\n            15: \"varchar\",\n            16: \"bit\",\n            245: \"json\",\n            246: \"decimal\",\n            247: \"enum\",\n            248: \"set\",\n            249: \"tinyblob\",\n            250: \"mediumblob\",\n            251: \"longblob\",\n            252: \"blob\",\n            253: \"varchar\",\n            254: \"char\",\n            255: \"geometry\"\n        };\n        return typeMap[type] || \"unknown\";\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database/connectors/mysql-connector.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database/connectors/postgresql-connector.ts":
/*!*************************************************************!*\
  !*** ./src/lib/database/connectors/postgresql-connector.ts ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PostgreSQLConnector: () => (/* binding */ PostgreSQLConnector)\n/* harmony export */ });\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pg */ \"pg\");\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(pg__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _base_connector__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./base-connector */ \"(rsc)/./src/lib/database/connectors/base-connector.ts\");\n\n\nclass PostgreSQLConnector extends _base_connector__WEBPACK_IMPORTED_MODULE_1__.BaseConnector {\n    constructor(config){\n        super(config);\n        const poolConfig = {\n            host: config.host,\n            port: config.port,\n            database: config.database,\n            user: config.username,\n            password: config.password,\n            ssl: config.ssl ? {\n                rejectUnauthorized: false\n            } : false,\n            max: 10,\n            min: 1,\n            idleTimeoutMillis: 30000,\n            connectionTimeoutMillis: config.connectionTimeout,\n            query_timeout: config.queryTimeout\n        };\n        this.pool = new pg__WEBPACK_IMPORTED_MODULE_0__.Pool(poolConfig);\n        // Handle pool errors\n        this.pool.on(\"error\", (err)=>{\n            console.error(\"PostgreSQL pool error:\", err);\n        });\n    }\n    async connect() {\n        try {\n            await this.withTimeout(this.pool.connect(), this.config.connectionTimeout, \"PostgreSQL connection timeout\");\n        } catch (error) {\n            throw this.formatError(error);\n        }\n    }\n    async disconnect() {\n        try {\n            await this.pool.end();\n        } catch (error) {\n            throw this.formatError(error);\n        }\n    }\n    async testConnection() {\n        let client = null;\n        try {\n            client = await this.withTimeout(this.pool.connect(), this.config.connectionTimeout, \"PostgreSQL connection timeout\");\n            await client.query(\"SELECT 1\");\n            return true;\n        } catch (error) {\n            console.error(\"PostgreSQL connection test failed:\", error);\n            return false;\n        } finally{\n            if (client) {\n                client.release();\n            }\n        }\n    }\n    async executeQuery(sql, params = []) {\n        const sanitizedSql = this.sanitizeQuery(sql);\n        let client = null;\n        try {\n            const startTime = Date.now();\n            client = await this.pool.connect();\n            const result = await this.withTimeout(client.query(sanitizedSql, params), this.config.queryTimeout, \"Query execution timeout\");\n            const executionTime = Date.now() - startTime;\n            const fields = result.fields.map((field)=>({\n                    name: field.name,\n                    type: this.mapPostgreSQLType(field.dataTypeID),\n                    nullable: true\n                }));\n            return {\n                rows: result.rows,\n                fields,\n                rowCount: result.rowCount || 0,\n                executionTime\n            };\n        } catch (error) {\n            throw this.formatError(error);\n        } finally{\n            if (client) {\n                client.release();\n            }\n        }\n    }\n    async getSchema() {\n        const tablesQuery = `\n      SELECT \n        t.table_name,\n        t.table_schema,\n        c.column_name,\n        c.data_type,\n        c.is_nullable,\n        c.column_default,\n        CASE WHEN pk.column_name IS NOT NULL THEN true ELSE false END as is_primary_key,\n        CASE WHEN fk.column_name IS NOT NULL THEN true ELSE false END as is_foreign_key,\n        fk.referenced_table_name,\n        fk.referenced_column_name\n      FROM information_schema.tables t\n      LEFT JOIN information_schema.columns c ON t.table_name = c.table_name AND t.table_schema = c.table_schema\n      LEFT JOIN (\n        SELECT ku.table_name, ku.column_name, ku.table_schema\n        FROM information_schema.table_constraints tc\n        JOIN information_schema.key_column_usage ku ON tc.constraint_name = ku.constraint_name\n        WHERE tc.constraint_type = 'PRIMARY KEY'\n      ) pk ON c.table_name = pk.table_name AND c.column_name = pk.column_name AND c.table_schema = pk.table_schema\n      LEFT JOIN (\n        SELECT \n          ku.table_name, \n          ku.column_name, \n          ku.table_schema,\n          ccu.table_name as referenced_table_name,\n          ccu.column_name as referenced_column_name\n        FROM information_schema.table_constraints tc\n        JOIN information_schema.key_column_usage ku ON tc.constraint_name = ku.constraint_name\n        JOIN information_schema.constraint_column_usage ccu ON tc.constraint_name = ccu.constraint_name\n        WHERE tc.constraint_type = 'FOREIGN KEY'\n      ) fk ON c.table_name = fk.table_name AND c.column_name = fk.column_name AND c.table_schema = fk.table_schema\n      WHERE t.table_schema NOT IN ('information_schema', 'pg_catalog', 'pg_toast')\n      ORDER BY t.table_name, c.ordinal_position;\n    `;\n        const indexesQuery = `\n      SELECT \n        i.relname as index_name,\n        t.relname as table_name,\n        n.nspname as schema_name,\n        array_agg(a.attname ORDER BY a.attnum) as columns,\n        ix.indisunique as is_unique,\n        ix.indisprimary as is_primary\n      FROM pg_class i\n      JOIN pg_index ix ON i.oid = ix.indexrelid\n      JOIN pg_class t ON ix.indrelid = t.oid\n      JOIN pg_namespace n ON t.relnamespace = n.oid\n      JOIN pg_attribute a ON t.oid = a.attrelid AND a.attnum = ANY(ix.indkey)\n      WHERE n.nspname NOT IN ('information_schema', 'pg_catalog', 'pg_toast')\n      GROUP BY i.relname, t.relname, n.nspname, ix.indisunique, ix.indisprimary\n      ORDER BY t.relname, i.relname;\n    `;\n        const viewsQuery = `\n      SELECT \n        table_name as view_name,\n        table_schema as schema_name,\n        view_definition\n      FROM information_schema.views\n      WHERE table_schema NOT IN ('information_schema', 'pg_catalog')\n      ORDER BY table_name;\n    `;\n        try {\n            const [tablesResult, indexesResult, viewsResult] = await Promise.all([\n                this.executeQuery(tablesQuery),\n                this.executeQuery(indexesQuery),\n                this.executeQuery(viewsQuery)\n            ]);\n            // Process tables and columns\n            const tablesMap = new Map();\n            for (const row of tablesResult.rows){\n                const tableKey = `${row.table_schema}.${row.table_name}`;\n                if (!tablesMap.has(tableKey)) {\n                    tablesMap.set(tableKey, {\n                        name: row.table_name,\n                        schema: row.table_schema,\n                        columns: [],\n                        indexes: []\n                    });\n                }\n                if (row.column_name) {\n                    tablesMap.get(tableKey).columns.push({\n                        name: row.column_name,\n                        type: row.data_type,\n                        nullable: row.is_nullable === \"YES\",\n                        defaultValue: row.column_default,\n                        isPrimaryKey: row.is_primary_key,\n                        isForeignKey: row.is_foreign_key,\n                        referencedTable: row.referenced_table_name,\n                        referencedColumn: row.referenced_column_name\n                    });\n                }\n            }\n            // Process indexes\n            for (const row of indexesResult.rows){\n                const tableKey = `${row.schema_name}.${row.table_name}`;\n                if (tablesMap.has(tableKey)) {\n                    tablesMap.get(tableKey).indexes.push({\n                        name: row.index_name,\n                        columns: row.columns,\n                        isUnique: row.is_unique,\n                        isPrimary: row.is_primary\n                    });\n                }\n            }\n            return {\n                tables: Array.from(tablesMap.values()),\n                views: viewsResult.rows.map((row)=>({\n                        name: row.view_name,\n                        schema: row.schema_name,\n                        definition: row.view_definition\n                    }))\n            };\n        } catch (error) {\n            throw this.formatError(error);\n        }\n    }\n    async validateQuery(sql) {\n        try {\n            const sanitizedSql = this.sanitizeQuery(sql);\n            // Use EXPLAIN to validate without executing\n            const explainSql = `EXPLAIN ${sanitizedSql}`;\n            await this.executeQuery(explainSql);\n            return {\n                isValid: true\n            };\n        } catch (error) {\n            return {\n                isValid: false,\n                error: error instanceof Error ? error.message : String(error)\n            };\n        }\n    }\n    mapPostgreSQLType(dataTypeID) {\n        // Map PostgreSQL OID types to readable names\n        const typeMap = {\n            16: \"boolean\",\n            20: \"bigint\",\n            21: \"smallint\",\n            23: \"integer\",\n            25: \"text\",\n            700: \"real\",\n            701: \"double precision\",\n            1043: \"varchar\",\n            1082: \"date\",\n            1114: \"timestamp\",\n            1184: \"timestamptz\"\n        };\n        return typeMap[dataTypeID] || \"unknown\";\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database/connectors/postgresql-connector.ts\n");

/***/ })

};
;