"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[850],{33277:function(e,t,r){r.d(t,{C:function(){return s}});var a=r(57437);r(2265);var n=r(96061),o=r(22169);let l=(0,n.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function s(e){let{className:t,variant:r,...n}=e;return(0,a.jsx)("div",{className:(0,o.cn)(l({variant:r}),t),...n})}},575:function(e,t,r){r.d(t,{z:function(){return d}});var a=r(57437),n=r(2265),o=r(67256),l=r(96061),s=r(22169);let i=(0,l.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=n.forwardRef((e,t)=>{let{className:r,variant:n,size:l,asChild:d=!1,...c}=e,u=d?o.g7:"button";return(0,a.jsx)(u,{className:(0,s.cn)(i({variant:n,size:l,className:r})),ref:t,...c})});d.displayName="Button"},15671:function(e,t,r){r.d(t,{Ol:function(){return s},Zb:function(){return l},aY:function(){return d},ll:function(){return i}});var a=r(57437),n=r(2265),o=r(22169);let l=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("div",{ref:t,className:(0,o.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...n})});l.displayName="Card";let s=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("div",{ref:t,className:(0,o.cn)("flex flex-col space-y-1.5 p-6",r),...n})});s.displayName="CardHeader";let i=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("h3",{ref:t,className:(0,o.cn)("text-2xl font-semibold leading-none tracking-tight",r),...n})});i.displayName="CardTitle",n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("p",{ref:t,className:(0,o.cn)("text-sm text-muted-foreground",r),...n})}).displayName="CardDescription";let d=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("div",{ref:t,className:(0,o.cn)("p-6 pt-0",r),...n})});d.displayName="CardContent",n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("div",{ref:t,className:(0,o.cn)("flex items-center p-6 pt-0",r),...n})}).displayName="CardFooter"},22782:function(e,t,r){r.d(t,{I:function(){return l}});var a=r(57437),n=r(2265),o=r(22169);let l=n.forwardRef((e,t)=>{let{className:r,type:n,...l}=e;return(0,a.jsx)("input",{type:n,className:(0,o.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r),ref:t,...l})});l.displayName="Input"},18641:function(e,t,r){r.d(t,{Bw:function(){return g},Ph:function(){return c},Ql:function(){return b},i4:function(){return f},ki:function(){return u}});var a=r(57437),n=r(2265),o=r(28010),l=r(83523),s=r(9224),i=r(62442),d=r(22169);let c=o.fC;o.ZA;let u=o.B4,f=n.forwardRef((e,t)=>{let{className:r,children:n,...s}=e;return(0,a.jsxs)(o.xz,{ref:t,className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",r),...s,children:[n,(0,a.jsx)(o.JO,{asChild:!0,children:(0,a.jsx)(l.Z,{className:"h-4 w-4 opacity-50"})})]})});f.displayName=o.xz.displayName;let m=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)(o.u_,{ref:t,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",r),...n,children:(0,a.jsx)(s.Z,{className:"h-4 w-4"})})});m.displayName=o.u_.displayName;let p=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)(o.$G,{ref:t,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",r),...n,children:(0,a.jsx)(l.Z,{className:"h-4 w-4"})})});p.displayName=o.$G.displayName;let g=n.forwardRef((e,t)=>{let{className:r,children:n,position:l="popper",...s}=e;return(0,a.jsx)(o.h_,{children:(0,a.jsxs)(o.VY,{ref:t,className:(0,d.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===l&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",r),position:l,...s,children:[(0,a.jsx)(m,{}),(0,a.jsx)(o.l_,{className:(0,d.cn)("p-1","popper"===l&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:n}),(0,a.jsx)(p,{})]})})});g.displayName=o.VY.displayName,n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)(o.__,{ref:t,className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",r),...n})}).displayName=o.__.displayName;let b=n.forwardRef((e,t)=>{let{className:r,children:n,...l}=e;return(0,a.jsxs)(o.ck,{ref:t,className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r),...l,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(o.wU,{children:(0,a.jsx)(i.Z,{className:"h-4 w-4"})})}),(0,a.jsx)(o.eT,{children:n})]})});b.displayName=o.ck.displayName,n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)(o.Z0,{ref:t,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",r),...n})}).displayName=o.Z0.displayName},5887:function(e,t,r){r.d(t,{RM:function(){return i},SC:function(){return d},iA:function(){return l},pj:function(){return u},ss:function(){return c},xD:function(){return s}});var a=r(57437),n=r(2265),o=r(22169);let l=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("div",{className:"relative w-full overflow-auto",children:(0,a.jsx)("table",{ref:t,className:(0,o.cn)("w-full caption-bottom text-sm",r),...n})})});l.displayName="Table";let s=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("thead",{ref:t,className:(0,o.cn)("[&_tr]:border-b",r),...n})});s.displayName="TableHeader";let i=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("tbody",{ref:t,className:(0,o.cn)("[&_tr:last-child]:border-0",r),...n})});i.displayName="TableBody",n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("tfoot",{ref:t,className:(0,o.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",r),...n})}).displayName="TableFooter";let d=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("tr",{ref:t,className:(0,o.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",r),...n})});d.displayName="TableRow";let c=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("th",{ref:t,className:(0,o.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",r),...n})});c.displayName="TableHead";let u=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("td",{ref:t,className:(0,o.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",r),...n})});u.displayName="TableCell",n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("caption",{ref:t,className:(0,o.cn)("mt-4 text-sm text-muted-foreground",r),...n})}).displayName="TableCaption"},1326:function(e,t,r){r.d(t,{f:function(){return o},z:function(){return n}});var a=r(2265);function n(e){var t,r,n,o,l,s;let{userId:i,autoRefresh:d=!1,refreshInterval:c=3e4}=e,[u,f]=(0,a.useState)({}),[m,p]=(0,a.useState)(!1),[g,b]=(0,a.useState)(null),[x,h]=(0,a.useState)(null),y=(0,a.useCallback)(async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{let r=new URLSearchParams({userId:i,type:e,...t.days&&{days:t.days.toString()},...t.limit&&{limit:t.limit.toString()}}),a=await fetch("/api/analytics?".concat(r));if(!a.ok)throw Error("Failed to fetch ".concat(e," analytics"));return(await a.json()).data}catch(t){throw console.error("Error fetching ".concat(e," analytics:"),t),t}},[i]),v=(0,a.useCallback)(async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:7;p(!0),b(null);try{let[t,r,a,n,o]=await Promise.all([y("performance",{days:e}),y("usage",{days:e}),y("trends",{days:e}),y("slowQueries",{limit:10}),y("alerts")]);f({performance:t,usage:r,trends:a,slowQueries:n,alerts:o}),h(new Date)}catch(e){b(e instanceof Error?e.message:"Failed to load analytics")}finally{p(!1)}},[y]),w=(0,a.useCallback)(async(e,t)=>{try{let r=await fetch("/api/analytics",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({type:e,userId:i,data:t})});if(!r.ok)throw Error("Failed to record analytics event");return await r.json()}catch(e){throw console.error("Error recording analytics event:",e),e}},[i]),N=(0,a.useCallback)(async e=>w("query_execution",e),[w]),j=(0,a.useCallback)(async e=>w("collaboration_session",e),[w]),k=(0,a.useCallback)(async e=>w("user_action",e),[w]),C=(0,a.useCallback)(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:7;return y("performance",{days:e})},[y]),R=(0,a.useCallback)(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:7;return y("usage",{days:e})},[y]),_=(0,a.useCallback)(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:30;return y("trends",{days:e})},[y]),T=(0,a.useCallback)(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10;return y("slowQueries",{limit:e})},[y]),S=(0,a.useCallback)(()=>y("alerts"),[y]);return(0,a.useEffect)(()=>{if(d&&c>0){let e=setInterval(()=>{v()},c);return()=>clearInterval(e)}},[d,c,v]),(0,a.useEffect)(()=>{v()},[v]),{data:u,isLoading:m,error:g,lastUpdated:x,loadAllAnalytics:v,recordQueryExecution:N,recordCollaborationSession:j,recordUserAction:k,getPerformanceStats:C,getUsageOverview:R,getTrends:_,getSlowQueries:T,getAlerts:S,hasData:Object.keys(u).length>0,performanceScore:(null===(t=u.usage)||void 0===t?void 0:t.performanceScore)||0,totalQueries:(null===(r=u.usage)||void 0===r?void 0:r.totalQueries)||0,avgExecutionTime:(null===(n=u.usage)||void 0===n?void 0:n.avgExecutionTime)||0,errorRate:(null===(o=u.usage)||void 0===o?void 0:o.errorRate)||0,activeAlerts:(null===(l=u.alerts)||void 0===l?void 0:l.filter(e=>!e.resolved))||[],alertCount:(null===(s=u.alerts)||void 0===s?void 0:s.filter(e=>!e.resolved).length)||0}}function o(e){let{recordQueryExecution:t,recordCollaborationSession:r,recordUserAction:o}=n({userId:e}),l=(0,a.useCallback)(e=>{t(e).catch(console.error)},[t]),s=(0,a.useCallback)(e=>{r(e).catch(console.error)},[r]),i=(0,a.useCallback)((e,t,r)=>{o({action:e,feature:t,metadata:r}).catch(console.error)},[o]),d=(0,a.useCallback)(e=>{i("page_view","navigation",{page:e})},[i]),c=(0,a.useCallback)((e,t,r)=>{i(t,e,r)},[i]);return{trackQueryExecution:l,trackCollaboration:s,trackUserAction:i,trackPageView:d,trackFeatureUsage:c}}},22169:function(e,t,r){r.d(t,{SY:function(){return l},cn:function(){return o}});var a=r(57042),n=r(74769);function o(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,n.m6)((0,a.W)(t))}function l(e){let t=new Date,r=new Date(e),a=Math.floor((t.getTime()-r.getTime())/1e3);if(a<60)return"just now";let n=Math.floor(a/60);if(n<60)return"".concat(n," minute").concat(1===n?"":"s"," ago");let o=Math.floor(n/60);if(o<24)return"".concat(o," hour").concat(1===o?"":"s"," ago");let l=Math.floor(o/24);return l<7?"".concat(l," day").concat(1===l?"":"s"," ago"):new Intl.DateTimeFormat("en-US",{month:"long",day:"numeric",year:"numeric"}).format(new Date(e))}}}]);