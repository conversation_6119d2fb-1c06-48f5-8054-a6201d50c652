(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[544],{28203:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(62898).Z)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]])},49617:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(62898).Z)("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]])},92295:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(62898).Z)("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]])},85790:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(62898).Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},51548:function(e,s,a){Promise.resolve().then(a.bind(a,35779)),Promise.resolve().then(a.bind(a,46582))},46582:function(e,s,a){"use strict";a.r(s),a.d(s,{ProfilePage:function(){return w}});var t=a(57437),i=a(2265),n=a(15671),r=a(575),l=a(22782),d=a(12647),c=a(33277),o=a(75808),m=a(82749),u=a(92295),x=a(67972),h=a(49617),f=a(62898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let p=(0,f.Z)("Camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]]);var v=a(28203),g=a(85790),j=a(6141),y=a(97332);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let b=(0,f.Z)("Award",[["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}],["path",{d:"M15.477 12.89 17 22l-5-3-5 3 1.523-9.11",key:"em7aur"}]]);var N=a(30034);function w(){var e,s,a,f;let{data:w}=(0,m.useSession)(),[C,k]=(0,i.useState)(!1),[Z,E]=(0,i.useState)({name:(null==w?void 0:null===(e=w.user)||void 0===e?void 0:e.name)||"",email:(null==w?void 0:null===(s=w.user)||void 0===s?void 0:s.email)||"",bio:"",location:"",website:"",company:""}),S={totalQueries:247,successfulQueries:231,avgExecutionTime:1.2,connectionsCreated:5,daysActive:45,lastActive:new Date},M=(null===(a=Z.name)||void 0===a?void 0:a.split(" ").map(e=>e[0]).join("").toUpperCase())||"U";return(0,t.jsxs)("div",{className:"p-6 space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-foreground",children:"Profile"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Manage your profile information and view your activity"})]}),C&&(0,t.jsxs)(r.z,{onClick:()=>{console.log("Profile saved:",Z),k(!1)},children:[(0,t.jsx)(u.Z,{className:"h-4 w-4 mr-2"}),"Save Changes"]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,t.jsxs)(n.Zb,{className:"lg:col-span-2",children:[(0,t.jsx)(n.Ol,{children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)(n.ll,{className:"flex items-center gap-2",children:[(0,t.jsx)(x.Z,{className:"h-5 w-5"}),"Profile Information"]}),(0,t.jsxs)(r.z,{variant:"outline",size:"sm",onClick:()=>k(!C),children:[(0,t.jsx)(h.Z,{className:"h-4 w-4 mr-2"}),C?"Cancel":"Edit"]})]})}),(0,t.jsxs)(n.aY,{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsxs)(o.qE,{className:"h-20 w-20",children:[(0,t.jsx)(o.F$,{src:(null==w?void 0:null===(f=w.user)||void 0===f?void 0:f.image)||"",alt:Z.name}),(0,t.jsx)(o.Q5,{className:"text-lg",children:M})]}),C&&(0,t.jsx)(r.z,{variant:"outline",size:"icon",className:"absolute -bottom-2 -right-2 h-8 w-8 rounded-full",children:(0,t.jsx)(p,{className:"h-4 w-4"})})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-xl font-semibold",children:Z.name||"User"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:Z.email}),(0,t.jsxs)("div",{className:"flex items-center gap-2 mt-1",children:[(0,t.jsx)(c.C,{variant:"outline",children:"Free Plan"}),(0,t.jsxs)(c.C,{variant:"outline",className:"flex items-center gap-1",children:[(0,t.jsx)(v.Z,{className:"h-3 w-3"}),"Joined ",(0,N.WU)(new Date,"MMM yyyy")]})]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(d._,{htmlFor:"name",children:"Full Name"}),(0,t.jsx)(l.I,{id:"name",value:Z.name,onChange:e=>E({...Z,name:e.target.value}),disabled:!C})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(d._,{htmlFor:"email",children:"Email"}),(0,t.jsx)(l.I,{id:"email",type:"email",value:Z.email,onChange:e=>E({...Z,email:e.target.value}),disabled:!C})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(d._,{htmlFor:"company",children:"Company"}),(0,t.jsx)(l.I,{id:"company",value:Z.company,onChange:e=>E({...Z,company:e.target.value}),disabled:!C,placeholder:"Your company"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(d._,{htmlFor:"location",children:"Location"}),(0,t.jsx)(l.I,{id:"location",value:Z.location,onChange:e=>E({...Z,location:e.target.value}),disabled:!C,placeholder:"City, Country"})]}),(0,t.jsxs)("div",{className:"space-y-2 md:col-span-2",children:[(0,t.jsx)(d._,{htmlFor:"website",children:"Website"}),(0,t.jsx)(l.I,{id:"website",value:Z.website,onChange:e=>E({...Z,website:e.target.value}),disabled:!C,placeholder:"https://yourwebsite.com"})]}),(0,t.jsxs)("div",{className:"space-y-2 md:col-span-2",children:[(0,t.jsx)(d._,{htmlFor:"bio",children:"Bio"}),(0,t.jsx)(l.I,{id:"bio",value:Z.bio,onChange:e=>E({...Z,bio:e.target.value}),disabled:!C,placeholder:"Tell us about yourself"})]})]})]})]}),(0,t.jsxs)(n.Zb,{children:[(0,t.jsx)(n.Ol,{children:(0,t.jsxs)(n.ll,{className:"flex items-center gap-2",children:[(0,t.jsx)(g.Z,{className:"h-5 w-5"}),"Statistics"]})}),(0,t.jsxs)(n.aY,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"text-center p-3 bg-muted rounded-lg",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-primary",children:S.totalQueries}),(0,t.jsx)("div",{className:"text-sm text-muted-foreground",children:"Total Queries"})]}),(0,t.jsxs)("div",{className:"text-center p-3 bg-muted rounded-lg",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-green-600",children:S.successfulQueries}),(0,t.jsx)("div",{className:"text-sm text-muted-foreground",children:"Successful"})]}),(0,t.jsxs)("div",{className:"text-center p-3 bg-muted rounded-lg",children:[(0,t.jsxs)("div",{className:"text-2xl font-bold text-blue-600",children:[S.avgExecutionTime,"s"]}),(0,t.jsx)("div",{className:"text-sm text-muted-foreground",children:"Avg Time"})]}),(0,t.jsxs)("div",{className:"text-center p-3 bg-muted rounded-lg",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:S.connectionsCreated}),(0,t.jsx)("div",{className:"text-sm text-muted-foreground",children:"Connections"})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,t.jsxs)("span",{className:"flex items-center gap-2",children:[(0,t.jsx)(j.Z,{className:"h-4 w-4"}),"Days Active"]}),(0,t.jsx)("span",{className:"font-medium",children:S.daysActive})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,t.jsxs)("span",{className:"flex items-center gap-2",children:[(0,t.jsx)(y.Z,{className:"h-4 w-4"}),"Success Rate"]}),(0,t.jsxs)("span",{className:"font-medium",children:[Math.round(S.successfulQueries/S.totalQueries*100),"%"]})]})]})]})]}),(0,t.jsxs)(n.Zb,{className:"lg:col-span-2",children:[(0,t.jsx)(n.Ol,{children:(0,t.jsxs)(n.ll,{className:"flex items-center gap-2",children:[(0,t.jsx)(b,{className:"h-5 w-5"}),"Achievements"]})}),(0,t.jsx)(n.aY,{children:(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[{id:1,name:"First Query",description:"Executed your first SQL query",earned:!0},{id:2,name:"Speed Demon",description:"Query executed in under 100ms",earned:!0},{id:3,name:"Database Explorer",description:"Connected to 5 different databases",earned:!0},{id:4,name:"Query Master",description:"Executed 100 successful queries",earned:!0},{id:5,name:"Collaboration Pro",description:"Shared 10 queries with team",earned:!1},{id:6,name:"Optimization Expert",description:"Optimized 50 queries",earned:!1}].map(e=>(0,t.jsx)("div",{className:"p-4 rounded-lg border ".concat(e.earned?"bg-green-50 border-green-200":"bg-gray-50 border-gray-200"),children:(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"p-2 rounded-full ".concat(e.earned?"bg-green-100 text-green-600":"bg-gray-100 text-gray-400"),children:(0,t.jsx)(b,{className:"h-4 w-4"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium",children:e.name}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:e.description})]})]})},e.id))})})]}),(0,t.jsxs)(n.Zb,{children:[(0,t.jsx)(n.Ol,{children:(0,t.jsxs)(n.ll,{className:"flex items-center gap-2",children:[(0,t.jsx)(j.Z,{className:"h-5 w-5"}),"Recent Activity"]})}),(0,t.jsx)(n.aY,{children:(0,t.jsx)("div",{className:"space-y-4",children:[{id:1,action:"Executed query",details:"SELECT * FROM users WHERE...",time:"2 hours ago"},{id:2,action:"Created connection",details:"PostgreSQL Production DB",time:"1 day ago"},{id:3,action:"Shared query",details:"User analytics report",time:"2 days ago"},{id:4,action:"Optimized query",details:"Improved performance by 40%",time:"3 days ago"}].map(e=>(0,t.jsxs)("div",{className:"flex items-start gap-3",children:[(0,t.jsx)("div",{className:"p-1 bg-primary/10 rounded-full mt-1",children:(0,t.jsx)("div",{className:"h-2 w-2 bg-primary rounded-full"})}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsx)("p",{className:"text-sm font-medium",children:e.action}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground truncate",children:e.details}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:e.time})]})]},e.id))})})]})]})]})}},33277:function(e,s,a){"use strict";a.d(s,{C:function(){return l}});var t=a(57437);a(2265);var i=a(96061),n=a(22169);let r=(0,i.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function l(e){let{className:s,variant:a,...i}=e;return(0,t.jsx)("div",{className:(0,n.cn)(r({variant:a}),s),...i})}},22782:function(e,s,a){"use strict";a.d(s,{I:function(){return r}});var t=a(57437),i=a(2265),n=a(22169);let r=i.forwardRef((e,s)=>{let{className:a,type:i,...r}=e;return(0,t.jsx)("input",{type:i,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:s,...r})});r.displayName="Input"},12647:function(e,s,a){"use strict";a.d(s,{_:function(){return c}});var t=a(57437),i=a(2265),n=a(36743),r=a(96061),l=a(22169);let d=(0,r.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=i.forwardRef((e,s)=>{let{className:a,...i}=e;return(0,t.jsx)(n.f,{ref:s,className:(0,l.cn)(d(),a),...i})});c.displayName=n.f.displayName},36743:function(e,s,a){"use strict";a.d(s,{f:function(){return l}});var t=a(2265),i=a(9381),n=a(57437),r=t.forwardRef((e,s)=>(0,n.jsx)(i.WV.label,{...e,ref:s,onMouseDown:s=>{s.target.closest("button, input, select, textarea")||(e.onMouseDown?.(s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));r.displayName="Label";var l=r}},function(e){e.O(0,[432,749,250,567,674,529,63,559,971,938,744],function(){return e(e.s=51548)}),_N_E=e.O()}]);