(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[809],{46836:function(e,s,t){Promise.resolve().then(t.bind(t,43499))},43499:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return eg}});var n=t(57437),a=t(2265),r=t(15671),i=t(575),l=t(3549),c=t(18641),o=t(33277),d=t(29069),u=t(1326),m=t(6141),h=t(13008),x=t(82104),j=t(97332),p=t(36357),f=t(94900),g=t(74056),N=t(72894);function v(e){var s,t;let{sql:l,connectionId:c,onExecutionComplete:v}=e,[y,w]=(0,a.useState)(!1),[b,C]=(0,a.useState)(null),[S,k]=(0,a.useState)(null),[Z,E]=(0,a.useState)(!1),I=(0,u.f)("user_demo_123"),T=async()=>{if(c&&l.trim()){w(!0),C(null);try{let s=await fetch("/api/queries/execute",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({connectionId:c,sql:l.trim(),maxRows:1e3,timeout:3e4,saveToHistory:!0})}),t=await s.json();if(C(t),null==v||v(t),c){var e;I.trackQueryExecution({connectionId:c,sql:l.trim(),result:t,executionTime:(null===(e=t.metadata)||void 0===e?void 0:e.executionTime)||0})}}catch(s){let e={success:!1,error:s instanceof Error?s.message:"Unknown error occurred",metadata:{connectionId:c,executedAt:new Date().toISOString(),executionTime:0,rowsReturned:0,queryHash:""}};C(e),null==v||v(e),c&&I.trackQueryExecution({connectionId:c,sql:l.trim(),result:e,executionTime:0})}finally{w(!1)}}},O=async()=>{if(c&&l.trim()){E(!0),k(null),I.trackFeatureUsage("query-validation","validate_query");try{let e=await fetch("/api/queries/execute/validate?connectionId=".concat(c,"&sql=").concat(encodeURIComponent(l.trim())));if(e.ok){let s=await e.json();k(s.validation)}}catch(e){console.error("Failed to validate query:",e)}finally{E(!1)}}},q=c&&l.trim()&&!y;return(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)(r.Zb,{children:[(0,n.jsx)(r.Ol,{children:(0,n.jsxs)(r.ll,{className:"flex items-center gap-2",children:[y?(0,n.jsx)(m.Z,{className:"h-4 w-4 animate-spin"}):(null==b?void 0:b.success)?(0,n.jsx)(h.Z,{className:"h-4 w-4 text-green-500"}):b&&!b.success?(0,n.jsx)(x.Z,{className:"h-4 w-4 text-red-500"}):(0,n.jsx)(j.Z,{className:"h-4 w-4"}),"Query Execution",b&&(0,n.jsx)(o.C,{variant:b.success?"default":"destructive",children:b.success?"Success":"Failed"})]})}),(0,n.jsxs)(r.aY,{className:"space-y-4",children:[(0,n.jsxs)("div",{className:"flex gap-2",children:[(0,n.jsx)(i.z,{onClick:T,disabled:!q,className:"flex items-center gap-2",children:y?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(p.Z,{className:"h-4 w-4"}),"Executing..."]}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(f.Z,{className:"h-4 w-4"}),"Execute Query"]})}),(0,n.jsx)(i.z,{variant:"outline",onClick:O,disabled:!c||!l.trim()||Z,className:"flex items-center gap-2",children:Z?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(m.Z,{className:"h-4 w-4 animate-spin"}),"Validating..."]}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(h.Z,{className:"h-4 w-4"}),"Validate"]})})]}),!c&&(0,n.jsxs)(d.bZ,{children:[(0,n.jsx)(g.Z,{className:"h-4 w-4"}),(0,n.jsx)(d.X,{children:"Please select a database connection to execute queries."})]})]})]}),S&&(0,n.jsxs)(r.Zb,{children:[(0,n.jsx)(r.Ol,{children:(0,n.jsxs)(r.ll,{className:"flex items-center gap-2",children:[(0,n.jsx)(h.Z,{className:"h-4 w-4"}),"Query Validation",(t=S.estimatedComplexity,(0,n.jsxs)(o.C,{variant:{LOW:"default",MEDIUM:"secondary",HIGH:"destructive"}[t]||"default",children:[t," Complexity"]}))]})}),(0,n.jsxs)(r.aY,{className:"space-y-3",children:[(0,n.jsxs)("div",{className:"flex gap-2 flex-wrap",children:[(0,n.jsx)(o.C,{variant:"outline",children:S.queryType}),S.isReadOnly&&(0,n.jsx)(o.C,{variant:"secondary",children:"Read-Only"})]}),S.errors.length>0&&(0,n.jsxs)(d.bZ,{className:"border-red-200 bg-red-50",children:[(0,n.jsx)(x.Z,{className:"h-4 w-4 text-red-500"}),(0,n.jsxs)(d.X,{children:[(0,n.jsx)("div",{className:"font-medium text-red-700 mb-1",children:"Validation Errors:"}),(0,n.jsx)("ul",{className:"list-disc list-inside text-red-600 text-sm",children:S.errors.map((e,s)=>(0,n.jsx)("li",{children:e},s))})]})]}),S.warnings.length>0&&(0,n.jsxs)(d.bZ,{className:"border-yellow-200 bg-yellow-50",children:[(0,n.jsx)(N.Z,{className:"h-4 w-4 text-yellow-500"}),(0,n.jsxs)(d.X,{children:[(0,n.jsx)("div",{className:"font-medium text-yellow-700 mb-1",children:"Warnings:"}),(0,n.jsx)("ul",{className:"list-disc list-inside text-yellow-600 text-sm",children:S.warnings.map((e,s)=>(0,n.jsx)("li",{children:e},s))})]})]}),S.isValid&&0===S.errors.length&&(0,n.jsxs)(d.bZ,{className:"border-green-200 bg-green-50",children:[(0,n.jsx)(h.Z,{className:"h-4 w-4 text-green-500"}),(0,n.jsx)(d.X,{className:"text-green-700",children:"Query validation passed. Ready to execute."})]})]})]}),b&&(0,n.jsxs)(r.Zb,{children:[(0,n.jsx)(r.Ol,{children:(0,n.jsxs)(r.ll,{className:"flex items-center gap-2",children:[b.success?(0,n.jsx)(h.Z,{className:"h-4 w-4 text-green-500"}):(0,n.jsx)(x.Z,{className:"h-4 w-4 text-red-500"}),"Execution Results"]})}),(0,n.jsxs)(r.aY,{className:"space-y-4",children:[(0,n.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("div",{className:"text-muted-foreground",children:"Execution Time"}),(0,n.jsxs)("div",{className:"font-medium",children:[b.metadata.executionTime,"ms"]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("div",{className:"text-muted-foreground",children:"Rows Returned"}),(0,n.jsx)("div",{className:"font-medium",children:b.metadata.rowsReturned})]}),(null===(s=b.data)||void 0===s?void 0:s.affectedRows)!==void 0&&(0,n.jsxs)("div",{children:[(0,n.jsx)("div",{className:"text-muted-foreground",children:"Rows Affected"}),(0,n.jsx)("div",{className:"font-medium",children:b.data.affectedRows})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("div",{className:"text-muted-foreground",children:"Executed At"}),(0,n.jsx)("div",{className:"font-medium",children:new Date(b.metadata.executedAt).toLocaleTimeString()})]})]}),b.error&&(0,n.jsxs)(d.bZ,{className:"border-red-200 bg-red-50",children:[(0,n.jsx)(x.Z,{className:"h-4 w-4 text-red-500"}),(0,n.jsxs)(d.X,{children:[(0,n.jsx)("div",{className:"font-medium text-red-700 mb-1",children:"Execution Error:"}),(0,n.jsx)("div",{className:"text-red-600 text-sm",children:b.error})]})]}),b.warnings&&b.warnings.length>0&&(0,n.jsxs)(d.bZ,{className:"border-yellow-200 bg-yellow-50",children:[(0,n.jsx)(N.Z,{className:"h-4 w-4 text-yellow-500"}),(0,n.jsxs)(d.X,{children:[(0,n.jsx)("div",{className:"font-medium text-yellow-700 mb-1",children:"Warnings:"}),(0,n.jsx)("ul",{className:"list-disc list-inside text-yellow-600 text-sm",children:b.warnings.map((e,s)=>(0,n.jsx)("li",{children:e},s))})]})]}),b.success&&b.data&&(0,n.jsxs)(d.bZ,{className:"border-green-200 bg-green-50",children:[(0,n.jsx)(h.Z,{className:"h-4 w-4 text-green-500"}),(0,n.jsxs)(d.X,{className:"text-green-700",children:["Query executed successfully!",b.data.rowCount>0&&(0,n.jsxs)("span",{children:[" Returned ",b.data.rowCount," rows."]}),void 0!==b.data.affectedRows&&b.data.affectedRows>0&&(0,n.jsxs)("span",{children:[" Affected ",b.data.affectedRows," rows."]})]})]})]})]})]})}var y=t(22782),w=t(5887),b=t(71813),C=t(35817),S=t(76637),k=t(41827),Z=t(81291),E=t(17158);function I(e){let{data:s,metadata:t}=e,[l,c]=(0,a.useState)(""),[d,u]=(0,a.useState)(1),[m]=(0,a.useState)(50),h=(0,a.useMemo)(()=>l?s.rows.filter(e=>Object.values(e).some(e=>String(e).toLowerCase().includes(l.toLowerCase()))):s.rows,[s.rows,l]),x=(0,a.useMemo)(()=>{let e=(d-1)*m;return h.slice(e,e+m)},[h,d,m]),p=Math.ceil(h.length/m),f=(e,s)=>{if(null==e)return(0,n.jsx)("span",{className:"text-muted-foreground italic",children:"NULL"});if(s.includes("json")||s.includes("jsonb"))try{return(0,n.jsx)("pre",{className:"text-xs",children:JSON.stringify(JSON.parse(e),null,2)})}catch(s){return String(e)}if(s.includes("bool"))return(0,n.jsx)(o.C,{variant:e?"default":"secondary",children:e?"TRUE":"FALSE"});if(s.includes("date")||s.includes("time"))try{return new Date(e).toLocaleString()}catch(s){return String(e)}let t=String(e);return t.length>100?(0,n.jsxs)("span",{title:t,children:[t.substring(0,100),"..."]}):t},g=e=>e.includes("int")||e.includes("number")||e.includes("decimal")?"\uD83D\uDD22":e.includes("text")||e.includes("varchar")||e.includes("char")?"\uD83D\uDCDD":e.includes("date")||e.includes("time")?"\uD83D\uDCC5":e.includes("bool")?"✅":e.includes("json")?"\uD83D\uDCCB":"\uD83D\uDCC4";return s.rows.length?(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)(r.Zb,{children:[(0,n.jsx)(r.Ol,{children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)(r.ll,{className:"flex items-center gap-2",children:[(0,n.jsx)(b.Z,{className:"h-5 w-5"}),"Query Results",(0,n.jsxs)(o.C,{variant:"outline",children:[s.rowCount," rows"]})]}),(0,n.jsxs)("div",{className:"flex gap-2",children:[(0,n.jsxs)(i.z,{variant:"outline",size:"sm",onClick:()=>{if(!s.rows.length)return;let e=[s.fields.map(e=>e.name).join(","),...s.rows.map(e=>s.fields.map(s=>{let t=e[s.name];return"string"==typeof t&&(t.includes(",")||t.includes('"'))?'"'.concat(t.replace(/"/g,'""'),'"'):null!=t?t:""}).join(","))].join("\n"),n=new Blob([e],{type:"text/csv;charset=utf-8;"}),a=document.createElement("a"),r=URL.createObjectURL(n);a.setAttribute("href",r),a.setAttribute("download","query_results_".concat(t.queryHash,".csv")),a.style.visibility="hidden",document.body.appendChild(a),a.click(),document.body.removeChild(a)},className:"flex items-center gap-2",children:[(0,n.jsx)(C.Z,{className:"h-4 w-4"}),"CSV"]}),(0,n.jsxs)(i.z,{variant:"outline",size:"sm",onClick:()=>{if(!s.rows.length)return;let e=JSON.stringify(s.rows,null,2),n=new Blob([e],{type:"application/json;charset=utf-8;"}),a=document.createElement("a"),r=URL.createObjectURL(n);a.setAttribute("href",r),a.setAttribute("download","query_results_".concat(t.queryHash,".json")),a.style.visibility="hidden",document.body.appendChild(a),a.click(),document.body.removeChild(a)},className:"flex items-center gap-2",children:[(0,n.jsx)(S.Z,{className:"h-4 w-4"}),"JSON"]})]})]})}),(0,n.jsx)(r.aY,{children:(0,n.jsxs)("div",{className:"flex items-center gap-4 text-sm text-muted-foreground",children:[(0,n.jsxs)("span",{children:["Execution time: ",t.executionTime,"ms"]}),(0,n.jsx)("span",{children:"•"}),(0,n.jsxs)("span",{children:["Rows returned: ",t.rowsReturned]}),(0,n.jsx)("span",{children:"•"}),(0,n.jsxs)("span",{children:["Executed at: ",new Date(t.executedAt).toLocaleString()]})]})})]}),(0,n.jsx)(r.Zb,{children:(0,n.jsx)(r.aY,{className:"pt-6",children:(0,n.jsxs)("div",{className:"flex items-center gap-4",children:[(0,n.jsxs)("div",{className:"relative flex-1",children:[(0,n.jsx)(k.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,n.jsx)(y.I,{placeholder:"Search in results...",value:l,onChange:e=>{c(e.target.value),u(1)},className:"pl-10"})]}),(0,n.jsxs)("div",{className:"text-sm text-muted-foreground",children:[h.length," of ",s.rows.length," rows"]})]})})}),(0,n.jsx)(r.Zb,{children:(0,n.jsx)(r.aY,{className:"p-0",children:(0,n.jsx)("div",{className:"overflow-x-auto",children:(0,n.jsxs)(w.iA,{children:[(0,n.jsx)(w.xD,{children:(0,n.jsx)(w.SC,{children:s.fields.map(e=>(0,n.jsx)(w.ss,{className:"whitespace-nowrap",children:(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)("span",{children:g(e.type)}),(0,n.jsxs)("div",{children:[(0,n.jsx)("div",{className:"font-medium",children:e.name}),(0,n.jsxs)("div",{className:"text-xs text-muted-foreground",children:[e.type,!e.nullable&&(0,n.jsx)("span",{className:"ml-1",children:"NOT NULL"})]})]})]})},e.name))})}),(0,n.jsx)(w.RM,{children:x.map((e,t)=>(0,n.jsx)(w.SC,{children:s.fields.map(s=>(0,n.jsx)(w.pj,{className:"max-w-xs",children:f(e[s.name],s.type)},s.name))},t))})]})})})}),p>1&&(0,n.jsx)(r.Zb,{children:(0,n.jsx)(r.aY,{className:"pt-6",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{className:"text-sm text-muted-foreground",children:["Page ",d," of ",p]}),(0,n.jsxs)("div",{className:"flex gap-2",children:[(0,n.jsxs)(i.z,{variant:"outline",size:"sm",onClick:()=>u(e=>Math.max(1,e-1)),disabled:1===d,className:"flex items-center gap-2",children:[(0,n.jsx)(Z.Z,{className:"h-4 w-4"}),"Previous"]}),(0,n.jsxs)(i.z,{variant:"outline",size:"sm",onClick:()=>u(e=>Math.min(p,e+1)),disabled:d===p,className:"flex items-center gap-2",children:["Next",(0,n.jsx)(E.Z,{className:"h-4 w-4"})]})]})]})})})]}):(0,n.jsx)(r.Zb,{children:(0,n.jsx)(r.aY,{className:"flex items-center justify-center p-8",children:(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)(j.Z,{className:"h-8 w-8 text-muted-foreground mx-auto mb-2"}),(0,n.jsx)("p",{className:"text-muted-foreground",children:"No data returned"}),(0,n.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:"Query executed successfully but returned no rows"})]})})})}var T=t(12647),O=t(66062),q=t(62442),F=t(22169);let L=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,n.jsx)(O.fC,{ref:s,className:(0,F.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",t),...a,children:(0,n.jsx)(O.z$,{className:(0,F.cn)("flex items-center justify-center text-current"),children:(0,n.jsx)(q.Z,{className:"h-4 w-4"})})})});L.displayName=O.fC.displayName;var _=t(86264),R=t(64280);let U={async getUser(e){let s=await fetch("/api/users?id=".concat(e));if(!s.ok)throw Error("Failed to fetch user");return s.json()},async createUser(e){let s=await fetch("/api/users",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!s.ok)throw Error("Failed to create user");return s.json()},async updateUser(e,s){let t=await fetch("/api/users?id=".concat(e),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)});if(!t.ok)throw Error("Failed to update user");return t.json()},async getUserConnections(e){let s=await fetch("/api/connections?userId=".concat(e));if(!s.ok)throw Error("Failed to fetch connections");return s.json()},async createConnection(e){let s=await fetch("/api/connections",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!s.ok)throw Error("Failed to create connection");return s.json()},async updateConnection(e,s){let t=await fetch("/api/connections?id=".concat(e),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)});if(!t.ok)throw Error("Failed to update connection");return t.json()},async deleteConnection(e){if(!(await fetch("/api/connections?id=".concat(e),{method:"DELETE"})).ok)throw Error("Failed to delete connection")},async getUserSessions(e){let s=await fetch("/api/queries?type=sessions&userId=".concat(e));if(!s.ok)throw Error("Failed to fetch sessions");return s.json()},async getSession(e){let s=await fetch("/api/queries?sessionId=".concat(e));if(!s.ok)throw Error("Failed to fetch session");return s.json()},async createSession(e){let s=await fetch("/api/queries?type=session",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!s.ok)throw Error("Failed to create session");return s.json()},async createQuery(e){let s=await fetch("/api/queries?type=query",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!s.ok)throw Error("Failed to create query");return s.json()},async getUserQueryHistory(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:20,t=await fetch("/api/queries?type=history&userId=".concat(e,"&limit=").concat(s));if(!t.ok)throw Error("Failed to fetch query history");return t.json()},async testConnection(e,s){let t=await fetch("/api/connections/test",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e?{connectionId:e}:s)});if(!t.ok)throw Error((await t.json()).error||"Failed to test connection");return t.json()},async getConnectionSchema(e){let s=arguments.length>1&&void 0!==arguments[1]&&arguments[1],t=await fetch("/api/connections/schema?connectionId=".concat(e,"&refresh=").concat(s));if(!t.ok)throw Error((await t.json()).error||"Failed to fetch schema");return t.json()},async refreshConnectionSchema(e){let s=await fetch("/api/connections/schema",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({connectionId:e})});if(!s.ok)throw Error((await s.json()).error||"Failed to refresh schema");return s.json()},async executeQuery(e){let s=await fetch("/api/queries/execute",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!s.ok)throw Error((await s.json()).error||"Failed to execute query");return s.json()},async validateQuery(e,s){let t=await fetch("/api/queries/execute/validate?connectionId=".concat(e,"&sql=").concat(encodeURIComponent(s)));if(!t.ok)throw Error((await t.json()).error||"Failed to validate query");return t.json()},async updateQueryFeedback(e,s){let t=await fetch("/api/queries?type=query&id=".concat(e),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({userFeedback:s})});if(!t.ok)throw Error("Failed to update query feedback");return t.json()}},Q=()=>U;function D(e){let{connectionId:s,onConnectionTested:t}=e,[l,u]=(0,a.useState)(!1),[m,p]=(0,a.useState)(null),[f,g]=(0,a.useState)({databaseType:"POSTGRESQL",connectionString:"",host:"",port:5432,database:"",username:"",password:"",ssl:!1}),N=Q(),v=async()=>{u(!0),p(null);try{let e;e=s?await N.testConnection(s):await N.testConnection(void 0,f),p(e),null==t||t(e)}catch(s){let e={success:!1,connected:!1,error:s instanceof Error?s.message:"Unknown error",timestamp:new Date().toISOString()};p(e),null==t||t(e)}finally{u(!1)}},w=(e,s)=>{g(t=>({...t,[e]:s})),p(null)};return(0,n.jsxs)(r.Zb,{children:[(0,n.jsx)(r.Ol,{children:(0,n.jsxs)(r.ll,{className:"flex items-center gap-2",children:[l?(0,n.jsx)(_.Z,{className:"h-4 w-4 animate-spin"}):(null==m?void 0:m.connected)?(0,n.jsx)(h.Z,{className:"h-4 w-4 text-green-500"}):m&&!m.connected?(0,n.jsx)(x.Z,{className:"h-4 w-4 text-red-500"}):(0,n.jsx)(j.Z,{className:"h-4 w-4"}),s?"Test Connection":"Test Database Connection",l?(0,n.jsx)(o.C,{variant:"secondary",children:"Testing..."}):(null==m?void 0:m.connected)?(0,n.jsx)(o.C,{variant:"default",className:"bg-green-100 text-green-800",children:"Connected"}):m&&!m.connected?(0,n.jsx)(o.C,{variant:"destructive",children:"Failed"}):null]})}),(0,n.jsxs)(r.aY,{className:"space-y-4",children:[!s&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)(T._,{htmlFor:"databaseType",children:"Database Type"}),(0,n.jsxs)(c.Ph,{value:f.databaseType,onValueChange:e=>w("databaseType",e),children:[(0,n.jsx)(c.i4,{children:(0,n.jsx)(c.ki,{})}),(0,n.jsxs)(c.Bw,{children:[(0,n.jsx)(c.Ql,{value:"POSTGRESQL",children:"PostgreSQL"}),(0,n.jsx)(c.Ql,{value:"MYSQL",children:"MySQL"})]})]})]}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)(T._,{htmlFor:"connectionString",children:"Connection String (Optional)"}),(0,n.jsx)(y.I,{id:"connectionString",type:"text",placeholder:"postgresql://user:password@host:port/database",value:f.connectionString,onChange:e=>w("connectionString",e.target.value)}),(0,n.jsx)("p",{className:"text-xs text-muted-foreground",children:"Leave empty to use individual connection fields below"})]}),!f.connectionString&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)(T._,{htmlFor:"host",children:"Host"}),(0,n.jsx)(y.I,{id:"host",type:"text",placeholder:"localhost",value:f.host,onChange:e=>w("host",e.target.value)})]}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)(T._,{htmlFor:"port",children:"Port"}),(0,n.jsx)(y.I,{id:"port",type:"number",placeholder:"MYSQL"===f.databaseType?"3306":"5432",value:f.port,onChange:e=>w("port",parseInt(e.target.value)||5432)})]})]}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)(T._,{htmlFor:"database",children:"Database Name"}),(0,n.jsx)(y.I,{id:"database",type:"text",placeholder:"my_database",value:f.database,onChange:e=>w("database",e.target.value)})]}),(0,n.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)(T._,{htmlFor:"username",children:"Username"}),(0,n.jsx)(y.I,{id:"username",type:"text",placeholder:"username",value:f.username,onChange:e=>w("username",e.target.value)})]}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)(T._,{htmlFor:"password",children:"Password"}),(0,n.jsx)(y.I,{id:"password",type:"password",placeholder:"password",value:f.password,onChange:e=>w("password",e.target.value)})]})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)(L,{id:"ssl",checked:f.ssl,onCheckedChange:e=>w("ssl",e)}),(0,n.jsx)(T._,{htmlFor:"ssl",children:"Use SSL"})]})]})]}),(0,n.jsx)(i.z,{onClick:v,disabled:l||!s&&!f.connectionString&&(!f.host||!f.database||!f.username),className:"w-full",children:l?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(_.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Testing Connection..."]}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(R.Z,{className:"mr-2 h-4 w-4"}),"Test Connection"]})}),m&&(0,n.jsx)(d.bZ,{className:m.connected?"border-green-200 bg-green-50":"border-red-200 bg-red-50",children:(0,n.jsxs)(d.X,{children:[m.connected?(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)(h.Z,{className:"h-4 w-4 text-green-500"}),(0,n.jsx)("span",{className:"text-green-700",children:"Connection successful!"})]}):(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)(x.Z,{className:"h-4 w-4 text-red-500"}),(0,n.jsx)("span",{className:"text-red-700",children:"Connection failed"})]}),m.error&&(0,n.jsx)("p",{className:"text-sm text-red-600",children:m.error})]}),(0,n.jsxs)("p",{className:"text-xs text-muted-foreground mt-2",children:["Tested at ",new Date(m.timestamp).toLocaleString()]})]})})]})]})}var P=t(90136),z=t(50774),M=t(57706),A=t(23223),Y=t(83523),J=t(4689),H=t(99670);function X(e){let{connectionId:s}=e,[t,l]=(0,a.useState)(null),[c,u]=(0,a.useState)(!1),[m,h]=(0,a.useState)(null),[x,p]=(0,a.useState)(null),[f,g]=(0,a.useState)(!1),[N,v]=(0,a.useState)(new Set),[y,w]=(0,a.useState)(new Set),b=Q(),C=async function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];u(!0),h(null);try{let t=await b.getConnectionSchema(s,e);l(t.schema),p(t.lastUpdated),g(t.cached)}catch(e){h(e instanceof Error?e.message:"Failed to load schema")}finally{u(!1)}},S=async()=>{u(!0),h(null);try{let e=await b.refreshConnectionSchema(s);l(e.schema),p(e.lastUpdated),g(!1)}catch(e){h(e instanceof Error?e.message:"Failed to refresh schema")}finally{u(!1)}};(0,a.useEffect)(()=>{C()},[s]);let k=e=>{let s=new Set(N);s.has(e)?s.delete(e):s.add(e),v(s)},Z=e=>{let s=new Set(y);s.has(e)?s.delete(e):s.add(e),w(s)},I=e=>e.isPrimaryKey?(0,n.jsx)(z.Z,{className:"h-3 w-3 text-yellow-500"}):e.isForeignKey?(0,n.jsx)(z.Z,{className:"h-3 w-3 text-blue-500"}):(0,n.jsx)(M.Z,{className:"h-3 w-3 text-gray-400"}),T=e=>{let s=[];return e.isPrimaryKey&&s.push((0,n.jsx)(o.C,{variant:"secondary",className:"text-xs",children:"PK"},"pk")),e.isForeignKey&&s.push((0,n.jsx)(o.C,{variant:"outline",className:"text-xs",children:"FK"},"fk")),e.nullable||s.push((0,n.jsx)(o.C,{variant:"outline",className:"text-xs",children:"NOT NULL"},"nn")),s};return c&&!t?(0,n.jsx)(r.Zb,{children:(0,n.jsx)(r.aY,{className:"flex items-center justify-center p-8",children:(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)(_.Z,{className:"h-4 w-4 animate-spin"}),(0,n.jsx)("span",{children:"Loading database schema..."})]})})}):m?(0,n.jsx)(r.Zb,{children:(0,n.jsxs)(r.aY,{className:"p-6",children:[(0,n.jsx)(d.bZ,{className:"border-red-200 bg-red-50",children:(0,n.jsx)(d.X,{className:"text-red-700",children:m})}),(0,n.jsxs)(i.z,{onClick:()=>C(),className:"mt-4",children:[(0,n.jsx)(R.Z,{className:"mr-2 h-4 w-4"}),"Retry"]})]})}):t?(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsx)(r.Zb,{children:(0,n.jsxs)(r.Ol,{children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)(r.ll,{className:"flex items-center gap-2",children:[(0,n.jsx)(j.Z,{className:"h-5 w-5"}),"Database Schema",f&&(0,n.jsx)(o.C,{variant:"secondary",children:"Cached"})]}),(0,n.jsxs)(i.z,{onClick:S,disabled:c,variant:"outline",size:"sm",children:[c?(0,n.jsx)(_.Z,{className:"mr-2 h-4 w-4 animate-spin"}):(0,n.jsx)(R.Z,{className:"mr-2 h-4 w-4"}),"Refresh"]})]}),x&&(0,n.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Last updated: ",new Date(x).toLocaleString()]})]})}),t.tables&&t.tables.length>0&&(0,n.jsxs)(r.Zb,{children:[(0,n.jsx)(r.Ol,{children:(0,n.jsxs)(r.ll,{className:"flex items-center gap-2",children:[(0,n.jsx)(A.Z,{className:"h-4 w-4"}),"Tables (",t.tables.length,")"]})}),(0,n.jsx)(r.aY,{className:"space-y-2",children:t.tables.map(e=>(0,n.jsxs)(P.zF,{open:N.has(e.name),onOpenChange:()=>k(e.name),children:[(0,n.jsxs)(P.wy,{className:"flex items-center gap-2 w-full p-2 hover:bg-muted rounded",children:[N.has(e.name)?(0,n.jsx)(Y.Z,{className:"h-4 w-4"}):(0,n.jsx)(E.Z,{className:"h-4 w-4"}),(0,n.jsx)(A.Z,{className:"h-4 w-4"}),(0,n.jsx)("span",{className:"font-medium",children:e.name}),(0,n.jsxs)(o.C,{variant:"outline",className:"text-xs",children:[e.columns.length," columns"]}),e.indexes.length>0&&(0,n.jsxs)(o.C,{variant:"outline",className:"text-xs",children:[e.indexes.length," indexes"]})]}),(0,n.jsxs)(P.Fw,{className:"ml-6 mt-2 space-y-2",children:[(0,n.jsxs)("div",{className:"space-y-1",children:[(0,n.jsx)("h4",{className:"text-sm font-medium",children:"Columns"}),e.columns.map(e=>(0,n.jsxs)("div",{className:"flex items-center gap-2 p-2 bg-muted/50 rounded text-sm",children:[I(e),(0,n.jsx)("span",{className:"font-mono",children:e.name}),(0,n.jsx)(o.C,{variant:"outline",className:"text-xs",children:e.type}),(0,n.jsx)("div",{className:"flex gap-1",children:T(e)}),e.isForeignKey&&e.referencedTable&&(0,n.jsxs)("span",{className:"text-xs text-muted-foreground",children:["→ ",e.referencedTable,".",e.referencedColumn]})]},e.name))]}),e.indexes.length>0&&(0,n.jsxs)("div",{className:"space-y-1",children:[(0,n.jsx)("h4",{className:"text-sm font-medium",children:"Indexes"}),e.indexes.map(e=>(0,n.jsxs)("div",{className:"flex items-center gap-2 p-2 bg-muted/50 rounded text-sm",children:[(0,n.jsx)(J.Z,{className:"h-3 w-3 text-gray-400"}),(0,n.jsx)("span",{className:"font-mono",children:e.name}),(0,n.jsxs)("span",{className:"text-xs text-muted-foreground",children:["(",e.columns.join(", "),")"]}),e.isUnique&&(0,n.jsx)(o.C,{variant:"outline",className:"text-xs",children:"UNIQUE"}),e.isPrimary&&(0,n.jsx)(o.C,{variant:"secondary",className:"text-xs",children:"PRIMARY"})]},e.name))]})]})]},"".concat(e.schema,".").concat(e.name)))})]}),t.views&&t.views.length>0&&(0,n.jsxs)(r.Zb,{children:[(0,n.jsx)(r.Ol,{children:(0,n.jsxs)(r.ll,{className:"flex items-center gap-2",children:[(0,n.jsx)(H.Z,{className:"h-4 w-4"}),"Views (",t.views.length,")"]})}),(0,n.jsx)(r.aY,{className:"space-y-2",children:t.views.map(e=>(0,n.jsxs)(P.zF,{open:y.has(e.name),onOpenChange:()=>Z(e.name),children:[(0,n.jsxs)(P.wy,{className:"flex items-center gap-2 w-full p-2 hover:bg-muted rounded",children:[y.has(e.name)?(0,n.jsx)(Y.Z,{className:"h-4 w-4"}):(0,n.jsx)(E.Z,{className:"h-4 w-4"}),(0,n.jsx)(H.Z,{className:"h-4 w-4"}),(0,n.jsx)("span",{className:"font-medium",children:e.name})]}),(0,n.jsx)(P.Fw,{className:"ml-6 mt-2",children:(0,n.jsx)("div",{className:"p-3 bg-muted/50 rounded",children:(0,n.jsx)("pre",{className:"text-xs overflow-x-auto whitespace-pre-wrap",children:e.definition})})})]},"".concat(e.schema,".").concat(e.name)))})]}),(!t.tables||0===t.tables.length)&&(!t.views||0===t.views.length)&&(0,n.jsx)(r.Zb,{children:(0,n.jsx)(r.aY,{className:"flex items-center justify-center p-8",children:(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)(j.Z,{className:"h-8 w-8 text-muted-foreground mx-auto mb-2"}),(0,n.jsx)("p",{className:"text-muted-foreground",children:"No tables or views found in this database"})]})})})]}):(0,n.jsx)(r.Zb,{children:(0,n.jsx)(r.aY,{className:"flex items-center justify-center p-8",children:(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)(j.Z,{className:"h-8 w-8 text-muted-foreground mx-auto mb-2"}),(0,n.jsx)("p",{className:"text-muted-foreground",children:"No schema data available"}),(0,n.jsxs)(i.z,{onClick:()=>C(!0),className:"mt-4",children:[(0,n.jsx)(R.Z,{className:"mr-2 h-4 w-4"}),"Load Schema"]})]})})})}var V=t(90261),B=t(18784),K=t(25750),G=t(56224),W=t(43480),$=t(51274),ee=t(74527),es=t(34337);class et{connect(){this.socket=(0,es.io)({path:"/api/socket",autoConnect:!1}),this.setupEventHandlers()}setupEventHandlers(){this.socket&&(this.socket.on("connect",()=>{console.log("Connected to collaboration server"),this.isConnected=!0}),this.socket.on("disconnect",()=>{console.log("Disconnected from collaboration server"),this.isConnected=!1,this.currentSessionId=null,this.currentUser=null}),Object.keys(this.eventHandlers).forEach(e=>{this.socket.on(e,s=>{let t=this.eventHandlers[e];t&&t(s)})}))}async authenticate(e){return new Promise(s=>{if(!this.socket){s(!1);return}this.socket.connect(),this.socket.once("authenticated",e=>{e.success&&e.user?(this.currentUser=e.user,s(!0)):(console.error("Authentication failed:",e.error),s(!1))}),this.socket.emit("authenticate",e)})}joinSession(e,s,t){if(!this.socket||!this.isConnected){console.error("Not connected to collaboration server");return}this.currentSessionId=e,this.socket.emit("join_session",{sessionId:e,connectionId:s,sessionName:t})}leaveSession(){this.socket&&this.currentSessionId&&(this.socket.emit("leave_session",this.currentSessionId),this.currentSessionId=null)}updateSqlContent(e,s){this.socket&&this.currentSessionId&&this.socket.emit("sql_change",{sessionId:this.currentSessionId,content:e,operation:s})}updateCursorPosition(e){this.socket&&this.currentSessionId&&this.socket.emit("cursor_update",{sessionId:this.currentSessionId,position:e})}sendChatMessage(e){this.socket&&this.currentSessionId&&this.socket.emit("chat_message",{sessionId:this.currentSessionId,message:e})}notifyQueryExecutionStart(e){this.socket&&this.currentSessionId&&this.socket.emit("query_execution_start",{sessionId:this.currentSessionId,sql:e})}notifyQueryExecutionComplete(e){this.socket&&this.currentSessionId&&this.socket.emit("query_execution_complete",{sessionId:this.currentSessionId,result:e})}disconnect(){this.socket&&(this.socket.disconnect(),this.socket=null),this.isConnected=!1,this.currentSessionId=null,this.currentUser=null}on(e,s){this.eventHandlers[e]=s,this.socket&&this.socket.on(e,s)}off(e){delete this.eventHandlers[e],this.socket&&this.socket.off(e)}get connected(){return this.isConnected}get sessionId(){return this.currentSessionId}get user(){return this.currentUser}constructor(){this.socket=null,this.eventHandlers={},this.isConnected=!1,this.currentSessionId=null,this.currentUser=null,this.connect()}}let en=null;function ea(){var e;let s=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=(0,a.useRef)(null),[n,r]=(0,a.useState)({isConnected:!1,isAuthenticated:!1,currentUser:null,participants:[],cursors:new Map,chatMessages:[],sqlContent:"",isInSession:!1});(0,a.useEffect)(()=>{t.current||(t.current=(en||(en=new et),en));let e=t.current;e.on("authenticated",e=>{r(s=>({...s,isAuthenticated:e.success,currentUser:e.user||null}))}),e.on("session_joined",e=>{r(s=>({...s,isInSession:!0,participants:e.participants,currentUser:e.user}))}),e.on("user_joined",e=>{r(s=>({...s,participants:e.participants}))}),e.on("user_left",e=>{r(s=>{let t=new Map(s.cursors);return t.delete(e.userId),{...s,participants:e.participants,cursors:t}})}),e.on("sql_sync",e=>{r(s=>({...s,sqlContent:e.content}))}),e.on("sql_change",e=>{r(s=>({...s,sqlContent:e.content}))}),e.on("cursor_update",e=>{r(s=>{let t=new Map(s.cursors);return t.set(e.user.id,{...e.position,user:e.user}),{...s,cursors:t}})}),e.on("chat_message",e=>{r(s=>({...s,chatMessages:[...s.chatMessages,e]}))}),e.on("query_execution_start",e=>{console.log("".concat(e.userName," started executing a query"))}),e.on("query_execution_complete",e=>{console.log("".concat(e.userName," completed query execution"))}),e.on("error",e=>{console.error("Collaboration error:",e.message)});let s=setInterval(()=>{r(s=>({...s,isConnected:e.connected}))},1e3);return()=>{clearInterval(s)}},[]),(0,a.useEffect)(()=>{s.autoConnect&&t.current&&!n.isAuthenticated&&i()},[s.autoConnect]),(0,a.useEffect)(()=>{s.sessionId&&s.connectionId&&n.isAuthenticated&&!n.isInSession&&l(s.sessionId,s.connectionId,s.sessionName)},[s.sessionId,s.connectionId,n.isAuthenticated,n.isInSession]);let i=(0,a.useCallback)(async()=>{if(!t.current)return!1;try{return await t.current.authenticate("demo-token")}catch(e){return console.error("Authentication failed:",e),!1}},[]),l=(0,a.useCallback)((e,s,n)=>{t.current&&t.current.joinSession(e,s,n)},[]),c=(0,a.useCallback)(()=>{t.current&&(t.current.leaveSession(),r(e=>({...e,isInSession:!1,participants:[],cursors:new Map,chatMessages:[],sqlContent:""})))},[]),o=(0,a.useCallback)((e,s)=>{t.current&&t.current.updateSqlContent(e,s)},[]),d=(0,a.useCallback)(e=>{t.current&&t.current.updateCursorPosition(e)},[]),u=(0,a.useCallback)(e=>{t.current&&t.current.sendChatMessage(e)},[]),m=(0,a.useCallback)(e=>{t.current&&t.current.notifyQueryExecutionStart(e)},[]),h=(0,a.useCallback)(e=>{t.current&&t.current.notifyQueryExecutionComplete(e)},[]),x=(0,a.useCallback)(()=>{r(e=>({...e,chatMessages:[]}))},[]),j=(0,a.useCallback)(()=>{t.current&&(t.current.disconnect(),r({isConnected:!1,isAuthenticated:!1,currentUser:null,participants:[],cursors:new Map,chatMessages:[],sqlContent:"",isInSession:!1}))},[]);return{...n,authenticate:i,joinSession:l,leaveSession:c,updateSqlContent:o,updateCursorPosition:d,sendChatMessage:u,notifyQueryExecutionStart:m,notifyQueryExecutionComplete:h,clearChatMessages:x,disconnect:j,participantCount:n.participants.length,otherParticipants:n.participants.filter(e=>{var s;return e.id!==(null===(s=n.currentUser)||void 0===s?void 0:s.id)}),canCollaborate:n.isConnected&&n.isAuthenticated&&n.isInSession,sessionId:(null===(e=t.current)||void 0===e?void 0:e.sessionId)||null}}function er(e){let{connectionId:s,onSessionJoined:t,onSessionLeft:l}=e,[c,u]=(0,a.useState)(""),[m,h]=(0,a.useState)(""),[x,j]=(0,a.useState)(!1),[p,f]=(0,a.useState)(""),g=ea({autoConnect:!0});(0,a.useEffect)(()=>{g.isInSession&&g.currentUser?(f("".concat(window.location.origin,"/dashboard/query-editor?session=").concat(g.sessionId,"&connection=").concat(s)),null==t||t(g.sessionId||"")):(f(""),null==l||l())},[g.isInSession,g.sessionId,s,t,l]);let N=async()=>{g.isAuthenticated||await g.authenticate(),j(!0);try{let e="session_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),t=m.trim()||"Collaboration Session";g.joinSession(e,s,t),h("")}catch(e){console.error("Failed to create session:",e)}finally{j(!1)}},v=async()=>{if(c.trim()){g.isAuthenticated||await g.authenticate();try{g.joinSession(c.trim(),s),u("")}catch(e){console.error("Failed to join session:",e)}}},w=async()=>{if(p)try{await navigator.clipboard.writeText(p)}catch(e){console.error("Failed to copy URL:",e)}};return(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)(r.Zb,{children:[(0,n.jsx)(r.Ol,{children:(0,n.jsxs)(r.ll,{className:"flex items-center gap-2",children:[g.isConnected?(0,n.jsx)(V.Z,{className:"h-4 w-4 text-green-500"}):(0,n.jsx)(B.Z,{className:"h-4 w-4 text-red-500"}),"Collaboration Status",(0,n.jsx)(o.C,{variant:g.isConnected?"default":"destructive",children:g.isConnected?g.isAuthenticated?g.isInSession?"In Session":"Connected":"Connecting...":"Disconnected"})]})}),(0,n.jsxs)(r.aY,{children:[!g.isConnected&&(0,n.jsxs)(d.bZ,{children:[(0,n.jsx)(B.Z,{className:"h-4 w-4"}),(0,n.jsx)(d.X,{children:"Not connected to collaboration server. Real-time features are unavailable."})]}),g.isConnected&&!g.isAuthenticated&&(0,n.jsx)(d.bZ,{children:(0,n.jsx)(d.X,{children:"Authenticating with collaboration server..."})})]})]}),g.isInSession&&(0,n.jsxs)(r.Zb,{children:[(0,n.jsx)(r.Ol,{children:(0,n.jsxs)(r.ll,{className:"flex items-center gap-2",children:[(0,n.jsx)(K.Z,{className:"h-5 w-5"}),"Active Session",(0,n.jsxs)(o.C,{variant:"default",children:[g.participantCount," participants"]})]})}),(0,n.jsxs)(r.aY,{className:"space-y-4",children:[(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)("h4",{className:"text-sm font-medium",children:"Participants"}),(0,n.jsx)("div",{className:"space-y-1",children:g.participants.map(e=>{var s;return(0,n.jsxs)("div",{className:"flex items-center gap-2 p-2 bg-muted rounded",children:[(0,n.jsx)("div",{className:"w-3 h-3 rounded-full",style:{backgroundColor:e.color}}),(0,n.jsx)("span",{className:"text-sm",children:e.name}),e.id===(null===(s=g.currentUser)||void 0===s?void 0:s.id)&&(0,n.jsx)(o.C,{variant:"secondary",className:"text-xs",children:"You"})]},e.id)})})]}),p&&(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)("h4",{className:"text-sm font-medium",children:"Share Session"}),(0,n.jsxs)("div",{className:"flex gap-2",children:[(0,n.jsx)(y.I,{value:p,readOnly:!0,className:"text-xs"}),(0,n.jsxs)(i.z,{variant:"outline",size:"sm",onClick:w,className:"flex items-center gap-2",children:[(0,n.jsx)(G.Z,{className:"h-4 w-4"}),"Copy"]})]})]}),(0,n.jsxs)(i.z,{variant:"outline",onClick:()=>{g.leaveSession()},className:"w-full flex items-center gap-2",children:[(0,n.jsx)(W.Z,{className:"h-4 w-4"}),"Leave Session"]})]})]}),g.isAuthenticated&&!g.isInSession&&(0,n.jsxs)(r.Zb,{children:[(0,n.jsx)(r.Ol,{children:(0,n.jsxs)(r.ll,{className:"flex items-center gap-2",children:[(0,n.jsx)($.Z,{className:"h-5 w-5"}),"Start Collaboration"]})}),(0,n.jsxs)(r.aY,{className:"space-y-4",children:[(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)("h4",{className:"text-sm font-medium",children:"Create New Session"}),(0,n.jsxs)("div",{className:"flex gap-2",children:[(0,n.jsx)(y.I,{placeholder:"Session name (optional)",value:m,onChange:e=>h(e.target.value)}),(0,n.jsxs)(i.z,{onClick:N,disabled:x,className:"flex items-center gap-2",children:[(0,n.jsx)(ee.Z,{className:"h-4 w-4"}),"Create"]})]})]}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)("h4",{className:"text-sm font-medium",children:"Join Existing Session"}),(0,n.jsxs)("div",{className:"flex gap-2",children:[(0,n.jsx)(y.I,{placeholder:"Enter session ID",value:c,onChange:e=>u(e.target.value)}),(0,n.jsxs)(i.z,{onClick:v,disabled:!c.trim(),variant:"outline",className:"flex items-center gap-2",children:[(0,n.jsx)(K.Z,{className:"h-4 w-4"}),"Join"]})]})]})]})]}),g.isConnected&&!g.isAuthenticated&&(0,n.jsx)(r.Zb,{children:(0,n.jsx)(r.aY,{className:"pt-6",children:(0,n.jsx)("div",{className:"text-center",children:(0,n.jsxs)(i.z,{onClick:g.authenticate,className:"flex items-center gap-2",children:[(0,n.jsx)(K.Z,{className:"h-4 w-4"}),"Connect to Collaboration"]})})})})]})}var ei=t(69102),el=t.n(ei);function ec(e){var s;let{editorRef:t,onCursorUpdate:r}=e,i=ea(),[l,c]=(0,a.useState)([]);return(0,a.useEffect)(()=>{let e=[];i.cursors.forEach((s,t)=>{var n;t!==(null===(n=i.currentUser)||void 0===n?void 0:n.id)&&e.push({id:t,user:s.user,position:{line:s.line,column:s.column,selection:s.selection}})}),c(e)},[i.cursors,null===(s=i.currentUser)||void 0===s?void 0:s.id]),(0,a.useEffect)(()=>{if(!(null==t?void 0:t.current))return;let e=t.current,s=()=>{var s;let t=e.selectionStart,n=e.selectionEnd,a=e.value.substring(0,t).split("\n"),l=a.length-1,c=(null===(s=a[a.length-1])||void 0===s?void 0:s.length)||0;i.canCollaborate&&i.updateCursorPosition({line:l,column:c,selection:t!==n?{startLine:l,startColumn:c,endLine:l,endColumn:c+(n-t)}:void 0}),null==r||r(l,c)};return e.addEventListener("selectionchange",s),e.addEventListener("keyup",s),e.addEventListener("mouseup",s),()=>{e.removeEventListener("selectionchange",s),e.removeEventListener("keyup",s),e.removeEventListener("mouseup",s)}},[t,i,r]),(0,n.jsxs)("div",{className:"relative",children:[(null==t?void 0:t.current)&&0!==l.length?l.map(e=>(0,n.jsx)(eo,{cursor:e,editorRef:t},e.id)):null,i.canCollaborate&&i.otherParticipants.length>0&&(0,n.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,n.jsx)("span",{className:"text-xs text-muted-foreground",children:"Active collaborators:"}),i.otherParticipants.map(e=>(0,n.jsxs)(o.C,{variant:"outline",className:"text-xs flex items-center gap-1",style:{borderColor:e.color},children:[(0,n.jsx)("div",{className:"w-2 h-2 rounded-full",style:{backgroundColor:e.color}}),e.name]},e.id))]})]})}function eo(e){let{cursor:s,editorRef:t}=e,[r,i]=(0,a.useState)(null);return((0,a.useEffect)(()=>{var e,n;if(!(null==t?void 0:t.current))return;let a=t.current,r=a.value,l=r.split("\n"),c=0;for(let e=0;e<s.position.line&&e<l.length;e++)c+=((null===(n=l[e])||void 0===n?void 0:n.length)||0)+1;c+=Math.min(s.position.column,(null===(e=l[s.position.line])||void 0===e?void 0:e.length)||0);let o=document.createElement("textarea");o.style.position="absolute",o.style.visibility="hidden",o.style.whiteSpace="pre",o.style.font=window.getComputedStyle(a).font,o.style.fontSize=window.getComputedStyle(a).fontSize,o.style.fontFamily=window.getComputedStyle(a).fontFamily,o.style.lineHeight=window.getComputedStyle(a).lineHeight,o.style.padding=window.getComputedStyle(a).padding,o.style.border=window.getComputedStyle(a).border,o.style.width=a.offsetWidth+"px",o.value=r.substring(0,c),document.body.appendChild(o);let d=a.scrollTop,u=a.scrollLeft,m=parseInt(window.getComputedStyle(a).lineHeight)||20,h=a.getBoundingClientRect();i({top:h.top+s.position.line*m-d,left:h.left+8*s.position.column-u}),document.body.removeChild(o)},[s.position,t]),r)?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{style:{top:r.top,left:r.left,backgroundColor:s.user.color,animation:"blink 1s infinite"},className:"jsx-a58dbfef7be8da39 absolute w-0.5 h-5 z-10 pointer-events-none"}),(0,n.jsx)("div",{style:{top:r.top-25,left:r.left,transform:"translateX(-50%)"},className:"jsx-a58dbfef7be8da39 absolute z-20 pointer-events-none",children:(0,n.jsx)(o.C,{className:"text-xs px-2 py-1 text-white",style:{backgroundColor:s.user.color},children:s.user.name})}),s.position.selection&&(0,n.jsx)("div",{style:{top:r.top,left:r.left,width:(s.position.selection.endColumn-s.position.selection.startColumn)*8,height:20,backgroundColor:s.user.color,opacity:.2},className:"jsx-a58dbfef7be8da39 absolute pointer-events-none z-5"}),(0,n.jsx)(el(),{id:"a58dbfef7be8da39",children:"@-webkit-keyframes blink{0%,50%{opacity:1}51%,100%{opacity:0}}@-moz-keyframes blink{0%,50%{opacity:1}51%,100%{opacity:0}}@-o-keyframes blink{0%,50%{opacity:1}51%,100%{opacity:0}}@keyframes blink{0%,50%{opacity:1}51%,100%{opacity:0}}"})]}):null}var ed=t(32302),eu=t(93966),em=t(67972),eh=t(32176),ex=t(76020);function ej(e){let{className:s,maxHeight:t="400px"}=e,[l,c]=(0,a.useState)(""),[d,u]=(0,a.useState)(!1),m=(0,a.useRef)(null),h=ea();(0,a.useEffect)(()=>{var e;null===(e=m.current)||void 0===e||e.scrollIntoView({behavior:"smooth"})},[h.chatMessages]);let x=()=>{l.trim()&&h.canCollaborate&&(h.sendChatMessage(l.trim()),c(""))},j=e=>new Date(e).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"}),p=e=>{switch(e){case"system":return(0,n.jsx)(eu.Z,{className:"h-3 w-3"});case"query_execution":return(0,n.jsx)(f.Z,{className:"h-3 w-3"});default:return(0,n.jsx)(em.Z,{className:"h-3 w-3"})}},g=e=>{var s;let t=e.userId===(null===(s=h.currentUser)||void 0===s?void 0:s.id);return"system"===e.type||"query_execution"===e.type?{container:"bg-muted/50 border-l-2 border-muted-foreground/20",text:"text-muted-foreground text-sm italic"}:t?{container:"bg-primary/10 border-l-2 border-primary/30 ml-4",text:"text-foreground"}:{container:"bg-background border-l-2 border-muted/30",text:"text-foreground"}};return h.canCollaborate?(0,n.jsxs)(r.Zb,{className:s,children:[(0,n.jsx)(r.Ol,{className:"cursor-pointer",onClick:()=>u(!d),children:(0,n.jsxs)(r.ll,{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)(eh.Z,{className:"h-5 w-5"}),"Chat",h.chatMessages.length>0&&(0,n.jsx)(o.C,{variant:"secondary",className:"text-xs",children:h.chatMessages.length})]}),(0,n.jsx)("div",{className:"flex items-center gap-2",children:h.otherParticipants.length>0&&(0,n.jsxs)(o.C,{variant:"outline",className:"text-xs",children:[h.participantCount," online"]})})]})}),d&&(0,n.jsxs)(r.aY,{className:"space-y-4",children:[(0,n.jsx)(ed.x,{style:{height:t},children:(0,n.jsxs)("div",{className:"space-y-3 pr-4",children:[0===h.chatMessages.length?(0,n.jsxs)("div",{className:"text-center text-muted-foreground text-sm py-8",children:[(0,n.jsx)(eh.Z,{className:"h-8 w-8 mx-auto mb-2 opacity-50"}),(0,n.jsx)("p",{children:"No messages yet. Start the conversation!"})]}):h.chatMessages.map(e=>{var s;let t=g(e),a=e.userId===(null===(s=h.currentUser)||void 0===s?void 0:s.id);return(0,n.jsx)("div",{className:"p-3 rounded-lg ".concat(t.container),children:(0,n.jsxs)("div",{className:"flex items-start gap-2",children:[(0,n.jsx)("div",{className:"flex-shrink-0 mt-0.5",children:p(e.type)}),(0,n.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,n.jsx)("span",{className:"text-sm font-medium",children:a?"You":e.userName}),(0,n.jsx)("span",{className:"text-xs text-muted-foreground",children:j(e.timestamp)}),"message"!==e.type&&(0,n.jsx)(o.C,{variant:"outline",className:"text-xs",children:"system"===e.type?"System":"Query"})]}),(0,n.jsx)("p",{className:"text-sm ".concat(t.text," break-words"),children:e.message})]})]})},e.id)}),(0,n.jsx)("div",{ref:m})]})}),(0,n.jsxs)("div",{className:"flex gap-2",children:[(0,n.jsx)(y.I,{value:l,onChange:e=>c(e.target.value),onKeyPress:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),x())},placeholder:"Type a message...",className:"flex-1",maxLength:500}),(0,n.jsx)(i.z,{onClick:x,disabled:!l.trim(),size:"sm",className:"flex items-center gap-2",children:(0,n.jsx)(ex.Z,{className:"h-4 w-4"})})]})]})]}):(0,n.jsx)(r.Zb,{className:s,children:(0,n.jsxs)(r.aY,{className:"p-6 text-center",children:[(0,n.jsx)(eh.Z,{className:"h-8 w-8 text-muted-foreground mx-auto mb-2"}),(0,n.jsx)("p",{className:"text-muted-foreground text-sm",children:"Join a collaboration session to start chatting"})]})})}var ep=t(26104),ef=t(29409);function eg(){var e,s;let[t,d]=(0,a.useState)("-- Welcome to QueryCraft Studio Query Editor\n-- Try executing this sample query:\n\nSELECT\n  customer_id,\n  name,\n  email,\n  COUNT(order_id) as total_orders,\n  SUM(order_total) as total_spent\nFROM customers c\nLEFT JOIN orders o ON c.customer_id = o.customer_id\nGROUP BY customer_id, name, email\nORDER BY total_spent DESC\nLIMIT 10;"),[u,m]=(0,a.useState)(null),[h,x]=(0,a.useState)("editor"),[p,g]=(0,a.useState)(null),[N]=(0,a.useState)(a.createRef()),y=ea({connectionId:u||void 0,autoConnect:!0}),w=[{id:"conn1",name:"Production DB (PostgreSQL)",type:"POSTGRESQL"},{id:"conn2",name:"Analytics DB (MySQL)",type:"MYSQL"},{id:"conn3",name:"Development DB (PostgreSQL)",type:"POSTGRESQL"}],b=e=>{d(e),y.canCollaborate&&e!==y.sqlContent&&y.updateSqlContent(e)};a.useEffect(()=>{y.sqlContent&&y.sqlContent!==t&&d(y.sqlContent)},[y.sqlContent]);let C=[{id:"editor",label:"Query Editor",icon:S.Z},{id:"results",label:"Results",icon:f.Z,disabled:!p},{id:"schema",label:"Schema",icon:H.Z,disabled:!u},{id:"test",label:"Test Connection",icon:ep.Z},{id:"collaborate",label:"Collaborate",icon:K.Z,badge:y.participantCount>1?y.participantCount:void 0}];return(0,n.jsxs)("div",{className:"container mx-auto p-6 space-y-6",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h1",{className:"text-3xl font-bold",children:"Query Editor"}),(0,n.jsx)("p",{className:"text-muted-foreground",children:"Execute SQL queries against your connected databases"})]}),(0,n.jsx)("div",{className:"flex items-center gap-4",children:(0,n.jsxs)(c.Ph,{value:u||"",onValueChange:m,children:[(0,n.jsx)(c.i4,{className:"w-64",children:(0,n.jsx)(c.ki,{placeholder:"Select a database connection"})}),(0,n.jsx)(c.Bw,{children:w.map(e=>(0,n.jsx)(c.Ql,{value:e.id,children:(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)(j.Z,{className:"h-4 w-4"}),(0,n.jsx)("span",{children:e.name}),(0,n.jsx)(o.C,{variant:"outline",className:"text-xs",children:e.type})]})},e.id))})]})})]}),u&&(0,n.jsx)(r.Zb,{children:(0,n.jsx)(r.aY,{className:"pt-6",children:(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)(j.Z,{className:"h-4 w-4 text-green-500"}),(0,n.jsxs)("span",{className:"text-sm",children:["Connected to: ",null===(e=w.find(e=>e.id===u))||void 0===e?void 0:e.name]}),(0,n.jsx)(o.C,{variant:"default",className:"text-xs",children:null===(s=w.find(e=>e.id===u))||void 0===s?void 0:s.type})]})})}),(0,n.jsx)("div",{className:"flex space-x-1 bg-muted p-1 rounded-lg",children:C.map(e=>{let s=e.icon;return(0,n.jsxs)(i.z,{variant:h===e.id?"default":"ghost",size:"sm",onClick:()=>x(e.id),disabled:e.disabled,className:"flex items-center gap-2",children:[(0,n.jsx)(s,{className:"h-4 w-4"}),e.label,e.badge&&(0,n.jsx)(o.C,{variant:"secondary",className:"text-xs",children:e.badge})]},e.id)})}),(0,n.jsxs)("div",{className:"space-y-6",children:["editor"===h&&(0,n.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,n.jsxs)("div",{className:"lg:col-span-2",children:[(0,n.jsxs)(r.Zb,{children:[(0,n.jsx)(r.Ol,{children:(0,n.jsxs)(r.ll,{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)(S.Z,{className:"h-5 w-5"}),"SQL Editor",y.canCollaborate&&(0,n.jsx)(o.C,{variant:"outline",className:"text-xs",children:"Live"})]}),y.participantCount>1&&(0,n.jsxs)(o.C,{variant:"secondary",className:"text-xs",children:[y.participantCount," collaborators"]})]})}),(0,n.jsxs)(r.aY,{className:"relative",children:[(0,n.jsx)(ec,{editorRef:N,onCursorUpdate:(e,s)=>{}}),(0,n.jsx)(l.g,{ref:N,value:t,onChange:e=>b(e.target.value),placeholder:"Enter your SQL query here...",className:"min-h-[400px] font-mono text-sm"})]})]}),(0,n.jsx)("div",{className:"mt-6",children:(0,n.jsx)(v,{sql:t,connectionId:u,onExecutionComplete:e=>{g(e),e.success&&x("results"),y.canCollaborate&&y.notifyQueryExecutionComplete(e)}})})]}),(0,n.jsx)("div",{className:"space-y-6",children:(0,n.jsx)(ej,{maxHeight:"300px"})})]}),"results"===h&&p&&(0,n.jsx)("div",{children:p.success&&p.data?(0,n.jsx)(I,{data:p.data,metadata:p.metadata}):(0,n.jsx)(r.Zb,{children:(0,n.jsx)(r.aY,{className:"p-8 text-center",children:(0,n.jsx)("div",{className:"text-muted-foreground",children:p.error?(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"font-medium text-red-600 mb-2",children:"Query Failed"}),(0,n.jsx)("p",{className:"text-sm",children:p.error})]}):(0,n.jsx)("p",{children:"No results to display"})})})})}),"schema"===h&&u&&(0,n.jsx)(X,{connectionId:u}),"test"===h&&(0,n.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[u&&(0,n.jsx)(D,{connectionId:u,onConnectionTested:e=>{console.log("Connection test result:",e)}}),(0,n.jsx)(D,{onConnectionTested:e=>{console.log("New connection test result:",e)}})]}),"collaborate"===h&&(0,n.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,n.jsx)("div",{className:"space-y-6",children:(0,n.jsx)(er,{connectionId:u||"",onSessionJoined:e=>{console.log("Joined session:",e)},onSessionLeft:()=>{console.log("Left session")}})}),(0,n.jsx)("div",{children:(0,n.jsx)(ej,{maxHeight:"500px"})})]})]}),(0,n.jsxs)(r.Zb,{children:[(0,n.jsx)(r.Ol,{children:(0,n.jsxs)(r.ll,{className:"flex items-center gap-2",children:[(0,n.jsx)(ef.Z,{className:"h-5 w-5"}),"Quick Actions"]})}),(0,n.jsx)(r.aY,{children:(0,n.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,n.jsxs)(i.z,{variant:"outline",onClick:()=>d("SELECT * FROM users LIMIT 10;"),className:"h-auto p-4 flex flex-col items-center gap-2",children:[(0,n.jsx)(j.Z,{className:"h-6 w-6"}),(0,n.jsx)("span",{className:"text-sm",children:"Sample Query"})]}),(0,n.jsxs)(i.z,{variant:"outline",onClick:()=>d(""),className:"h-auto p-4 flex flex-col items-center gap-2",children:[(0,n.jsx)(S.Z,{className:"h-6 w-6"}),(0,n.jsx)("span",{className:"text-sm",children:"Clear Editor"})]}),(0,n.jsxs)(i.z,{variant:"outline",onClick:()=>x("schema"),disabled:!u,className:"h-auto p-4 flex flex-col items-center gap-2",children:[(0,n.jsx)(H.Z,{className:"h-6 w-6"}),(0,n.jsx)("span",{className:"text-sm",children:"View Schema"})]}),(0,n.jsxs)(i.z,{variant:"outline",onClick:()=>x("test"),className:"h-auto p-4 flex flex-col items-center gap-2",children:[(0,n.jsx)(ep.Z,{className:"h-6 w-6"}),(0,n.jsx)("span",{className:"text-sm",children:"Test Connection"})]})]})})]})]})}},29069:function(e,s,t){"use strict";t.d(s,{X:function(){return o},bZ:function(){return c}});var n=t(57437),a=t(2265),r=t(96061),i=t(22169);let l=(0,r.j)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),c=a.forwardRef((e,s)=>{let{className:t,variant:a,...r}=e;return(0,n.jsx)("div",{ref:s,role:"alert",className:(0,i.cn)(l({variant:a}),t),...r})});c.displayName="Alert",a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,n.jsx)("h5",{ref:s,className:(0,i.cn)("mb-1 font-medium leading-none tracking-tight",t),...a})}).displayName="AlertTitle";let o=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,n.jsx)("div",{ref:s,className:(0,i.cn)("text-sm [&_p]:leading-relaxed",t),...a})});o.displayName="AlertDescription"},90136:function(e,s,t){"use strict";t.d(s,{Fw:function(){return i},wy:function(){return r},zF:function(){return a}});var n=t(91927);let a=n.fC,r=n.wy,i=n.Fw},12647:function(e,s,t){"use strict";t.d(s,{_:function(){return o}});var n=t(57437),a=t(2265),r=t(36743),i=t(96061),l=t(22169);let c=(0,i.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),o=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,n.jsx)(r.f,{ref:s,className:(0,l.cn)(c(),t),...a})});o.displayName=r.f.displayName},32302:function(e,s,t){"use strict";t.d(s,{x:function(){return l}});var n=t(57437),a=t(2265),r=t(25331),i=t(22169);let l=a.forwardRef((e,s)=>{let{className:t,children:a,...l}=e;return(0,n.jsxs)(r.fC,{ref:s,className:(0,i.cn)("relative overflow-hidden",t),...l,children:[(0,n.jsx)(r.l_,{className:"h-full w-full rounded-[inherit]",children:a}),(0,n.jsx)(c,{}),(0,n.jsx)(r.Ns,{})]})});l.displayName=r.fC.displayName;let c=a.forwardRef((e,s)=>{let{className:t,orientation:a="vertical",...l}=e;return(0,n.jsx)(r.gb,{ref:s,orientation:a,className:(0,i.cn)("flex touch-none select-none transition-colors","vertical"===a&&"h-full w-2.5 border-l border-l-transparent p-[1px]","horizontal"===a&&"h-2.5 flex-col border-t border-t-transparent p-[1px]",t),...l,children:(0,n.jsx)(r.q4,{className:"relative flex-1 rounded-full bg-border"})})});c.displayName=r.gb.displayName},3549:function(e,s,t){"use strict";t.d(s,{g:function(){return i}});var n=t(57437),a=t(2265),r=t(22169);let i=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,n.jsx)("textarea",{className:(0,r.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:s,...a})});i.displayName="Textarea"}},function(e){e.O(0,[432,567,674,742,341,45,850,971,938,744],function(){return e(e.s=46836)}),_N_E=e.O()}]);