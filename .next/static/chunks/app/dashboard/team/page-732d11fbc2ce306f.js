(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[558],{13008:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(62898).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},99670:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(62898).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},1295:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(62898).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},76020:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(62898).Z)("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]])},49036:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(62898).Z)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])},74527:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(62898).Z)("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},82104:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(62898).Z)("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},17626:function(e,s,t){Promise.resolve().then(t.bind(t,35779)),Promise.resolve().then(t.bind(t,81121))},81121:function(e,s,t){"use strict";t.r(s),t.d(s,{TeamPage:function(){return D}});var a=t(57437),l=t(2265),r=t(15671),n=t(575),c=t(22782),i=t(12647),d=t(33277),x=t(75808),m=t(62898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let h=(0,m.Z)("Crown",[["path",{d:"m2 4 3 12h14l3-12-6 7-4-7-4 7-6-7zm3 16h14",key:"zkxr6b"}]]);var o=t(49036),j=t(49617),u=t(99670),N=t(74527),p=t(76020),g=t(25750),v=t(13008),w=t(6141);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let y=(0,m.Z)("MoreVertical",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}]]);var b=t(1295),f=t(4737),Z=t(82104),k=t(57108),M=t(34645),C=t(18641),A=t(30034);function D(){let[e,s]=(0,l.useState)(!1),[t,m]=(0,l.useState)({email:"",role:"member"}),D=[{id:"1",name:"John Doe",email:"<EMAIL>",avatar:"",role:"owner",status:"active",joinedAt:new Date("2024-01-15"),lastActive:new Date},{id:"2",name:"Jane Smith",email:"<EMAIL>",avatar:"",role:"admin",status:"active",joinedAt:new Date("2024-02-01"),lastActive:new Date(Date.now()-72e5)},{id:"3",name:"Mike Johnson",email:"<EMAIL>",avatar:"",role:"member",status:"active",joinedAt:new Date("2024-02-15"),lastActive:new Date(Date.now()-864e5)},{id:"4",name:"Sarah Wilson",email:"<EMAIL>",avatar:"",role:"viewer",status:"inactive",joinedAt:new Date("2024-03-01"),lastActive:new Date(Date.now()-6048e5)}],z=[{id:"1",email:"<EMAIL>",role:"member",invitedAt:new Date(Date.now()-1728e5),invitedBy:"John Doe"},{id:"2",email:"<EMAIL>",role:"viewer",invitedAt:new Date(Date.now()-432e6),invitedBy:"Jane Smith"}],q=e=>{switch(e){case"owner":return(0,a.jsx)(h,{className:"h-4 w-4 text-yellow-600"});case"admin":return(0,a.jsx)(o.Z,{className:"h-4 w-4 text-blue-600"});case"member":return(0,a.jsx)(j.Z,{className:"h-4 w-4 text-green-600"});case"viewer":return(0,a.jsx)(u.Z,{className:"h-4 w-4 text-gray-600"});default:return null}},S=e=>{switch(e){case"owner":return"bg-yellow-100 text-yellow-800 border-yellow-200";case"admin":return"bg-blue-100 text-blue-800 border-blue-200";case"member":return"bg-green-100 text-green-800 border-green-200";default:return"bg-gray-100 text-gray-800 border-gray-200"}},_=e=>{switch(e){case"active":return"bg-green-100 text-green-800 border-green-200";case"pending":return"bg-yellow-100 text-yellow-800 border-yellow-200";default:return"bg-gray-100 text-gray-800 border-gray-200"}},I=e=>e.split(" ").map(e=>e[0]).join("").toUpperCase();return(0,a.jsxs)("div",{className:"p-6 space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-foreground",children:"Team Management"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Manage your team members and their permissions"})]}),(0,a.jsxs)(k.Vq,{open:e,onOpenChange:s,children:[(0,a.jsx)(k.hg,{asChild:!0,children:(0,a.jsxs)(n.z,{children:[(0,a.jsx)(N.Z,{className:"h-4 w-4 mr-2"}),"Invite Member"]})}),(0,a.jsxs)(k.cZ,{children:[(0,a.jsxs)(k.fK,{children:[(0,a.jsx)(k.$N,{children:"Invite Team Member"}),(0,a.jsx)(k.Be,{children:"Send an invitation to join your team workspace"})]}),(0,a.jsxs)("form",{onSubmit:e=>{e.preventDefault(),console.log("Inviting user:",t),m({email:"",role:"member"}),s(!1)},className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(i._,{htmlFor:"email",children:"Email Address"}),(0,a.jsx)(c.I,{id:"email",type:"email",value:t.email,onChange:e=>m({...t,email:e.target.value}),placeholder:"<EMAIL>",required:!0})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(i._,{htmlFor:"role",children:"Role"}),(0,a.jsxs)(C.Ph,{value:t.role,onValueChange:e=>m({...t,role:e}),children:[(0,a.jsx)(C.i4,{children:(0,a.jsx)(C.ki,{})}),(0,a.jsxs)(C.Bw,{children:[(0,a.jsx)(C.Ql,{value:"viewer",children:"Viewer - Can view queries and results"}),(0,a.jsx)(C.Ql,{value:"member",children:"Member - Can create and edit queries"}),(0,a.jsx)(C.Ql,{value:"admin",children:"Admin - Full access except billing"})]})]})]}),(0,a.jsxs)(k.cN,{children:[(0,a.jsx)(n.z,{type:"button",variant:"outline",onClick:()=>s(!1),children:"Cancel"}),(0,a.jsxs)(n.z,{type:"submit",children:[(0,a.jsx)(p.Z,{className:"h-4 w-4 mr-2"}),"Send Invitation"]})]})]})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,a.jsx)(r.Zb,{children:(0,a.jsx)(r.aY,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(g.Z,{className:"h-5 w-5 text-primary"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-2xl font-bold",children:D.length}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Total Members"})]})]})})}),(0,a.jsx)(r.Zb,{children:(0,a.jsx)(r.aY,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(v.Z,{className:"h-5 w-5 text-green-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-2xl font-bold",children:D.filter(e=>"active"===e.status).length}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Active"})]})]})})}),(0,a.jsx)(r.Zb,{children:(0,a.jsx)(r.aY,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(w.Z,{className:"h-5 w-5 text-yellow-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-2xl font-bold",children:z.length}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Pending"})]})]})})}),(0,a.jsx)(r.Zb,{children:(0,a.jsx)(r.aY,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(o.Z,{className:"h-5 w-5 text-blue-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-2xl font-bold",children:D.filter(e=>"admin"===e.role||"owner"===e.role).length}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Admins"})]})]})})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,a.jsxs)(r.Zb,{className:"lg:col-span-2",children:[(0,a.jsx)(r.Ol,{children:(0,a.jsxs)(r.ll,{className:"flex items-center gap-2",children:[(0,a.jsx)(g.Z,{className:"h-5 w-5"}),"Team Members"]})}),(0,a.jsx)(r.aY,{children:(0,a.jsx)("div",{className:"space-y-4",children:D.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsxs)(x.qE,{children:[(0,a.jsx)(x.F$,{src:e.avatar,alt:e.name}),(0,a.jsx)(x.Q5,{children:I(e.name)})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("h4",{className:"font-medium",children:e.name}),q(e.role)]}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:e.email}),(0,a.jsxs)("div",{className:"flex items-center gap-2 mt-1",children:[(0,a.jsx)(d.C,{className:S(e.role),children:e.role}),(0,a.jsx)(d.C,{className:_(e.status),children:e.status})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)("div",{className:"text-right text-sm text-muted-foreground",children:[(0,a.jsxs)("p",{children:["Joined ",(0,A.WU)(e.joinedAt,"MMM dd, yyyy")]}),(0,a.jsxs)("p",{children:["Last active ",(0,A.WU)(e.lastActive,"MMM dd, HH:mm")]})]}),(0,a.jsxs)(M.h_,{children:[(0,a.jsx)(M.$F,{asChild:!0,children:(0,a.jsx)(n.z,{variant:"ghost",size:"icon",children:(0,a.jsx)(y,{className:"h-4 w-4"})})}),(0,a.jsxs)(M.AW,{align:"end",children:[(0,a.jsxs)(M.Xi,{children:[(0,a.jsx)(j.Z,{className:"h-4 w-4 mr-2"}),"Edit Role"]}),(0,a.jsxs)(M.Xi,{children:[(0,a.jsx)(b.Z,{className:"h-4 w-4 mr-2"}),"Resend Invite"]}),(0,a.jsxs)(M.Xi,{className:"text-red-600",children:[(0,a.jsx)(f.Z,{className:"h-4 w-4 mr-2"}),"Remove"]})]})]})]})]},e.id))})})]}),(0,a.jsxs)(r.Zb,{children:[(0,a.jsx)(r.Ol,{children:(0,a.jsxs)(r.ll,{className:"flex items-center gap-2",children:[(0,a.jsx)(w.Z,{className:"h-5 w-5"}),"Pending Invites"]})}),(0,a.jsx)(r.aY,{children:(0,a.jsx)("div",{className:"space-y-4",children:0===z.length?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(b.Z,{className:"h-8 w-8 text-muted-foreground mx-auto mb-2"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"No pending invites"})]}):z.map(e=>(0,a.jsx)("div",{className:"p-3 border rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"font-medium text-sm",children:e.email}),(0,a.jsx)(d.C,{className:S(e.role),size:"sm",children:e.role}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground mt-1",children:["Invited ",(0,A.WU)(e.invitedAt,"MMM dd")," by ",e.invitedBy]})]}),(0,a.jsxs)(M.h_,{children:[(0,a.jsx)(M.$F,{asChild:!0,children:(0,a.jsx)(n.z,{variant:"ghost",size:"icon",className:"h-6 w-6",children:(0,a.jsx)(y,{className:"h-3 w-3"})})}),(0,a.jsxs)(M.AW,{align:"end",children:[(0,a.jsxs)(M.Xi,{children:[(0,a.jsx)(b.Z,{className:"h-3 w-3 mr-2"}),"Resend"]}),(0,a.jsxs)(M.Xi,{className:"text-red-600",children:[(0,a.jsx)(Z.Z,{className:"h-3 w-3 mr-2"}),"Cancel"]})]})]})]})},e.id))})})]})]}),(0,a.jsxs)(r.Zb,{children:[(0,a.jsx)(r.Ol,{children:(0,a.jsx)(r.ll,{children:"Role Permissions"})}),(0,a.jsx)(r.aY,{children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full",children:[(0,a.jsx)("thead",{children:(0,a.jsxs)("tr",{className:"border-b",children:[(0,a.jsx)("th",{className:"text-left p-2",children:"Permission"}),(0,a.jsx)("th",{className:"text-center p-2",children:"Viewer"}),(0,a.jsx)("th",{className:"text-center p-2",children:"Member"}),(0,a.jsx)("th",{className:"text-center p-2",children:"Admin"}),(0,a.jsx)("th",{className:"text-center p-2",children:"Owner"})]})}),(0,a.jsxs)("tbody",{className:"text-sm",children:[(0,a.jsxs)("tr",{className:"border-b",children:[(0,a.jsx)("td",{className:"p-2",children:"View queries and results"}),(0,a.jsx)("td",{className:"text-center p-2",children:(0,a.jsx)(v.Z,{className:"h-4 w-4 text-green-600 mx-auto"})}),(0,a.jsx)("td",{className:"text-center p-2",children:(0,a.jsx)(v.Z,{className:"h-4 w-4 text-green-600 mx-auto"})}),(0,a.jsx)("td",{className:"text-center p-2",children:(0,a.jsx)(v.Z,{className:"h-4 w-4 text-green-600 mx-auto"})}),(0,a.jsx)("td",{className:"text-center p-2",children:(0,a.jsx)(v.Z,{className:"h-4 w-4 text-green-600 mx-auto"})})]}),(0,a.jsxs)("tr",{className:"border-b",children:[(0,a.jsx)("td",{className:"p-2",children:"Create and edit queries"}),(0,a.jsx)("td",{className:"text-center p-2",children:(0,a.jsx)(Z.Z,{className:"h-4 w-4 text-red-600 mx-auto"})}),(0,a.jsx)("td",{className:"text-center p-2",children:(0,a.jsx)(v.Z,{className:"h-4 w-4 text-green-600 mx-auto"})}),(0,a.jsx)("td",{className:"text-center p-2",children:(0,a.jsx)(v.Z,{className:"h-4 w-4 text-green-600 mx-auto"})}),(0,a.jsx)("td",{className:"text-center p-2",children:(0,a.jsx)(v.Z,{className:"h-4 w-4 text-green-600 mx-auto"})})]}),(0,a.jsxs)("tr",{className:"border-b",children:[(0,a.jsx)("td",{className:"p-2",children:"Manage database connections"}),(0,a.jsx)("td",{className:"text-center p-2",children:(0,a.jsx)(Z.Z,{className:"h-4 w-4 text-red-600 mx-auto"})}),(0,a.jsx)("td",{className:"text-center p-2",children:(0,a.jsx)(v.Z,{className:"h-4 w-4 text-green-600 mx-auto"})}),(0,a.jsx)("td",{className:"text-center p-2",children:(0,a.jsx)(v.Z,{className:"h-4 w-4 text-green-600 mx-auto"})}),(0,a.jsx)("td",{className:"text-center p-2",children:(0,a.jsx)(v.Z,{className:"h-4 w-4 text-green-600 mx-auto"})})]}),(0,a.jsxs)("tr",{className:"border-b",children:[(0,a.jsx)("td",{className:"p-2",children:"Invite team members"}),(0,a.jsx)("td",{className:"text-center p-2",children:(0,a.jsx)(Z.Z,{className:"h-4 w-4 text-red-600 mx-auto"})}),(0,a.jsx)("td",{className:"text-center p-2",children:(0,a.jsx)(Z.Z,{className:"h-4 w-4 text-red-600 mx-auto"})}),(0,a.jsx)("td",{className:"text-center p-2",children:(0,a.jsx)(v.Z,{className:"h-4 w-4 text-green-600 mx-auto"})}),(0,a.jsx)("td",{className:"text-center p-2",children:(0,a.jsx)(v.Z,{className:"h-4 w-4 text-green-600 mx-auto"})})]}),(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"p-2",children:"Manage billing and subscription"}),(0,a.jsx)("td",{className:"text-center p-2",children:(0,a.jsx)(Z.Z,{className:"h-4 w-4 text-red-600 mx-auto"})}),(0,a.jsx)("td",{className:"text-center p-2",children:(0,a.jsx)(Z.Z,{className:"h-4 w-4 text-red-600 mx-auto"})}),(0,a.jsx)("td",{className:"text-center p-2",children:(0,a.jsx)(Z.Z,{className:"h-4 w-4 text-red-600 mx-auto"})}),(0,a.jsx)("td",{className:"text-center p-2",children:(0,a.jsx)(v.Z,{className:"h-4 w-4 text-green-600 mx-auto"})})]})]})]})})})]})]})}}},function(e){e.O(0,[432,749,250,567,674,529,742,63,559,240,971,938,744],function(){return e(e.s=17626)}),_N_E=e.O()}]);