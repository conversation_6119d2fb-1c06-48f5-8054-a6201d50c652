{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}], "regex": "^(?:/(.*))(?:/)?$"}], "dynamicRoutes": [{"page": "/api/auth/[...next<PERSON>h]", "regex": "^/api/auth/(.+?)(?:/)?$", "routeKeys": {"nxtPnextauth": "nxtPnextauth"}, "namedRegex": "^/api/auth/(?<nxtPnextauth>.+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/auth/signin", "regex": "^/auth/signin(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/signin(?:/)?$"}, {"page": "/auth/signup", "regex": "^/auth/signup(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/signup(?:/)?$"}, {"page": "/dashboard", "regex": "^/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard(?:/)?$"}, {"page": "/dashboard/analytics", "regex": "^/dashboard/analytics(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/analytics(?:/)?$"}, {"page": "/dashboard/connections", "regex": "^/dashboard/connections(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/connections(?:/)?$"}, {"page": "/dashboard/docs", "regex": "^/dashboard/docs(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/docs(?:/)?$"}, {"page": "/dashboard/help", "regex": "^/dashboard/help(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/help(?:/)?$"}, {"page": "/dashboard/history", "regex": "^/dashboard/history(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/history(?:/)?$"}, {"page": "/dashboard/profile", "regex": "^/dashboard/profile(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/profile(?:/)?$"}, {"page": "/dashboard/query-editor", "regex": "^/dashboard/query\\-editor(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/query\\-editor(?:/)?$"}, {"page": "/dashboard/settings", "regex": "^/dashboard/settings(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/settings(?:/)?$"}, {"page": "/dashboard/team", "regex": "^/dashboard/team(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/team(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Url", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc"}, "rewrites": [{"source": "/api/:path*", "destination": "/api/:path*", "regex": "^/api(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}]}