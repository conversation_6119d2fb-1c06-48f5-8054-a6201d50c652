import { formatDistance } from "./nb/_lib/formatDistance.js";
import { formatLong } from "./nb/_lib/formatLong.js";
import { formatRelative } from "./nb/_lib/formatRelative.js";
import { localize } from "./nb/_lib/localize.js";
import { match } from "./nb/_lib/match.js";

/**
 * @category Locales
 * @summary Norwegian Bokmål locale.
 * @language Norwegian Bokmål
 * @iso-639-2 nob
 * <AUTHOR> [@Hanse](https://github.com/Hanse)
 * <AUTHOR> [@mikolajgrzyb](https://github.com/mikolajgrzyb)
 * <AUTHOR> [@dagstuan](https://github.com/dagstuan)
 */
export const nb = {
  code: "nb",
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 4,
  },
};

// Fallback for modularized imports:
export default nb;
