import { formatDistance } from "./id/_lib/formatDistance.js";
import { formatLong } from "./id/_lib/formatLong.js";
import { formatRelative } from "./id/_lib/formatRelative.js";
import { localize } from "./id/_lib/localize.js";
import { match } from "./id/_lib/match.js";

/**
 * @category Locales
 * @summary Indonesian locale.
 * @language Indonesian
 * @iso-639-2 ind
 * <AUTHOR> [@rbudiharso](https://github.com/rbudiharso)
 * <AUTHOR> Nata [@bentinata](https://github.com/bentinata)
 * <AUTHOR> [@deerawan](https://github.com/deerawan)
 * <AUTHOR> Ajitiono [@imballinst](https://github.com/imballinst)
 */
export const id = {
  code: "id",
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 1,
  },
};

// Fallback for modularized imports:
export default id;
