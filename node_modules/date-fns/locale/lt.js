import { formatDistance } from "./lt/_lib/formatDistance.js";
import { formatLong } from "./lt/_lib/formatLong.js";
import { formatRelative } from "./lt/_lib/formatRelative.js";
import { localize } from "./lt/_lib/localize.js";
import { match } from "./lt/_lib/match.js";

/**
 * @category Locales
 * @summary Lithuanian locale.
 * @language Lithuanian
 * @iso-639-2 lit
 * <AUTHOR> [@pshpak](https://github.com/pshpak)
 * <AUTHOR> [@eduardopsll](https://github.com/eduardopsll)
 */
export const lt = {
  code: "lt",
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 4,
  },
};

// Fallback for modularized imports:
export default lt;
